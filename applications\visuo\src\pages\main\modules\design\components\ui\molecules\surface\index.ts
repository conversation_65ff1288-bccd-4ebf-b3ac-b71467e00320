import { Color, Effects, FillPaints, FillType, Stroke, useFill } from '@vis/document-core';
import { computed, defineComponent, onMounted, ref, watch, type PropType } from 'vue';

/**
 * 背景+边框+阴影组件
 */
export default defineComponent({
  name: 'vis-surface',
  props: {
    minusWidth: {
      type: Number,
      default: 28
    },
    /**
     * 填充色
     */
    fill: {
      type: Object as PropType<FillPaints | Color>,
      required: true
    },

    /** 是否只显示纯色 */
    onlyColor: {
      type: Boolean,
      default: false
    },
    /**
     * 边框
     */
    stroke: {
      type: Object as PropType<Stroke>
    },
    /**
     * 特效
     */
    effects: {
      type: Object as PropType<Effects>,
      required: true
    },
    isShowEyes: {
      type: Boolean,
      default: true
    },
    isShowTitle: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    const { getFillStyle, savePaletteColor, getRgbaFromString, hexToRgba } = useFill();
    //#region 填充
    const oldColor = ref('');
    const colorRef = ref();
    const colorValue = ref('');
    const computedFill = computed({
      get() {
        if (!props.fill) {
          return new FillPaints();
        }

        if (props.onlyColor) {
          if ((props.fill as FillPaints).color) {
            return props.fill as FillPaints;
          } else {
            const fillPaints = new FillPaints();
            fillPaints.color = props.fill as Color;
            return fillPaints;
          }
        } else {
          return props.fill as FillPaints;
        }
      },

      set(value) {
        Object.assign(props.fill, value);
      }
    });

    const colorType = ref(props.onlyColor ? FillType.Solid : computedFill.value.type);

    const isSolid = computed(() => {
      return colorType.value === FillType.Solid;
    });

    const isGradient = computed(() => {
      return colorType.value.includes('gradient');
    });

    const isImage = computed(() => {
      return colorType.value === FillType.Image;
    });

    const isEmptyImage = computed(() => {
      return isImage.value && !computedFill.value.image?.url;
    });

    /**
     * 计算输入框内的图标样式
     */
    const iconStyle = computed(() => {
      if (!colorType.value) return '';

      const type = props.onlyColor ? { type: FillType.Solid } : {};
      const fillStyle = getFillStyle({ ...computedFill.value, ...type, visible: true, opacity: 1 });

      // 设置图标透明，防止遮挡背景
      fillStyle.color = isEmptyImage.value ? '' : 'transparent';

      return fillStyle;
    });

    // 计算 hex 值
    const hexColor = computed(() => {
      const { r, g, b } = computedFill.value.color;
      // 确保数值在 0-255 范围内
      const clamp = (val: number) => Math.max(0, Math.min(255, val));
      const hex = [r, g, b]
        .map(clamp)
        .map((x) => x.toString(16).padStart(2, '0'))
        .join('');
      return `${hex}`.toUpperCase();
    });

    const focusColor = () => {
      oldColor.value = colorValue.value;
      colorRef.value && colorRef.value.select();
    };

    /**
     * rgb参数合法
     * @param r
     * @param g
     * @param b
     * @returns
     */
    const isValidRgb = (r: number, g: number, b: number) => {
      return (
        Number.isInteger(r) &&
        r >= 0 &&
        r <= 255 &&
        Number.isInteger(g) &&
        g >= 0 &&
        g <= 255 &&
        Number.isInteger(b) &&
        b >= 0 &&
        b <= 255
      );
    };

    /**
     * 改变输入框值时，面板中的颜色值跟随变化
     * @param ev
     * @returns
     */
    const updateColor = (ev?: Event) => {
      const targetValue = (ev?.target as HTMLInputElement)?.value;
      if (targetValue) {
        colorValue.value = targetValue;
      }

      if (!colorValue.value) return;

      // 不是rgb开头则认为是hex模式
      if (!colorValue.value.startsWith('rgb')) {
        colorValue.value = hexToRgba(colorValue.value);
      }

      const { r, g, b, a } = getRgbaFromString(colorValue.value);
      if (!colorValue.value || !isValidRgb(r, g, b)) {
        colorValue.value = oldColor.value;
      } else {
        colorValue.value = `rgb(${r}, ${g}, ${b})`;
        oldColor.value = colorValue.value;
        alpha.value = `${(a * 100).toFixed(0)}%`;

        updateValue();
      }
    };

    const updateValue = () => {
      const { r, g, b } = getRgbaFromString(colorValue.value);
      const a = Number(alpha.value.replace('%', '')) / 100;

      if (props.onlyColor) {
        handleUpdate(Object.assign({}, props.fill, { r, g, b, a }));
      } else {
        handleUpdate(Object.assign({}, props.fill, { color: { r, g, b, a }, opacity: a }));
      }
      emit('change');
    };

    const handleUpdate = (val: FillPaints | Color) => {
      emit('update:fill', val);
    };
    //#endregion 填充

    //#region 透明度
    const alpha = ref('');
    const oldAlpha = ref('');

    const alphaRef = ref();
    const focusAlpha = () => {
      oldAlpha.value = alpha.value;
      alphaRef.value && alphaRef.value.select();
    };

    /**
     * 保持alpha在0-100之间
     */
    const updateAlpha = () => {
      const alphaNum = Number(alpha.value.replace('%', ''));
      if (!alpha.value || Number.isNaN(alphaNum)) {
        alpha.value = oldAlpha.value;
      } else {
        if (alphaNum < 0) {
          alpha.value = '0%';
        } else if (alphaNum > 100) {
          alpha.value = '100%';
        } else {
          alpha.value = `${alphaNum}%`;
        }
        oldAlpha.value = alpha.value;

        updateValue();
      }
    };
    //#endregion 透明度

    //#region 渐变

    const fillTypeName = computed(() => {
      if (!colorType.value) return '';
      switch (colorType.value) {
        case FillType.Linear:
          return '线性渐变';
        case FillType.Radial:
          return '径向渐变';
        case FillType.Angular:
          return '旋转渐变';
        case FillType.Diamond:
          return '菱形渐变';
        case FillType.Image:
          return '图片';
        default:
          return '';
      }
    });

    const visible = ref(computedFill.value.visible);
    const handleVisible = () => {
      visible.value = !visible.value;
      handleUpdate(Object.assign({}, props.fill, { visible: visible.value }));

      emit('toggle', visible.value);
    };
    //#endregion 渐变

    /**
     * 初始化颜色和透明度
     */
    const initColor = () => {
      const { r, g, b, a } = computedFill.value.color;
      colorValue.value = `rgba(${r},${g},${b},${a})`;

      const opacity = isSolid.value ? a : computedFill.value.opacity || 1;
      alpha.value = `${(opacity * 100).toFixed(0)}%`;
    };
    watch(
      () => computedFill.value,
      () => {
        initColor();
      },
      { immediate: true, deep: true }
    );

    //#region 边框
    const positionIndex = ref(4);

    // 描边
    const strokeOptions = [
      { label: '全部', icon: 'border-a', value: 4, active: true },
      { label: '顶部', icon: 'border-t', value: 0, active: false },
      { label: '底部', icon: 'border-b', value: 2, active: false },
      { label: '左侧', icon: 'border-l', value: 3, active: false },
      { label: '右侧', icon: 'border-r', value: 1, active: false }
    ];

    const computedStroke = computed({
      get() {
        if (!props.stroke) {
          return new Stroke();
        }
        return props.stroke;
      },
      set(value) {
        Object.assign({}, props.stroke, value);
      }
    });

    const initStrokeOptions = () => {
      const positions = computedStroke.value.position;
      strokeOptions.forEach((item) => (item.active = false));

      const nonZeroCount = positions.filter((pos) => pos > 0).length;
      const isAllEqual = nonZeroCount === 4 && positions.every((pos) => pos === positions[0]);

      let targetIndex = 4;

      if (nonZeroCount === 1) {
        targetIndex = positions.findIndex((pos) => pos > 0);
      } else if (nonZeroCount > 1 && !isAllEqual) {
        targetIndex = 5;
      }

      const option = strokeOptions.find((item) => item.value === targetIndex);
      if (option) {
        option.active = true;
        strokeIcon.value = option.icon;
      } else if (targetIndex === 5) {
        strokeIcon.value = 'border-a';
      }

      positionIndex.value = targetIndex;
    };

    onMounted(() => {
      initStrokeOptions();
    });

    const strokeIcon = ref('border-a');

    // 修改线宽
    const handlePositionChange = (value: number) => {
      if (positionIndex.value === 4 || positionIndex.value === 5) {
        computedStroke.value.position = [value, value, value, value];
      } else {
        // 将其他元素设为0，只设置当前选中位置的值
        computedStroke.value.position = [0, 0, 0, 0];
        computedStroke.value.position[positionIndex.value] = value;
      }
    };

    const handleStrokeChange = (item: { icon: string; value: number; active: boolean }) => {
      strokeOptions.forEach((item) => (item.active = false));
      item.active = true;
      positionIndex.value = item.value;
      const position = computedStroke.value.position.find((item) => item !== 0);
      handlePositionChange(position || 0);
      strokeIcon.value = item.icon || 'border-a';
    };

    //#endregion 边框

    //#region 弹出框
    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };
    //#endregion 弹出框
    return {
      isEmptyImage,
      iconStyle,
      isSolid,
      isGradient,
      hexColor,
      colorRef,
      focusColor,
      updateColor,

      alpha,
      alphaRef,
      focusAlpha,
      updateAlpha,

      fillTypeName,
      visible,
      handleVisible,

      positionIndex,
      strokeIcon,
      strokeOptions,
      computedStroke,
      handleStrokeChange,

      popupRef,
      popupShow,
      showPopup
    };
  }
});
