import { defineComponent, computed, type PropType } from 'vue';
import { useGraphStyle, useLayout } from '../../hooks';
import { Frame, Graph, GraphType } from '../../models';
import type { Records } from '@hetu/util';

/**
 * 预览/发布入口组件
 */
export default defineComponent({
  name: 'vis-entrance',
  components: {},
  props: {
    graph: {
      type: Object as PropType<Graph>,
      required: true
    },
    parent: {
      type: Object as PropType<Frame>,
      required: false
    }
  },
  setup(props) {
    const {
      isFlex,
      isFreeform,
      isGrid,
      flexItemStyle,
      freeformStyle,
      constraintsStyle,
      gridItemStyle,
      isLayoutRowColumn,
      layoutGuideItemStyle
    } = useLayout();
    const { graphClassName, graphComponentName, graphBaseStyle } = useGraphStyle();

    const graph = computed(() => props.graph);
    const parent = computed(() => props.parent);

    const className = computed(() => {
      return graphClassName(graph.value).join(' ');
    });

    const componentName = computed(() => graphComponentName(graph.value));

    const style = computed(() => {
      const getStyle = (graph: Graph, parent?: Frame) => {
        let positionStyle: Records<string | number> = {};

        // 说明frame是容器下的子元素
        if (parent) {
          // 一、父级是flex布局
          if (isFlex(parent)) {
            if (graph.ignoreAutoLayout) {
              positionStyle = constraintsStyle(graph, parent);
            } else {
              positionStyle = flexItemStyle(graph, parent);
            }
          }
          // 二、父级是grid布局
          else if (isGrid(parent)) {
            if (graph.ignoreAutoLayout && graph.constraints) {
              positionStyle = constraintsStyle(graph, parent);
            } else {
              positionStyle = gridItemStyle(graph, parent);
            }
          }
          // 三、父级是自由布局
          else if (isFreeform(parent)) {
            // 1. 配置了布局网格
            if (isLayoutRowColumn(parent)) {
              // 在栅格内
              if (graph.gridItem) {
                positionStyle = layoutGuideItemStyle(graph, parent);
              }
              // 在栅格的边距或槽内
              else {
                positionStyle = constraintsStyle(graph, parent);
              }
            }
            // 2. 未配置布局网格，但自由布局配置了约束
            else {
              positionStyle = constraintsStyle(graph, parent);
            }
          }
        } else {
          positionStyle = freeformStyle(graph);
        }

        // 基础样式
        const baseStyle = graphBaseStyle(graph);

        return { ...positionStyle, ...baseStyle };
      };
      return getStyle(graph.value, parent.value);
    });

    return {
      style,
      className,
      componentName,
      GraphType
    };
  }
});
