@import '../../../index';

.#{$vis-prefix}-store-page-container {
  &-header {
    @apply flex justify-between lh-7 h-7 pl-1 pr-1;

    &-search {
      @apply flex items-center w-100%;
    }

    &-input {
      @apply w-100% bg-#efefef rounded-4px pl-1 pr-1;
    }
  }

  &-title {
    @apply lh-7 h-7;

    font-size: $primary-font-size;
  }

  &-btn {
    @include component-base;
    @apply p-0 m-0 w-6;

    font-size: $primary-font-size;

    &-group {
      @apply flex items-center;

      :deep(.q-btn) {
        .vis-icon,
        .q-icon {
          @apply text-#666 text-16px;
        }
      }
    }
  }

  &-tree_layout {
    .empty-content {
      @apply h-30px px-0 py-6px flex justify-center items-center text-#00000080;

      font-size: $primary-font-size;
    }
  }

  &-tree_layout.is-dragging {
    :deep(.vis-store-layer),
    :deep(.vis-store-page) {
      .#{$vis-prefix}-store-layer-node__parent,
      .#{$vis-prefix}-store-page-node__parent {
        @apply relative;

        .vis-store-layer-collapsible.folder,
        .vis-store-page-collapsible.folder {
          @apply absolute bottom-1.25 h-5 w-full overflow-hidden op-0;

          .vis-store-layer,
          .vis-store-page {
            @apply pt-5;
          }
        }
      }

      .#{$vis-prefix}-store-layer-node__child {
        @apply relative;

        .vis-store-layer {
          @apply absolute bottom-1.25 min-h-5 w-[calc(100%-18px)] op-0;
        }
      }

      .#{$vis-prefix}-store-layer-node.selected,
      .#{$vis-prefix}-store-page-node.selected {
        @apply h-0 overflow-hidden border-b-1px border-b-solid border-b-[#000];
      }

      .#{$vis-prefix}-store-layer-header:hover,
      .#{$vis-prefix}-store-layer-node__child:hover,
      .#{$vis-prefix}-store-page-header:hover,
      .#{$vis-prefix}-store-page-node__child:hover {
        background-color: transparent;
      }
    }
  }

  &-separator {
    position: relative;
    transition: background-color 0.2s ease;
    cursor: ns-resize;

    // 使用伪元素扩展触发区域
    &::before {
      content: '';
      position: absolute;
      z-index: 1;
      inset: -10px 0;
    }

    &:hover {
      background-color: rgb(25 118 210 / 10%);
    }

    &.is-dragging {
      background-color: rgb(25 118 210 / 20%);
    }
  }
}
