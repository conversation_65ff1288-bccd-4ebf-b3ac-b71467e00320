<template>
  <vis-popup :title="activeBlock.name" ref="popupRef" class="vis-config-quick-style" :target="false">
    <q-card flat>
      <q-card-section class="row q-col-gutter-sm no-padding">
        <div class="col-4">
          <div>
            <div class="vis-config-quick-style-item">
              <q-img :src="`./static-next/img/tag.gif`"> </q-img>
            </div>
            <div class="vis-config-quick-style-item__label">选项卡</div>
          </div>
        </div>
        <div class="col-4">
          <div>
            <div class="vis-config-quick-style-item">
              <q-img :src="`./static-next/img/tag.gif`"> </q-img>
            </div>
            <div class="vis-config-quick-style-item__label">标签页</div>
          </div>
        </div>
      </q-card-section>
    </q-card>
    <q-separator />
    <q-card flat>
      <q-card-section class="card-text q-px-none q-py-xs">
        <div class="vis-config-quick-style-title">样式</div>
      </q-card-section>
      <q-card-section class="row q-col-gutter-sm no-padding">
        <div v-for="(item, index) in styleOptions" :key="item.id" class="col-6">
          <div>
            <div class="vis-config-quick-style-item" @click="selectItemStyle(item)">
              <q-img :src="`./static-next/img/tab-${index}.${index === 0 ? 'png' : 'gif'}`"> </q-img>
            </div>
            <div class="vis-config-quick-style-item__label">{{ item.title }}</div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </vis-popup>
</template>

<script lang="ts" src="./quick-style.ts"></script>
<style lang="scss" src="./quick-style.scss"></style>
