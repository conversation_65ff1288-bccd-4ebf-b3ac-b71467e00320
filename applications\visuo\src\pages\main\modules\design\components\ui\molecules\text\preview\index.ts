import { Text, TextAlign, TextAdapt, VerticalAlign, DirectionType } from '@vis/document-core';
import { computed, defineComponent, type PropType } from 'vue';

/**
 * 预览组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-preview',
  props: {
    ishover: {
      type: Boolean,
      default: false
    },
    option: {
      type: Object as PropType<Text>,
      default: () => ({} as Text)
    }
  },
  setup(props) {
    const alignVertical = computed(() => {
      const isHorizontal = props.option.direction === DirectionType.Horizontal;
      const style: Record<string, string | number> = {};
      style.display = 'flex';
      style[isHorizontal ? 'alignItems' : 'justifyContent'] =
        props.option.alignVertical === VerticalAlign.Top
          ? 'flex-start'
          : props.option.alignVertical === VerticalAlign.Bottom
          ? 'flex-end'
          : 'center';

      style[isHorizontal ? 'justifyContent' : 'alignItems'] =
        props.option.alignHorizontal === TextAlign.Left
          ? 'flex-start'
          : props.option.alignHorizontal === TextAlign.Right
          ? 'flex-end'
          : 'center';
      return style;
    });

    const alignHorizontal = computed(() => {
      const style: Record<string, string | number> = {};

      style.display = 'block';
      style.textAlignLast = props.option.alignHorizontal;
      return style;
    });

    const textStyle = computed(() => {
      const style: Record<string, string | number> = {};

      // 文字修饰 - 斜体
      style.fontStyle = props.option.italic ? 'italic' : 'normal';

      // 文字修饰 - 下划线和删除线处理（互斥）
      let textDecoration = 'none';
      if (props.option.underlined) {
        textDecoration = 'underline';
      } else if (props.option.through) {
        textDecoration = 'line-through';
      }
      style.textDecoration = textDecoration;
      // 文字方向
      style.writingMode = props.option.direction === DirectionType.Horizontal ? 'horizontal-tb' : 'vertical-lr';
      style.textOrientation = props.option.direction === DirectionType.Horizontal ? 'mixed' : 'upright';
      style.whiteSpace = 'nowrap';
      return style;
    });

    const spanStyle = computed(() => {
      const style: Record<string, string | number> = {};

      // 展示模式 - 文本适应方式
      if (props.option.adapt) {
        switch (props.option.adapt) {
          case TextAdapt.Single:
            style.whiteSpace = 'nowrap';
            break;
          case TextAdapt.Ellipsis:
            style.width = '100px';
            style.overflow = 'hidden';
            style.textOverflow = 'ellipsis';
            style.display = '-webkit-box';
            style.webkitBoxOrient = 'vertical';
            style.webkitLineClamp = '1';
            break;
        }
      }

      const isHorizontal = props.option.direction === DirectionType.Horizontal;
      style[isHorizontal ? 'width' : 'height'] = props.option.alignHorizontal === TextAlign.Justify ? '100%' : 'auto';
      return style;
    });

    return {
      spanStyle,
      textStyle,
      alignVertical,
      alignHorizontal
    };
  }
});
