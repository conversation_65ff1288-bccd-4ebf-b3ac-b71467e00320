import { useDocumentStore } from '@vis/document-core';
import { useActionStore, useDesignStore } from '../stores';
import { useAction } from './action';
import { CacheService } from '@hetu/util';
import { computed } from 'vue';
import { Keyboard, osType } from '@hetu/platform-shared';

/**
 * 快捷键
 * <AUTHOR>
 */
export const useShortcutKeys = () => {
  const docStore = useDocumentStore();
  const actionStore = useActionStore();
  const designStore = useDesignStore();

  const { move, hand, deleteGraphs, copyGraph, pasteGraph } = useAction();

  const active = computed(() => designStore.active.value);

  /**
   * 添加快捷键
   */
  const addShortcuts = () => {
    window.addEventListener('keydown', onKeydown);
    window.addEventListener('keyup', onKeyup);

    window.addEventListener('mousedown', onMouseDown);
    window.addEventListener('mouseup', onMouseUp);
  };

  /**
   * 移除快捷键
   */
  const removeShortcuts = () => {
    window.removeEventListener('keydown', onKeydown);
    window.removeEventListener('keyup', onKeyup);

    window.removeEventListener('mousedown', onMouseDown);
    window.removeEventListener('mouseup', onMouseUp);
  };

  const onKeydown = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;
    const target = e.target as HTMLElement;

    if (target) {
      const tagName = target.tagName;
      const exceptTags = ['INPUT', 'TEXTAREA', 'SELECT'];
      if (exceptTags.indexOf(tagName) !== -1) {
        return;
      }
      if (target.contentEditable === 'true') {
        return;
      }
    }

    // 按空格键移动画布
    if (e.code === 'Space') {
      e.preventDefault();
      hand();
    }
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyS') {
      console.log('保存:', docStore.document.value);
      CacheService.set(`doc_${docStore.document.value.id}`, docStore.document.value);
      // 阻止事件的默认行为
      e.preventDefault();
    }
    if (e.key === 'Delete' || e.key === 'Backspace') {
      deleteGraphs();
    }
    // 复制
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyC') {
      copyGraph();
    }
    // 粘贴
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyV') {
      pasteGraph();
    }
  };

  const onKeyup = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;

    // 松开空格键框选组件
    if (e.code === 'Space') {
      move();
    }
  };

  const onMouseDown = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseDown:--------', true);
    }
  };

  const onMouseUp = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseUp:--------', false);
    }
  };

  /**
   * 根据按键编码返回对应值
   * @param keyboard 按键码
   */
  const getKeyCode = (keyboard: number | string | (number | string)[]) => {
    let code = '';

    if (Array.isArray(keyboard)) {
      const codes: string[] = [];
      keyboard.forEach((item: number | string) => {
        codes.push(getSingleKeyCode(item));
      });
      code = codes.join(' + ');
    } else {
      code = getSingleKeyCode(keyboard);
    }
    return code;
  };

  /**
   * 获取单个按键的显示代码
   * @param keyboard 按键码
   */
  const getSingleKeyCode = (keyboard: number | string) => {
    switch (keyboard) {
      case Keyboard.Meta:
      case Keyboard.Control:
        // Mac Command / Windows Ctrl 键
        return osType.isMac ? '⌘' : 'Ctrl';
      case Keyboard.Alt:
      case Keyboard.Option:
        // Alt/Option 键
        return osType.isMac ? '⌥' : 'Alt';
      case Keyboard.Shift:
        return osType.isMac ? '⇧' : 'Shift';
      case Keyboard['Numpad+']:
      case Keyboard['Numpad-']:
        return Keyboard[keyboard as number].replace('Numpad', '');
      case Keyboard.ArrowUp:
        return '↑';
      case Keyboard.ArrowRight:
        return '→';
      case Keyboard.ArrowDown:
        return '↓';
      case Keyboard.ArrowLeft:
        return '←';
      case 'mousewheel':
        return '鼠标滚轮';
      case 'click':
        return '鼠标单击';
      default:
        return Keyboard[keyboard as number] || keyboard.toString();
    }
  };

  return {
    addShortcuts,
    removeShortcuts,
    onKeydown,
    onKeyup,

    getKeyCode
  };
};
