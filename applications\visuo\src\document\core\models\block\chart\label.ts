import { Text, FillPaints, Stroke, Effects } from '../../ui';

/**
 * 图表标签类
 * <AUTHOR>
 */
export class Label {
  /** 标签是否可见 */
  visible: boolean = false;

  /** 标签透明度，取值范围 0-1 */
  opacity: number = 1;

  /** 标签与对应刻度的间距 */
  spacing: number = 0;

  /** 标签旋转角度，单位为度 */
  angle: number = 0;

  /** 标签水平偏移量 */
  offsetX: number = 0;

  /** 标签垂直偏移量 */
  offsetY: number = 0;

  /** 标签文字样式配置 */
  fontStyle: Text = new Text();

  /** 标签文字填充颜色 */
  fillPaints: FillPaints = new FillPaints();

  /** 标签文字描边样式 */
  stroke: Stroke = new Stroke();

  /** 标签阴影和特效 */
  effects: Effects = new Effects();
}
