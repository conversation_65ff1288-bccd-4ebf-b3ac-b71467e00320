import type { Records } from '@hetu/util';
import type { Effects, FillPaints, Stroke } from '../../ui';
import { isNumber } from 'lodash-es';

/**
 * 快速切换类
 */
export class QuickStyle {
  /**
   * 名称
   */
  title = '';
  /** 值 */
  id = '';
  /** 图标 */
  icon = '';
  /** 组件样式 */
  widgetBlockOptions: Records = {};
  /** 块样式 */
  graphStyle?: GraphStyle;
  constructor(title?: string, id?: string, icon?: string, widgetBlockOptions?: Records, graphStyle?: GraphStyle) {
    title && (this.title = title);
    id && (this.id = id);
    icon && (this.icon = icon);
    widgetBlockOptions && (this.widgetBlockOptions = widgetBlockOptions);
    graphStyle && (this.graphStyle = graphStyle);
  }
}

/**
 * 快切支持的图形样式
 */
export class GraphStyle {
  opacity?: number;
  radius?: [number, number, number, number];
  text?: Text;
  fillPaints?: FillPaints;
  stroke?: Stroke;
  effects?: Effects;
  width?: number;
  height?: number;

  constructor(
    opacity?: number,
    radius?: [number, number, number, number],
    text?: Text,
    fillPaints?: FillPaints,
    stroke?: Stroke,
    effects?: Effects,
    width?: number,
    height?: number
  ) {
    isNumber(opacity) && (this.opacity = opacity);
    radius && (this.radius = radius);
    text && (this.text = text);
    fillPaints && (this.fillPaints = fillPaints);
    stroke && (this.stroke = stroke);
    effects && (this.effects = effects);
    isNumber(width) && (this.width = width);
    isNumber(height) && (this.height = height);
  }
}
