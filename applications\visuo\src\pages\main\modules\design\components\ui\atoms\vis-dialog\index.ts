import { defineComponent, ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import type { PropType } from 'vue';
import { useDialogPluginComponent } from 'quasar';

export interface DialogPosition {
  x: number;
  y: number;
}

export default defineComponent({
  name: 'vis-dialog',
  emits: [...useDialogPluginComponent.emits, 'update:modelValue', 'show', 'hide', 'drag-start', 'drag-end', 'resize'],
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    width: {
      type: [String, Number],
      default: 'auto'
    },
    height: {
      type: [String, Number],
      default: 'auto'
    },
    minWidth: {
      type: [String, Number],
      default: 400
    },
    maxWidth: {
      type: [String, Number],
      default: '100vw'
    },
    minHeight: {
      type: [String, Number],
      default: 300
    },
    maxHeight: {
      type: [String, Number],
      default: '100vh'
    },
    draggable: {
      type: Boolean,
      default: true
    },
    persistent: {
      type: <PERSON>olean,
      default: true
    },
    isFullscreen: {
      type: Boolean,
      default: false
    },
    initialPosition: {
      type: Object as PropType<DialogPosition>,
      default: () => ({ x: 0, y: 0 })
    },
    zIndex: {
      type: Number,
      default: 2000
    }
  },
  setup(props, { emit, expose }) {
    // 使用 Quasar Dialog 插件
    const { dialogRef, onDialogOK, onDialogCancel, onDialogHide } = useDialogPluginComponent();

    // 响应式数据
    const isVisible = ref(false);
    const isDragging = ref(false);
    const dragOffset = ref<DialogPosition>({ x: 0, y: 0 });
    const dragStartPos = ref<DialogPosition>({ x: 0, y: 0 });
    const mouseStartPos = ref<DialogPosition>({ x: 0, y: 0 });
    const dialogCard = ref<HTMLElement | null>(null);
    const dialogContainer = ref<HTMLElement | null>(null);

    // 计算属性
    const dialogStyle = computed(() => ({
      width: typeof props.width === 'number' ? `${props.width}px` : props.width,
      height: typeof props.height === 'number' ? `${props.height}px` : props.height,
      minWidth: typeof props.minWidth === 'number' ? `${props.minWidth}px` : props.minWidth,
      maxWidth: typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth,
      minHeight: typeof props.minHeight === 'number' ? `${props.minHeight}px` : props.minHeight,
      maxHeight: typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight,
      zIndex: props.zIndex
    }));

    const transformStyle = computed(() => ({
      transform: props.isFullscreen ? 'none' : `translate(${dragOffset.value.x}px, ${dragOffset.value.y}px)`
    }));

    // 拖拽边界限制常量
    const DRAG_BOUNDS = {
      // 水平方向限制：对话框不能拖出屏幕80%范围
      HORIZONTAL_LIMIT: 0.8,
      VERTICAL_TOP_LIMIT: 0.2,
      VERTICAL_BOTTOM_LIMIT: 0.8,
      // 顶部最小尺寸
      TOP_MIN_DISTANCE: 40
    };

    // 防抖处理
    let dragAnimationFrame: number | null = null;

    // 计算拖拽边界（缓存窗口尺寸）
    let cachedWindowSize = { width: 0, height: 0 };
    const getDragBounds = () => {
      // 缓存窗口尺寸，避免频繁获取
      if (cachedWindowSize.width !== window.innerWidth || cachedWindowSize.height !== window.innerHeight) {
        cachedWindowSize = {
          width: window.innerWidth,
          height: window.innerHeight
        };
      }

      const { width, height } = cachedWindowSize;
      return {
        minX: -width * DRAG_BOUNDS.HORIZONTAL_LIMIT,
        maxX: width * DRAG_BOUNDS.HORIZONTAL_LIMIT,
        minY: DRAG_BOUNDS.TOP_MIN_DISTANCE - height * DRAG_BOUNDS.VERTICAL_TOP_LIMIT,
        maxY: height * DRAG_BOUNDS.VERTICAL_BOTTOM_LIMIT
      };
    };

    // 弹框边界限制
    const applyBounds = (x: number, y: number) => {
      const bounds = getDragBounds();
      return {
        x: Math.max(bounds.minX, Math.min(bounds.maxX, x)),
        y: Math.max(bounds.minY, Math.min(bounds.maxY, y))
      };
    };

    // 拖拽处理函数
    const updateDragPosition = (deltaX: number, deltaY: number) => {
      const newX = dragStartPos.value.x + deltaX;
      const newY = dragStartPos.value.y + deltaY;

      // 弹框边界限制
      const boundedPosition = applyBounds(newX, newY);

      if (dragAnimationFrame) {
        cancelAnimationFrame(dragAnimationFrame);
      }

      dragAnimationFrame = requestAnimationFrame(() => {
        dragOffset.value = boundedPosition;
        dragAnimationFrame = null;
      });
    };

    watch(
      () => props.modelValue,
      (newVal) => {
        isVisible.value = newVal;
        if (newVal) {
          nextTick(() => {
            centerDialog();
          });
        }
      }
    );

    watch(isVisible, (newVal) => {
      emit('update:modelValue', newVal);
    });

    const centerDialog = () => {
      nextTick(() => {
        // 直接居中，不需要获取元素位置
        dragOffset.value = { x: 0, y: 0 };
      });
    };

    const startDrag = (event: MouseEvent) => {
      if (!props.draggable || !dialogCard.value) return;

      event.preventDefault();
      event.stopPropagation();

      isDragging.value = true;
      mouseStartPos.value = { x: event.clientX, y: event.clientY };
      dragStartPos.value = { ...dragOffset.value };

      document.addEventListener('mousemove', onDrag);
      document.addEventListener('mouseup', stopDrag);

      emit('drag-start', { event, position: dragOffset.value });
    };

    const onDrag = (event: MouseEvent) => {
      if (!isDragging.value) return;

      event.preventDefault();

      // 计算鼠标移动距离
      const deltaX = event.clientX - mouseStartPos.value.x;
      const deltaY = event.clientY - mouseStartPos.value.y;

      // 更新拖拽位置
      updateDragPosition(deltaX, deltaY);
    };

    const stopDrag = () => {
      if (isDragging.value) {
        isDragging.value = false;
        emit('drag-end', { position: dragOffset.value });
      }

      // 清理防抖处理
      if (dragAnimationFrame) {
        cancelAnimationFrame(dragAnimationFrame);
        dragAnimationFrame = null;
      }

      // 移除事件监听器
      document.removeEventListener('mousemove', onDrag);
      document.removeEventListener('mouseup', stopDrag);
    };

    const closeDialog = () => {
      isVisible.value = false;
      onDialogCancel();
    };

    const onShow = () => {
      emit('show');

      nextTick(() => {
        if (props.initialPosition) {
          dragOffset.value = { ...props.initialPosition };
        } else {
          centerDialog();
        }
      });
    };

    const onHide = () => {
      emit('hide');
      // 重置拖拽状态
      isDragging.value = false;
      onDialogHide();
    };

    // 生命周期钩子
    onUnmounted(() => {
      // 清理防抖处理
      if (dragAnimationFrame) {
        cancelAnimationFrame(dragAnimationFrame);
        dragAnimationFrame = null;
      }

      // 移除事件监听器
      document.removeEventListener('mousemove', onDrag);
      document.removeEventListener('mouseup', stopDrag);
    });

    return {
      dialogRef,
      onDialogOK,
      onDialogCancel,
      onDialogHide,

      // 响应式数据
      isVisible,
      isDragging,
      dragOffset,
      dialogCard,
      dialogContainer,

      // 计算属性
      dialogStyle,
      transformStyle,

      // 方法
      startDrag,
      onDrag,
      stopDrag,
      closeDialog,
      onShow,
      onHide,
      centerDialog
    };
  }
});
