<template>
  <div class="vis-layout">
    <div class="vis-form-inline" v-if="frame && autoLayout">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label">类型</div>
          <div class="vis-form-field__content">
            <vis-button-group v-model="autoLayout.direction" :options="directionOptions" @change="onChangeDirection" />
          </div>
        </div>
      </div>
      <q-btn
        v-if="autoLayout.direction === DirectionType.Horizontal"
        class="btn-field"
        :class="{ active: autoLayout.flowWarp }"
        @click="autoLayout.flowWarp = !autoLayout.flowWarp"
      >
        <ht-icon name="vis-layout-warp" />
        <q-tooltip> 换行 </q-tooltip>
      </q-btn>
    </div>

    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <!-- 水平、垂直布局的宽高 -->
        <!-- 1. flex布局 2. flex布局内的子元素,并且未设置忽略自动布局 -->
        <template
          v-if="
            (frame && autoLayout && autoLayout.direction !== DirectionType.Freeform) ||
            (graph.parent && parentFrame && (isFlex(parentFrame) || isGrid(parentFrame)) && !graph.ignoreAutoLayout)
          "
        >
          <div class="vis-form-field">
            <div class="vis-form-field__label">宽度</div>
            <div class="vis-form-field__content">
              <vis-mix-input
                v-model="graph.width"
                :min="GRAPH_SIZE_MIN"
                :icon="`hticon-vis-${
                  graph.limitSize.width.resize === ResizeType.Adapt
                    ? 'resize-adapt-x'
                    : graph.limitSize.width.resize === ResizeType.Fill
                    ? 'resize-fill-x'
                    : 'letter-w'
                }`"
                class="pr-0"
                :class="{ 'aspect-ratio-locked-left': graph.aspectRatio }"
                :input-class="graph.limitSize.width.resize !== ResizeType.Fixed ? 'hidden' : ''"
                @change="onSizeWidth"
                :disabled="sizeWidthDisabled"
              >
                <template v-slot:default v-if="graph.limitSize.width.resize !== ResizeType.Fixed">
                  <span class="text-font-regular">
                    {{ graph.limitSize.width.resize === ResizeType.Adapt ? '适应' : '充满' }}
                  </span>
                </template>
                <template v-slot:append>
                  <q-btn>
                    <q-icon name="keyboard_arrow_down" class="!text-xs" />
                    <q-menu v-model="showMenuWidth" style="width: 180px" class="vis-menu" dense>
                      <q-list dense>
                        <q-item
                          :active="graph.limitSize.width.resize === ResizeType.Fixed"
                          @click="onChangeResize('width', ResizeType.Fixed)"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-resize-fixed-x" />
                          </q-item-section>
                          <q-item-section>固定尺寸 : {{ graph.width }}</q-item-section>
                        </q-item>
                        <!-- 1. 设置了flex布局的父容器  2. 在flex布局内的文本-->
                        <q-item
                          v-if="
                            (frame && isFlex(frame)) ||
                            (graph.parent && parentFrame && isFlex(parentFrame) && graph.type === GraphType.TextBox)
                          "
                          :active="graph.limitSize.width.resize === ResizeType.Adapt"
                          @click="onChangeResize('width', ResizeType.Adapt)"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-resize-adapt-x" />
                          </q-item-section>
                          <q-item-section>适应内容</q-item-section>
                        </q-item>
                        <!-- 设置了flex布局的子元素   -->
                        <q-item
                          v-if="graph.parent && parentFrame && (isFlex(parentFrame) || isGrid(parentFrame))"
                          :active="graph.limitSize.width.resize === ResizeType.Fill"
                          @click="onChangeResize('width', ResizeType.Fill)"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-resize-fill-x" />
                          </q-item-section>
                          <q-item-section>充满容器</q-item-section>
                        </q-item>
                        <q-separator />
                        <q-item
                          v-if="graph.limitSize.width.max === ''"
                          @click="isShowSize.width.max = true"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-max-width" />
                          </q-item-section>
                          <q-item-section>添加最大宽度</q-item-section>
                        </q-item>
                        <q-item v-else lickable v-close-popup>
                          <q-item-section avatar>
                            <ht-icon name="vis-max-width" />
                          </q-item-section>
                          <q-item-section>最大宽度: {{ graph.limitSize.width.max }}</q-item-section>
                        </q-item>
                        <q-item
                          v-if="graph.limitSize.width.min === ''"
                          @click="isShowSize.width.min = true"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-min-width" />
                          </q-item-section>
                          <q-item-section>添加最小宽度</q-item-section>
                        </q-item>
                        <q-item v-else clickable v-close-popup>
                          <q-item-section avatar>
                            <ht-icon name="vis-min-width" />
                          </q-item-section>
                          <q-item-section>最小宽度: {{ graph.limitSize.width.min }}</q-item-section>
                        </q-item>
                        <template v-if="graph.limitSize.width.min !== '' || graph.limitSize.width.max !== ''">
                          <q-separator />
                          <q-item @click="onDeleteSize('width')" clickable v-close-popup>
                            <q-item-section avatar>
                              <ht-icon name="vis-delete-width" />
                            </q-item-section>
                            <q-item-section>删除宽度限制</q-item-section>
                          </q-item>
                        </template>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </template>
              </vis-mix-input>
            </div>
          </div>

          <div class="vis-form-field">
            <div class="vis-form-field__label">高度</div>
            <div class="vis-form-field__content">
              <vis-mix-input
                v-model="graph.height"
                :min="GRAPH_SIZE_MIN"
                :icon="`hticon-vis-${
                  graph.limitSize.height.resize === ResizeType.Adapt
                    ? 'resize-adapt-y'
                    : graph.limitSize.height.resize === ResizeType.Fill
                    ? 'resize-fill-y'
                    : 'letter-h'
                }`"
                class="pr-0"
                :class="{ 'aspect-ratio-locked-right': graph.aspectRatio }"
                :input-class="graph.limitSize.height.resize !== ResizeType.Fixed ? 'hidden' : ''"
                @change="onSizeHeight"
                :disabled="sizeHeightDisabled"
              >
                <template v-slot:default v-if="graph.limitSize.height.resize !== ResizeType.Fixed">
                  <span class="text-font-regular">
                    {{ graph.limitSize.height.resize === ResizeType.Adapt ? '适应' : '充满' }}
                  </span>
                </template>
                <template v-slot:append>
                  <q-btn>
                    <q-icon name="keyboard_arrow_down" class="!text-xs" />
                    <q-menu v-model="showMenuHeight" style="width: 180px" class="vis-menu" dense>
                      <q-list dense>
                        <q-item
                          :active="graph.limitSize.height.resize === ResizeType.Fixed"
                          @click="onChangeResize('height', ResizeType.Fixed)"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-resize-fixed-y" />
                          </q-item-section>
                          <q-item-section>固定尺寸: {{ graph.height }}</q-item-section>
                        </q-item>
                        <!-- 设置了flex布局的父容器  -->
                        <q-item
                          v-if="
                            (frame && isFlex(frame)) ||
                            (graph.parent && parentFrame && isFlex(parentFrame) && graph.type === GraphType.TextBox)
                          "
                          :active="graph.limitSize.height.resize === ResizeType.Adapt"
                          @click="onChangeResize('height', ResizeType.Adapt)"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-resize-adapt-y" />
                          </q-item-section>
                          <q-item-section>适应内容</q-item-section>
                        </q-item>
                        <!-- 设置了flex布局的子元素   -->
                        <q-item
                          v-if="graph.parent && parentFrame && (isFlex(parentFrame) || isGrid(parentFrame))"
                          :active="graph.limitSize.height.resize === ResizeType.Fill"
                          @click="onChangeResize('height', ResizeType.Fill)"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-resize-fill-y" />
                          </q-item-section>
                          <q-item-section>充满容器</q-item-section>
                        </q-item>
                        <q-separator />
                        <q-item
                          v-if="graph.limitSize.height.max === ''"
                          @click="isShowSize.height.max = true"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-max-height" />
                          </q-item-section>
                          <q-item-section>添加最大高度</q-item-section>
                        </q-item>
                        <q-item v-else clickable v-close-popup>
                          <q-item-section avatar>
                            <ht-icon name="vis-max-height" />
                          </q-item-section>
                          <q-item-section>最大高度: {{ graph.limitSize.height.max }} </q-item-section>
                        </q-item>

                        <q-item
                          v-if="graph.limitSize.height.min === ''"
                          @click="isShowSize.height.min = true"
                          clickable
                          v-close-popup
                        >
                          <q-item-section avatar>
                            <ht-icon name="vis-min-height" />
                          </q-item-section>
                          <q-item-section>添加最小高度</q-item-section>
                        </q-item>
                        <q-item v-else clickable v-close-popup>
                          <q-item-section avatar>
                            <ht-icon name="vis-min-height" />
                          </q-item-section>
                          <q-item-section>最小高度: {{ graph.limitSize.height.min }}</q-item-section>
                        </q-item>
                        <template v-if="graph.limitSize.height.min !== '' || graph.limitSize.height.max !== ''">
                          <q-separator />
                          <q-item @click="onDeleteSize('height')" clickable v-close-popup>
                            <q-item-section avatar>
                              <ht-icon name="vis-delete-height" />
                            </q-item-section>
                            <q-item-section>删除高度限制</q-item-section>
                          </q-item>
                        </template>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </template>
              </vis-mix-input>
            </div>
          </div>
        </template>
        <!-- 其他的宽高 -->
        <template v-else>
          <div class="vis-form-field">
            <div class="vis-form-field__label">宽度</div>
            <div class="vis-form-field__content">
              <vis-number
                :class="{ 'aspect-ratio-locked-left': graph.aspectRatio }"
                v-model="graph.width"
                :min="GRAPH_SIZE_MIN"
                icon="hticon-vis-letter-w"
                @change="onSizeWidth"
                :disabled="sizeWidthDisabled"
              />
            </div>
          </div>

          <div class="vis-form-field">
            <div class="vis-form-field__label">高度</div>
            <div class="vis-form-field__content">
              <vis-number
                :class="{ 'aspect-ratio-locked-right': graph.aspectRatio }"
                v-model="graph.height"
                :min="GRAPH_SIZE_MIN"
                icon="hticon-vis-letter-h"
                @change="onSizeHeight"
                :disabled="sizeHeightDisabled"
              />
            </div>
          </div>
        </template>
      </div>
      <q-btn class="btn-field" :class="{ active: graph.aspectRatio }" @click="onAspectRatio">
        <ht-icon class="vis-icon" name="vis-associate" />
        <q-tooltip> 锁定纵横比 </q-tooltip>
      </q-btn>
    </div>

    <!-- 最大、最小限制 -->
    <template
      v-if="
        (frame && autoLayout && autoLayout.direction !== DirectionType.Freeform) ||
        (graph.parent && parentFrame && (isFlex(parentFrame) || isGrid(parentFrame)) && !graph.ignoreAutoLayout)
      "
    >
      <div
        class="vis-form-inline"
        v-if="isShowSize.width.max || isShowSize.width.min || isShowSize.height.max || isShowSize.height.min"
      >
        <div class="vis-form-inline__content--minus-32 !items-start">
          <div class="vis-form-field">
            <div class="vis-form-field" v-if="isShowSize.width.max">
              <div class="vis-form-field__label">最大宽度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="graph.limitSize.width.max"
                  :min="graph.width"
                  @change="onChangeSize('max', 'width', $event)"
                  icon="hticon-vis-max-width"
                  class="pr-0"
                >
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item @click="onDeleteSize('width', 'max')" clickable v-close-popup>
                            <q-item-section avatar>
                              <ht-icon name="vis-delete" />
                            </q-item-section>
                            <q-item-section>删除最大宽度</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>
            <div class="vis-form-field" v-if="isShowSize.width.min">
              <div class="vis-form-field__label">最小宽度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="graph.limitSize.width.min"
                  :max="graph.width"
                  @change="onChangeSize('min', 'width', $event)"
                  icon="hticon-vis-min-width"
                  class="pr-0"
                >
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item @click="onDeleteSize('width', 'min')" clickable v-close-popup>
                            <q-item-section avatar>
                              <ht-icon name="vis-delete" />
                            </q-item-section>
                            <q-item-section>删除最小宽度</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field" v-if="isShowSize.height.max">
              <div class="vis-form-field__label">最大高度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="graph.limitSize.height.max"
                  :min="graph.height"
                  @change="onChangeSize('max', 'height', $event)"
                  icon="hticon-vis-max-height"
                  class="pr-0"
                >
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item @click="onDeleteSize('height', 'max')" clickable v-close-popup>
                            <q-item-section avatar>
                              <ht-icon name="vis-delete" />
                            </q-item-section>
                            <q-item-section>删除最大高度</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>
            <div class="vis-form-field" v-if="isShowSize.height.min">
              <div class="vis-form-field__label">最小高度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="graph.limitSize.height.min"
                  :max="graph.height"
                  @change="onChangeSize('min', 'height', $event)"
                  icon="hticon-vis-min-height"
                  class="pr-0"
                >
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item @click="onDeleteSize('height', 'min')" clickable v-close-popup>
                            <q-item-section avatar>
                              <ht-icon name="vis-delete" />
                            </q-item-section>
                            <q-item-section>删除最小高度</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-if="frame && autoLayout && autoLayout.direction !== DirectionType.Freeform">
      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32 !items-start">
          <div class="vis-form-field">
            <div class="vis-form-field__label">
              {{ autoLayout.direction === DirectionType.Grid ? '网格' : '对齐' }}
            </div>
            <div class="vis-form-field__content">
              <template v-if="[DirectionType.Horizontal, DirectionType.Vertical].includes(autoLayout.direction)">
                <div
                  class="vis-layout-align"
                  v-if="
                    (autoLayout.direction === DirectionType.Horizontal && autoLayout.horizontalGap !== 'Auto') ||
                    (autoLayout.direction === DirectionType.Vertical && autoLayout.verticalGap !== 'Auto')
                  "
                >
                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.Start &&
                        autoLayout.crossAxisAlignment === CrossAlignment.Start
                    }"
                    @click="onHandleAlign(MainAlignment.Start, CrossAlignment.Start)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="autoLayout.direction === DirectionType.Horizontal ? 'vis-align-top' : 'vis-align-left'"
                    />
                    <q-tooltip> 左上对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.Center &&
                        autoLayout.crossAxisAlignment === CrossAlignment.Start
                    }"
                    @click="onHandleAlign(MainAlignment.Center, CrossAlignment.Start)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="
                        autoLayout.direction === DirectionType.Horizontal ? 'vis-align-top' : 'vis-vertical-center'
                      "
                    />
                    <q-tooltip> 顶部居中对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.End &&
                        autoLayout.crossAxisAlignment === CrossAlignment.Start
                    }"
                    @click="onHandleAlign(MainAlignment.End, CrossAlignment.Start)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="autoLayout.direction === DirectionType.Horizontal ? 'vis-align-top' : 'vis-align-right'"
                    />
                    <q-tooltip> 右上对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.Start &&
                        autoLayout.crossAxisAlignment === CrossAlignment.Center
                    }"
                    @click="onHandleAlign(MainAlignment.Start, CrossAlignment.Center)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="
                        autoLayout.direction === DirectionType.Horizontal ? 'vis-horizontal-center' : 'vis-align-left'
                      "
                    />
                    <q-tooltip> 左对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.Center &&
                        autoLayout.crossAxisAlignment === CrossAlignment.Center
                    }"
                    @click="onHandleAlign(MainAlignment.Center, CrossAlignment.Center)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="
                        autoLayout.direction === DirectionType.Horizontal
                          ? 'vis-horizontal-center'
                          : 'vis-vertical-center'
                      "
                    />
                    <q-tooltip> 居中对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.End &&
                        autoLayout.crossAxisAlignment === CrossAlignment.Center
                    }"
                    @click="onHandleAlign(MainAlignment.End, CrossAlignment.Center)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="
                        autoLayout.direction === DirectionType.Horizontal ? 'vis-horizontal-center' : 'vis-align-right'
                      "
                    />
                    <q-tooltip> 右对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.Start &&
                        autoLayout.crossAxisAlignment === CrossAlignment.End
                    }"
                    @click="onHandleAlign(MainAlignment.Start, CrossAlignment.End)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="autoLayout.direction === DirectionType.Horizontal ? 'vis-align-bottom' : 'vis-align-left'"
                    />
                    <q-tooltip> 左下对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.Center &&
                        autoLayout.crossAxisAlignment === CrossAlignment.End
                    }"
                    @click="onHandleAlign(MainAlignment.Center, CrossAlignment.End)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="
                        autoLayout.direction === DirectionType.Horizontal ? 'vis-align-bottom' : 'vis-vertical-center'
                      "
                    />
                    <q-tooltip> 底部居中对齐 </q-tooltip>
                  </div>

                  <div
                    :class="{
                      hand: true,
                      active:
                        autoLayout.mainAxisAlignment === MainAlignment.End &&
                        autoLayout.crossAxisAlignment === CrossAlignment.End
                    }"
                    @click="onHandleAlign(MainAlignment.End, CrossAlignment.End)"
                  >
                    <div class="dot"></div>
                    <ht-icon
                      :name="autoLayout.direction === DirectionType.Horizontal ? 'vis-align-bottom' : 'vis-align-right'"
                    />
                    <q-tooltip> 右下对齐 </q-tooltip>
                  </div>
                </div>

                <div v-else :class="`vis-layout-between-align ${autoLayout.direction}`">
                  <div
                    :class="{ hand: true, active: autoLayout.crossAxisAlignment === CrossAlignment.Start }"
                    @click="onHandleAlign(MainAlignment.Start, CrossAlignment.Start)"
                  >
                    <div class="bar items-start">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                    <div class="dot">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  </div>
                  <div
                    :class="{ hand: true, active: autoLayout.crossAxisAlignment === CrossAlignment.Center }"
                    @click="onHandleAlign(MainAlignment.Start, CrossAlignment.Center)"
                  >
                    <div class="bar items-center">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                    <div class="dot">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  </div>
                  <div
                    :class="{ hand: true, active: autoLayout.crossAxisAlignment === CrossAlignment.End }"
                    @click="onHandleAlign(MainAlignment.Start, CrossAlignment.End)"
                  >
                    <div class="bar items-end">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                    <div class="dot">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  </div>
                </div>
              </template>
              <div
                v-if="autoLayout.direction === DirectionType.Grid"
                class="vis-layout-grid flex flex-center text-xs"
                @click="onShowPopup"
              >
                {{ autoLayout.gridSize[0] }} × {{ autoLayout.gridSize[1] }}
              </div>
              <vis-popup title="网格" ref="popupRef" :target="false" @hide="onHidePopup" width="260px">
                <div class="vis-form-inline !flex-nowrap">
                  <div class="vis-form-field !flex-1">
                    <div class="vis-form-field__content">
                      <vis-number
                        v-model="autoLayout.gridSize[0]"
                        @change="onChangeGridRow"
                        icon="hticon-vis-grid-row"
                        :min="1"
                        :precision="0"
                      />
                    </div>
                  </div>
                  ×
                  <div class="vis-form-field !flex-1">
                    <div class="vis-form-field__content">
                      <vis-number
                        v-model="autoLayout.gridSize[1]"
                        @change="onChangeGridCol"
                        icon="hticon-vis-grid-col"
                        :min="1"
                        :precision="0"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="vis-menu-grid_picker row gap-1 pt-3"
                  @mousemove="onMouseMovePicker"
                  @mouseleave="onMouseLeavePicker"
                >
                  <div class="row gap-1" v-for="row in 12" :key="row">
                    <div
                      :class="{
                        hand: true,
                        hover: row <= hoverGrid[0] && col <= hoverGrid[1],
                        active: row <= autoLayout.gridSize[0] && col <= autoLayout.gridSize[1]
                      }"
                      :data-row="row"
                      :data-col="col"
                      v-for="col in 12"
                      :key="col"
                      @click="onClickGrid(row, col)"
                    >
                      <q-tooltip> {{ row }} × {{ col }} </q-tooltip>
                    </div>
                  </div>
                </div>
              </vis-popup>
            </div>
          </div>

          <div class="vis-form-field">
            <div class="vis-form-field__label">间距</div>
            <div class="vis-form-field__content column">
              <vis-mix-input
                v-if="autoLayout.direction === DirectionType.Horizontal"
                v-model="autoLayout.horizontalGap"
                @change="onChangeGap('horizontal', $event)"
                icon="hticon-vis-gap-horizontal"
                class="pr-0"
                :min="0"
                :precision="0"
              >
                <template v-slot:append>
                  <q-btn>
                    <q-icon name="keyboard_arrow_down" class="!text-xs" />
                    <q-menu style="width: 120px" class="vis-menu" dense>
                      <q-list dense>
                        <q-item
                          :active="autoLayout.horizontalGap !== 'Auto'"
                          @click="autoLayout.horizontalGap = 10"
                          clickable
                          v-close-popup
                        >
                          <q-item-section>自定义</q-item-section>
                        </q-item>
                        <q-item
                          :active="autoLayout.horizontalGap === 'Auto'"
                          @click="autoLayout.horizontalGap = 'Auto'"
                          clickable
                          v-close-popup
                        >
                          <q-item-section>Auto</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </template>
              </vis-mix-input>

              <vis-mix-input
                v-if="autoLayout.direction === DirectionType.Vertical || autoLayout.flowWarp"
                v-model="autoLayout.verticalGap"
                @change="onChangeGap('vertical', $event)"
                icon="hticon-vis-gap-vertical"
                class="pr-0"
                :min="0"
                :precision="0"
              >
                <template v-slot:append>
                  <q-btn>
                    <q-icon name="keyboard_arrow_down" class="!text-xs" />
                    <q-menu dense style="width: 120px" class="vis-menu">
                      <q-list dense>
                        <q-item
                          :active="autoLayout.verticalGap !== 'Auto'"
                          clickable
                          v-close-popup
                          @click="autoLayout.verticalGap = 10"
                        >
                          <q-item-section>自定义</q-item-section>
                        </q-item>
                        <q-item
                          :active="autoLayout.verticalGap === 'Auto'"
                          clickable
                          v-close-popup
                          @click="autoLayout.verticalGap = 'Auto'"
                        >
                          <q-item-section>Auto</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </template>
              </vis-mix-input>
              <template v-if="autoLayout.direction === DirectionType.Grid">
                <vis-number
                  v-model="autoLayout.horizontalGap"
                  icon="hticon-vis-gap-horizontal"
                  :min="0"
                  :precision="0"
                />
                <vis-number v-model="autoLayout.verticalGap" icon="hticon-vis-gap-vertical" :min="0" :precision="0" />
              </template>
            </div>
          </div>
        </div>
      </div>

      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">边距</div>
            <div class="vis-form-field__content">
              <vis-number
                v-if="showPadding"
                v-model="autoLayout.padding[0]"
                icon="hticon-vis-padding-top"
                tooltip="上"
                :min="0"
                :precision="0"
              />
              <vis-number
                v-else
                v-model="paddingV"
                icon="hticon-vis-padding-y"
                @change="onChangePadding('v', $event)"
                tooltip="上下"
                :min="0"
                :precision="0"
              />
            </div>
          </div>

          <div class="vis-form-field">
            <div class="vis-form-field__label"></div>
            <div class="vis-form-field__content">
              <vis-number
                v-if="showPadding"
                v-model="autoLayout.padding[2]"
                icon="hticon-vis-padding-bottom"
                tooltip="下"
                :min="0"
                :precision="0"
              />
              <vis-number
                v-else
                v-model="paddingH"
                icon="hticon-vis-padding-x"
                @change="onChangePadding('h', $event)"
                tooltip="左右"
                :min="0"
                :precision="0"
              />
            </div>
          </div>
        </div>

        <q-btn flat class="btn-field" :class="{ active: showPadding }" @click="showPadding = !showPadding">
          <ht-icon name="vis-padding-round" class="vis-icon" />
          <q-tooltip> 单独设置 </q-tooltip>
        </q-btn>
      </div>

      <div class="vis-form-inline" v-if="showPadding">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-number
                v-model="autoLayout.padding[3]"
                icon="hticon-vis-padding-left"
                tooltip="左"
                :min="0"
                :precision="0"
              />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-number
                v-model="autoLayout.padding[1]"
                icon="hticon-vis-padding-right"
                tooltip="右"
                :min="0"
                :precision="0"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
    <div v-if="frame" class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <q-checkbox v-model="autoLayout.scrollDirection" true-value="all" false-value="none" label="滚动条" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
