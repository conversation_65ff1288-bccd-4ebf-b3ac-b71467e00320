<template>
  <div class="vis-text-more">
    <q-btn color="grey-5" outline @click.stop="showPopup">
      <span :style="textColor" class="text-sm">A</span>
    </q-btn>
    <vis-popup ref="textPopupRef" :target="false" @hide="popupShow = false" title="文字">
      <template #title v-if="textEffects">
        <q-tabs
          v-model="tab"
          dense
          active-color="black"
          active-bg-color="grey-2"
          indicator-color="transparent"
          align="left"
          narrow-indicator
          class="vis-tabs"
        >
          <q-tab name="text" label="文字" />
          <q-tab name="effects" label="特效" />
        </q-tabs>
      </template>
      <component :is="`vis-text-popup-${tab}`" :options="tab === 'text' ? text : textEffects"></component>
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
