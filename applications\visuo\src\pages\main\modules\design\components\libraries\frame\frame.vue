<template>
  <div :className="className" :id="frame.id" :parent="frame.parent" :style="frameStyle">
    <div class="wrap" :style="wrapStyle">
      <vis-graph-entrance v-for="ele in children" :key="ele.id" :graph="ele" :parent="frame" />
    </div>
    <vis-graph-entrance v-for="ele in ignoreAutoGraphs" :key="ele.id" :graph="ele" :parent="frame" style="top: 0" />

    <template v-if="isGrid(frame)">
      <div class="vis-frame-grid-ghost" :style="gridGhostStyle">
        <div
          v-for="i in frame.autoLayout.gridSize[0] * frame.autoLayout.gridSize[1]"
          :key="i"
          :data-row="getGridItemRowIndex(i, 'row')"
          :data-col="getGridItemRowIndex(i, 'col')"
          :class="{
            active: isActiveGhostItem(i)
          }"
        ></div>
      </div>
    </template>

    <div v-if="layoutGuides.length" class="vis-frame-layout-guides">
      <div
        v-for="(layoutGuide, index) in layoutGuides"
        :key="index"
        :id="layoutGuide.id"
        :class="{
          invisible: !layoutGuide.visible,
          [`${layoutGuide.type}-layout-ghost`]: true
        }"
        :style="layoutGuideStyle(layoutGuide)"
      >
        <template v-if="layoutGuide.type !== LayoutGuideType.Grid">
          <div
            v-for="i in layoutGuide.count"
            :key="i"
            :[layoutGuide.type]="i"
            :style="{ background: getFillStyle(layoutGuide.color).backgroundColor }"
          ></div>
        </template>
      </div>
    </div>

    <div class="vis-frame-title" v-if="!frame.parent">
      <span @click="onClickName">{{ frame.name }}</span>
    </div>
  </div>
</template>

<script lang="ts" src="./frame.ts"></script>
<style lang="scss" src="./frame.scss"></style>

<!-- <span v-if="!isEdit" @click="onClickName">{{ frame.name }}</span>
       <input v-else ref="inputRef" v-model="frame.name" @blur="isEdit = !isEdit" /> -->
