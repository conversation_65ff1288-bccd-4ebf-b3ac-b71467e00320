@import '../../../index.scss';

.#{$vis-prefix}-frame-grid-layout-ghost {
  // z-index: $active-ghost-z-index;

  .rect {
    @apply absolute outline-1 outline-solid;
    background: rgba($canvas-theme-color, 0.1);
    outline-color: $canvas-theme-color;
  }
  .left {
    @apply absolute w-50px;
    .hand {
      @apply h-full;

      .rect {
        @apply h-full left-50px;

        .drag {
          @apply absolute h-10px w-full cursor-row-resize;

          &.t {
            @apply top--6px;
          }
          &.b {
            @apply bottom--6px;
          }
        }
      }
    }
  }
  .top {
    @apply absolute h-50px;
    .hand {
      @apply w-full h-full;

      .rect {
        @apply w-full top-50px;

        .drag {
          @apply absolute w-10px h-full cursor-col-resize;

          &.l {
            @apply left--6px;
          }
          &.r {
            @apply right--6px;
          }
        }
      }
    }
  }

  .hand {
    .item {
      @apply relative;

      .tip {
        @apply absolute flex justify-center items-center w-full h-full invisible;
        .text {
          @apply text-white text-10px p-1 rounded cursor-pointer;
          background-color: $canvas-theme-color;
        }
        .q-field {
          &__control-container,
          &__control,
          &__native {
            @apply w-28px h-20px min-h-20px leading-20px;
          }
          &__native {
            @apply text-10px text-center;
          }
        }
      }
      &:hover {
        .tip {
          @apply visible;
        }
      }
    }
  }
}
