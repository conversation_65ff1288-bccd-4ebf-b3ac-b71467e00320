import { Graph, useDocumentStore, Block, WidgetBlock } from '@vis/document-core';
import { useDesignStore } from '../../../../../../stores';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { DatasetField, DatasetService } from '@hetu/metadata-shared';
import { useDataset } from '@vis/page-main/modules/design/hooks';

/**
 * 字段列表
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-field-list',
  props: {
    fieldList: {
      type: Array as PropType<DatasetField[]>,
      default: () => []
    },
    activeIds: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    // 单选
    single: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const { rightWidth } = useDesignStore();

    const search = ref('');
    // 内部维护的选中状态
    const selectedIds = ref<string[]>([]);

    // 初始化选中状态
    watch(
      () => props.activeIds,
      (newActiveIds) => {
        selectedIds.value = [...(newActiveIds || [])];
      },
      { immediate: true }
    );

    // 过滤后的字段列表
    const filteredFieldList = computed(() => {
      if (!search.value.trim()) {
        return props.fieldList;
      }

      const searchTerm = search.value.toLowerCase().trim();
      return props.fieldList.filter((field) => {
        // 搜索字段别名和字段名
        const alias = field.fieldAlias?.toLowerCase() || '';
        const name = field.fieldName?.toLowerCase() || '';
        return alias.includes(searchTerm) || name.includes(searchTerm);
      });
    });

    // 切换选中状态
    const onToggleSelect = (field: DatasetField) => {
      const index = selectedIds.value.indexOf(field.id);

      // 单选
      if (props.single) {
        selectedIds.value = [field.id];
        onClosePopup();
        return;
      }

      if (index > -1) {
        // 已选中，则取消选中
        selectedIds.value.splice(index, 1);
      } else {
        // 未选中，则添加到选中列表
        selectedIds.value.push(field.id);
      }
    };

    const onClosePopup = () => {
      search.value = '';
      const selectedFields = props.fieldList.filter((field) => selectedIds.value.includes(field.id));
      //判断是否一致
      const isSame = selectedIds.value.length ? selectedIds.value.every((id) => props.activeIds.includes(id)) : false;
      if (!isSame) {
        emit('addField', selectedFields, selectedIds.value);
      }

      // 发送关闭事件
      emit('closePopup');
    };

    const { getDataTypeIcon } = useDataset();
    const getFieldIcon = (fieldDatatype: string) => {
      return 'field-' + getDataTypeIcon(fieldDatatype);
    };

    return {
      rightWidth,
      getFieldIcon,
      search,
      filteredFieldList,
      selectedIds,
      onToggleSelect,
      onClosePopup
    };
  }
});
