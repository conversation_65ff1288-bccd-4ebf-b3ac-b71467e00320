import { Carousel, GraphStyle, QuickStyle, TabOptions, TabResizeType, WidgetType } from '../models';

/**
 * 组件配置工厂类
 * 用于管理不同组件的快速切换样式配置
 * <AUTHOR>
 */
export class WidgetConfigQuickStyleFactory {
  /**
   * 段落
   */
  get [WidgetType.Paragraph]() {
    const style: QuickStyle[] = [];

    return style;
  }

  /**
   * 标题组件配置
   */
  get [WidgetType.Title]() {
    const style: QuickStyle[] = [];

    return style;
  }

  /**
   * 选项卡快速样式切换配置
   */
  get [WidgetType.Tab]() {
    const style = [
      new QuickStyle('多选选项卡', 'multiple', undefined, new TabOptions(true, '1,2'), new GraphStyle()),
      new QuickStyle(
        '滚动选项卡',
        'scrollTab',
        undefined,
        new TabOptions(false, '1', true, TabResizeType.Fixed, undefined, new Carousel()),
        new GraphStyle()
      )
    ];
    return style;
  }

  /**
   * 文本输入组件配置
   */
  get [WidgetType.Input]() {
    const style: QuickStyle[] = [];

    return style;
  }
}
