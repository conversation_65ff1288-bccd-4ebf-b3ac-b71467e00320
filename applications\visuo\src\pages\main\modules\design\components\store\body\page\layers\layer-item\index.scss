@import '../../../../../index';

.#{$vis-prefix}-store-layer {
  @apply p-0;

  &-node,
  &-node__parent {
    @apply p-0;
  }

  &-node.drag-target-parent {
    background-color: $tree-item-hover !important;
  }

  &-node__parent {
    &.selected {
      background-color: $tree-item-selected-lighter;

      > .#{$vis-prefix}-store-layer-header {
        background-color: $tree-item-selected;
      }

      div.#{$vis-prefix}-store-layer-header:hover,
      div.#{$vis-prefix}-store-layer-node__child:hover {
        background-color: transparent;
      }

      > div.#{$vis-prefix}-store-layer-header:hover {
        background-color: $tree-item-selected;
      }
    }

    &:not(.selected) {
      > .#{$vis-prefix}-store-layer-header:hover {
        background-color: $tree-item-hover;
      }
    }
  }

  &-node__child {
    @apply pl-12px;

    &:not(.selected):hover {
      @apply rounded;
      background-color: $tree-item-hover;
    }

    &.selected {
      background-color: $tree-item-selected;
    }
  }

  &-node.drag-target-parent,
  &-node__parent.selected,
  .#{$vis-prefix}-store-layer-header {
    @apply rounded;
  }

  // 选中元素的圆角处理
  &-node__child.selected {
    // 默认情况：单独选中的元素添加4px圆角
    border-radius: 4px;
  }

  // 连续选中的情况处理
  // 当前选中元素的上一个兄弟元素也被选中时，移除当前元素的上圆角
  &-node__child.selected + &-node__child.selected {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  // 使用JavaScript类来标记连续选中的状态，提供更精确的控制
  &-node__child.selected.continuous-start {
    border-radius: 4px 4px 0 0;
  }

  &-node__child.selected.continuous-middle {
    border-radius: 0;
  }

  &-node__child.selected.continuous-end {
    border-radius: 0 0 4px 4px;
  }

  &-header {
    @apply flex flex-nowrap items-center mt-0 p-1px cursor-pointer;

    &-expand {
      @apply flex justify-between;

      &-arrow {
        @apply flex items-center lh-7 h-7 transition-transform transition-duration-300 ease;

        font-size: $primary-font-size;
      }

      &-arrow.rotate {
        @apply rotate-90;
      }
    }

    &-content {
      @apply flex justify-between w-full;

      &-title {
        @apply flex items-center lh-7 h-7;

        font-size: $primary-font-size;

        &-icon {
          @apply flex items-center text-16px mr-2px;
        }

        &-input {
          @apply pl-3px mr-1px bg-#fff rounded;
        }

        &-text {
          @apply lh-7 h-7 w-full ml-3px;

          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;

          // position: relative;
          // line-height: 1.75rem;
          // max-height: 1.75rem; /* 1行的高度 */
          // overflow: hidden;

          // &::after {
          //   content: '...';
          //   position: absolute;
          //   bottom: 0;
          //   right: 0;
          // }
        }
      }

      &-wrapper {
        @apply flex items-center gap-2 mr-5px;
      }

      &-main-frame {
        @apply flex items-center;
      }

      &-actions {
        @apply display-none items-center gap-2;

        .tool_icon {
          // @apply flex opacity-0;
          @apply display-none;
        }

        .tool_icon-active {
          // @apply flex! opacity-100;
          @apply flex!;

          font-size: $primary-font-size;
        }
      }

      &-actions.show {
        @apply flex;
      }
    }

    &.hovered {
      .tool_icon {
        // @apply opacity-100 shadow-[none]! bg-transparent!;
        @apply flex shadow-[none]! bg-transparent!;

        font-size: $primary-font-size;
      }
    }
  }

  &-collapsible {
    @apply grid transition-property-all transition-duration-300 ease overflow-hidden;

    grid-template-rows: 1fr;
  }

  &-collapsible.folder {
    grid-template-rows: 0fr;
  }

  &-children {
    @apply pl-6px min-h-0px;
  }

  &-ghost {
    @apply h-0 overflow-hidden border-b-1px border-b-solid border-b-[#000];
  }

  &-drag {
    @apply h-7.5 overflow-hidden;
  }
}
