import { defineComponent, ref, type PropType } from 'vue';
import { useDesignStore } from '../../../../../../stores';
import type { AxisField } from '@vis/document-core';

/**
 * 字段配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-field-config',
  props: {
    field: {
      type: Object as PropType<AxisField>
    }
  },
  setup(props, { emit }) {
    const { aggregatorFunctions } = useDesignStore();

    const handleBeforeShow = () => {
      emit('before-show');
    };

    const handleBeforeHide = () => {
      emit('before-hide');
    };
    const handleFilter = () => {
      emit('handleFilter');
    };

    const handleSort = (order: 'asc' | 'desc') => {
      emit('handleSort', order);
    };

    const handleAggregator = (fun: string) => {
      emit('handleAggregator', fun);
    };

    const handleRemove = () => {
      emit('handleRemove');
    };

    return {
      aggregatorFunctions,
      handleFilter,
      handleSort,
      handleAggregator,
      handleBeforeShow,
      handleBeforeHide,
      handleRemove
    };
  }
});
