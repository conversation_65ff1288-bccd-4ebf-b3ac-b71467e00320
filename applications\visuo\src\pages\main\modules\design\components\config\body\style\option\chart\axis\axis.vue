<template>
  <vis-text :option="axisOptions.label.fontStyle" mode="base" textTitle="" :mini="true"> </vis-text>
  <!-- 标题 -->
  <div class="vis-form-inline">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__label">轴标题</div>
        <div class="vis-form-field__content">
          <q-btn>Tt</q-btn>
          <vis-mix-Input v-model="axisOptions.title.text"> </vis-mix-Input>
          <!-- <vis-number v-model="axisOptions.label" icon="vis-resizing-w" :min="0" /> -->
        </div>
      </div>
    </div>
    <q-btn class="btn-field">
      <ht-icon class="vis-icon" :name="axisOptions.title.visible ? 'vis-eye-o' : 'vis-eye-c'" />
    </q-btn>
  </div>
  <div class="vis-form-inline">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__label">轴线</div>
        <div class="vis-form-field__content">
          <vis-number v-model="axisOptions.line.width" icon="vis-resizing-w" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">网格</div>
        <div class="vis-form-field__content">
          <vis-number v-model="axisOptions.line.width" icon="vis-resizing-w" :min="0" />
        </div>
      </div>
    </div>
  </div>
  <!-- 网格背景 -->
  <div class="vis-form-inline" v-if="axisOptions.grid.fillPaints.length > 0">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__label">网格背景</div>
        <div class="vis-form-field__content">
          <vis-fill
            v-for="(item, index) in axisOptions.grid.fillPaints"
            v-model="axisOptions.grid.fillPaints[index]"
            :key="index"
            base
            :showEyes="false"
            :minusWidth="0"
            @delete="handleDelete(index)"
          />
        </div>
      </div>
      <!--  <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-fill
            v-model="axisOptions.grid.fillPaints[1]"
            base
            :showEyes="false"
            :minusWidth="0"
            @delete="handleDelete(1)"
          />
        </div>
      </div> -->
    </div>
  </div>
</template>
<script lang="ts" src="./axis.ts"></script>
