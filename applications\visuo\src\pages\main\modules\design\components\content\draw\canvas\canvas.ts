import { useDesignStore } from '../../../../stores';
import { defineComponent, computed } from 'vue';
import VisDesignMoveable from '../moveable/moveable.vue';
import { type Page } from '@vis/document-core';
import VisFrameGridGhost from '../frame-grid-ghost/frame-grid-ghost.vue';

export default defineComponent({
  name: 'vis-design-canvas',
  components: {
    VisDesignMoveable,
    VisFrameGridGhost
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();

    const page = computed(() => {
      return designStore.active.value.page as Page;
    });

    return {
      page
    };
  }
});
