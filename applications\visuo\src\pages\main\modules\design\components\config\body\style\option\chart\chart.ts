import type { BarOptions } from '@vis/document-core';
import { computed, defineComponent, type PropType } from 'vue';
import VisConfigChartAxis from './axis/axis.vue';

export default defineComponent({
  name: 'vis-config-chart-option',
  props: {
    options: {
      type: Object as PropType<BarOptions>,
      required: true
    }
  },
  components: {
    VisConfigChartAxis
  },
  setup(props) {
    const chartOptions = computed(() => props.options);

    /**
     * 切换轴线显示
     * @param axis 轴线
     */
    const handleAxis = (axis: 'xAxis' | 'yAxis') => {
      chartOptions.value[axis].visible = !chartOptions.value[axis].visible;
    };

    return {
      handleAxis,
      chartOptions
    };
  }
});
