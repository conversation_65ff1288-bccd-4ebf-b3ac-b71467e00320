import { Graph, Page, GraphType, GraphBasic, useDocumentStore, Block, WidgetBlock } from '@vis/document-core';
import { scrollDomIntoView } from '@hetu/platform-shared';
import { defineComponent, ref, computed, watch, nextTick, onMounted } from 'vue';
import { useDesignStore, useActionStore } from '../../../../../stores';
import { useGraph, useAction } from '../../../../../hooks';
import { debounce, QScrollArea } from 'quasar';
import { cloneDeep, difference } from 'lodash-es';
import VisLayerItem from './layer-item/index.vue';

/**
 * 图层
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-layer',
  components: {
    VisLayerItem
  },
  props: {
    pagesHeight: {
      type: Number,
      required: true
    }
  },
  setup() {
    // 设计器状态
    const designStore = useDesignStore();
    const active = computed(() => designStore.active.value);
    const activePage = computed(() => active.value.page);
    const activeGraphIds = computed(() => active.value.graphIds);

    // 文档状态
    const docStore = useDocumentStore();
    const document = computed(() => docStore.document.value);
    const blocks = computed(() => document.value.blocks);
    const isEditingStatus = computed(() => !!designStore.editingGraph.value);

    // 右键操作集
    const { actions, contextMenus } = useActionStore();

    // 图层数据
    const layers = ref<Graph[]>([]);
    const layersCopy = ref<Graph[]>([]);
    // 画布状态
    const { activeGraphs, moveGraphs, findGraph, flattenPageFrames } = useGraph();

    // #region 搜索
    const keyWord = ref<string>('');
    const filterRef = ref<HTMLInputElement>();
    const onKeyWordChange = debounce((value: string | number | null): void => {
      // 关键字为空则重置数据，否则筛选数据
      layers.value = !value ? layersCopy.value : filterTree(layersCopy.value, value as string);
    }, 300);

    // 根据关键字筛选树形结构:
    // 1. 子节点匹配时保留父节点及匹配的子节点
    // 2. 父节点匹配时子节点不匹配则不保留子节点
    const filterTree = (nodes: Graph[], keyword: string): Graph[] => {
      if (!keyword) return nodes;
      return nodes.reduce((result: Graph[], node) => {
        const children = node.children ? filterTree(node.children, keyword) : [];
        const isMatch = node.name.includes(keyword);
        if (isMatch || children.length > 0) {
          return [...result, { ...node, children }];
        }
        return result;
      }, []);
    };

    // 重置
    const resetFilter = () => {
      keyWord.value = '';
      filterRef.value?.focus();
    };
    // 搜索显示/隐藏
    const isSearchHide = ref<boolean>(false);
    const onUpdateSearchHide = () => {
      if (keyWord.value) return;
      if (!isSearchHide.value) {
        setTimeout(() => filterRef.value?.focus(), 0);
      } else {
        keyWord.value = '';
      }
      isSearchHide.value = !isSearchHide.value;
    };
    // #endregion

    // #region 展开收起
    // 收集所有有子节点（children）的节点id
    const collectExpandedIds = (graphs: Graph[]) => {
      const ids: string[] = [];
      const traverse = (nodes: Graph[]) => {
        nodes.forEach((node: Graph) => {
          //  && node.children.length > 0
          if (node.children) {
            ids.push(node.id);
            traverse(node.children);
          }
        });
      };

      traverse(graphs);
      return ids;
    };

    // 初始化收集展开状态
    const initExpandedState = (nodes: Graph[]) => {
      // 使用map 记录id 和 展开收起状态
      if (!expandedMap.value.size) {
        collectExpandedIds(nodes).forEach((id) => expandedMap.value.set(id, true));
        return;
      } else {
        const newIds = collectExpandedIds(nodes);
        const oldIds = Array.from(expandedMap.value.keys());
        if (newIds.length === oldIds.length) return;
        if (newIds.length > oldIds.length) {
          const addIds = difference(newIds, oldIds);
          addIds.forEach((id) => expandedMap.value.set(id, true));
        } else {
          const delIds = difference(oldIds, newIds);
          delIds.forEach((id) => expandedMap.value.delete(id));
        }
      }
    };

    const expandedMap = ref(new Map<string, boolean>());
    const isExpandHide = ref<boolean>(true);
    // 展开/收起全部
    const onUpdateExpandAllHide = () => {
      isExpandHide.value = !isExpandHide.value;
      if (isExpandHide.value) {
        expandedMap.value.forEach((value, key) => expandedMap.value.set(key, true));
      } else {
        expandedMap.value.forEach((value, key) => expandedMap.value.set(key, false));
      }
    };
    // 展开/收起单个
    const onUpdateExpandHide = (node: Graph) => {
      expandedMap.value.set(node.id, !expandedMap.value.get(node.id));
    };
    // #endregion

    // #region 拖拽
    const isDragging = ref<boolean>(false);
    const dragTargetParentId = ref<string | null>(null);
    const onDragStart = (evt: any) => {
      isDragging.value = true;
      dragTargetParentId.value = null;
      const element = evt.item['__draggable_context'].element; // 当前拖拽元素
      // 开始拖拽时计算所有的容器
      flattenPageFrames();
      nextTick(() => activeGraphs([element.id]));
    };

    const onDragEnd = (evt: any) => {
      // console.log(evt, 'onDragEnd', dragTargetParentId.value, 'dragTargetParentId');
      isDragging.value = false;
      dragTargetParentId.value = null;

      // 同级拖拽
      const element = evt.item['__draggable_context'].element; // 当前拖拽元素
      // 同级拖拽顺序需要手动更新
      const graph: Graph | Page | undefined = element.parent ? findGraph(element.parent) : activePage.value; // 拖拽元素父级
      const graphChildren = graph!.children;

      // 在位置中删除图形
      const index = graphChildren!.findIndex((item) => item.id === (<Graph>element).id);
      if (index !== -1) {
        graphChildren!.splice(index, 1);
      }

      // 插入到新的位置
      graphChildren!.splice(evt.newIndex, 0, element);
      graphChildren!.forEach((graph, index, _) => (graph.order = _.length - 1 - index)); // 更新order
    };

    const onDragAdd = (evt: any) => {
      // console.log(evt, 'onDragAdd');
      const element = evt.item['__draggable_context'].element;
      // 如果为主容器，清空主容器
      if (activePage.value.main === element.id) {
        activePage.value.main = '';
      }
      const targetId = evt.to.dataset.itemKey;
      moveGraphs([element], targetId, evt.newIndex);
    };

    const onCheckMove = (evt: any) => {
      // console.log('onCheckMove:', evt);
      // 检测是否为嵌套拖拽（跨容器拖拽）
      dragTargetParentId.value = null;

      if (evt.from && evt.to && evt.from !== evt.to) {
        // 获取目标容器的 data-item-key 属性，即父节点的ID
        const targetParentId = evt.to.dataset?.itemKey;
        if (targetParentId) {
          // 设置目标父节点ID
          dragTargetParentId.value = targetParentId;
        }
      }
    };
    // #endregion

    // #region 选中高亮 - 支持多选
    const selectedIds = ref<string[]>([]);
    const onUpdateSelected = (id: string, $event: PointerEvent) => {
      if ($event.ctrlKey || $event.metaKey) {
        if (!selectedIds.value.includes(id)) {
          selectedIds.value.push(id);
        } else {
          selectedIds.value = selectedIds.value.filter((i) => i !== id);
        }
      } else {
        selectedIds.value = [id];
      }

      nextTick(() => {
        activeGraphs([...selectedIds.value]);
      });
    };
    // #endregion

    // #region 右键菜单
    const contextMenuRef = designStore.contextMenuRef;
    const onOpenContextMenu = (node: Graph, event: MouseEvent) => {
      // 设置为主容器条件：1 根容器（parent为""）；2 容器（Frame）类型；
      actions.value['setAsMainFrame'].disable = !(!node.parent && node.type === GraphType.Frame);
      // 取消容器条件：1 容器类型；
      actions.value['cancelFrame'].disable = !(node.type === GraphType.Frame);
      // 图层无粘贴到鼠标位置
      actions.value['pasteToMousePosition'].disable = true;

      nextTick(() => {
        activeGraphs([node.id]);

        contextMenuRef.value?.open({
          menus: contextMenus.graph,
          x: event.clientX,
          y: event.clientY,
          action: (actionKey: string) => {
            (useAction() as any)[actionKey] && (useAction() as any)[actionKey]();
          }
        });
      });
    };
    // #endregion

    // #region 滚动当前 selected 状态到可见区域
    const scrollAreaRef = ref<QScrollArea>();

    // 递归展开父级节点，并返回true
    const expandParentByIds = (graphIds: string[]) => {
      graphIds.forEach((graphId) => {
        let currentId = graphId;
        while (currentId) {
          const node = findGraph(currentId);
          if (!node?.parent) break;
          expandedMap.value.set(node.parent, true);
          currentId = node.parent;
        }
      });
      return true;
    };

    // 判断元素是否在可视区内
    const isElementView = (el: HTMLElement, target: HTMLElement) => {
      const rect = el.getBoundingClientRect();
      return rect.top >= 0 && rect.left >= 0 && rect.bottom <= target.clientHeight && rect.right <= target.clientWidth;
    };

    // 滚动到可视区
    const scrollSelectedIntoView = () => {
      setTimeout(() => {
        const scrollbar = scrollAreaRef.value?.getScrollTarget();
        const activeDoms = scrollbar?.getElementsByClassName('selected');
        if (activeDoms?.length) {
          const isView = isElementView(activeDoms.item(0)! as HTMLElement, scrollbar as HTMLElement);
          !isView && scrollDomIntoView(activeDoms.item(0)!, scrollbar!);
        }
      }, 100);
    };

    // #endregion

    // #region 递归设置 BLOCK 组件的名称（name）
    const blockLength = computed(() => blocks.value.length);
    const initBlockName = (nodes: Graph[], block: WidgetBlock[]) => {
      nodes.forEach((node: Graph) => {
        if (node.type === GraphType.Block && !node.name) {
          const targetBlock = block.find((b) => b.id === (node as Block).decoration);
          node.name = targetBlock ? targetBlock.name : '';
        }
        node.children && initBlockName(node.children, block);
      });
    };
    // #endregion

    // #region 渲染图层
    const renderLayer = (nodes: Graph[]) => {
      // 更新副本数据
      layersCopy.value = cloneDeep(nodes);
      // 同步图层树 支持搜索
      layers.value = !keyWord.value ? layersCopy.value : filterTree(layersCopy.value, keyWord.value as string);
    };
    // #endregion

    // #region 初始化选中
    const initActiveGraphIds = (graphIds: string[]) => {
      // 同步图层树选中元素的id
      selectedIds.value = graphIds;
      // 是否选中元素 - 展开父级节点 - 滚动到可视区
      graphIds.length > 0 && expandParentByIds(graphIds) && scrollSelectedIntoView();
    };

    // #endregion

    // #region 监听画布 同步图层树
    watch(
      activePage,
      debounce((page, o_page) => {
        // 同步图层树
        renderLayer(page.children);
        // 切换页面/初始化-收集展开状态
        page !== o_page && expandedMap.value.clear();
        initExpandedState(page.children);
      }, 100),
      {
        deep: true
      }
    );

    watch(activeGraphIds, (graphIds) => {
      initActiveGraphIds(graphIds);
    });
    // #endregion

    // #region 监听BLOCK新增 同步name
    watch(
      blockLength,
      () => {
        initBlockName(activePage.value.children, blocks.value);
      },
      {
        immediate: true
      }
    );
    // #endregion

    onMounted(() => {
      nextTick(() => {
        // 初始化图层树
        renderLayer(activePage.value.children);
        // 初始化展开状态
        initExpandedState(activePage.value.children);
        // 初始化选中
        initActiveGraphIds(activeGraphIds.value);
      });
    });

    return {
      layers,
      keyWord,
      filterRef,
      onKeyWordChange,
      resetFilter,
      isSearchHide,
      onUpdateSearchHide,
      isDragging,
      onDragStart,
      onDragEnd,
      onDragAdd,
      onCheckMove,
      selectedIds,
      onUpdateSelected,
      expandedMap,
      isExpandHide,
      onUpdateExpandAllHide,
      onUpdateExpandHide,
      scrollAreaRef,
      dragTargetParentId,
      onOpenContextMenu,
      contextMenuRef,
      isEditingStatus
    };
  }
});
