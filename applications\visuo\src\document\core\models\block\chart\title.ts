import { Effects, FillPaints, Stroke, Text } from '../../ui';

/**
 * 图表标题类
 * 用于定义图表的标题样式、位置和效果
 * <AUTHOR>
 */
export class Title {
  /** 是否显示标题 */
  visible: boolean = false;

  /** 标题文本内容 */
  text: string = '';

  /** 标题位置：顶部 | 底部 | 左侧 | 右侧 | 居中 */
  position: 'top' | 'bottom' | 'left' | 'right' | 'center' = 'top';

  /** 标题与轴之间的间距 */
  spacing: number = 0;

  /** 标题旋转角度（度） */
  angle: number = 0;

  /** 水平偏移量 */
  offsetX: number = 0;

  /** 垂直偏移量 */
  offsetY: number = 0;

  /** 整体透明度（0-1） */
  opacity: number = 1;

  /** 文字样式，包括：字体、大小、粗细、行高、水平对齐、垂直对齐 */
  fontStyle: Text = new Text();

  /** 文字颜色填充 */
  fillPaints: FillPaints = new FillPaints();

  /** 描边样式 */
  stroke: Stroke = new Stroke();

  /** 阴影特效 */
  effects: Effects = new Effects();
}
