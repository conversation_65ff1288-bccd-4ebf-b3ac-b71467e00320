/**
 * 处理变量相关的方法
 * <AUTHOR>
 */
export const useVars = () => {
  /**
   * 获取配置项中的变量
   * @param
   */
  const getOptionsVars = (option: string) => {
    let vars: string[] = [];
    const regex = /\{var_(.+?)\}/g;
    const matcheds = option.match(regex);
    if (matcheds?.length) {
      vars = matcheds?.map((m) => m.replace('{var_', '').replace('}', ''));
    }
    return Array.from(new Set(vars));
  };

  return {
    getOptionsVars
  };
};
