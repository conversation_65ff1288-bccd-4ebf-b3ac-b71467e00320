<template>
  <div class="vis-surface h-full">
    <div class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__label" v-if="isShowTitle">外观</div>
        <div class="vis-form-field__content">
          <div class="vis-form-inline">
            <div :class="`vis-form-inline__content--minus-${minusWidth}`">
              <div class="flex vis-fill__content" :class="{ 'vis-invisible': !visible }">
                <!-- 颜色按钮 -->
                <q-btn class="vis-field--mini" @click.stop="showPopup">
                  <q-icon
                    class="cursor-pointer vis-fill__icon"
                    :class="isEmptyImage ? 'hticon-vis-image' : 'hticon-vis-rect'"
                    :style="iconStyle"
                  ></q-icon>
                </q-btn>

                <!-- 输入框 -->
                <div class="flex-1 row items-center pl-1">
                  <template v-if="isSolid">
                    <q-input
                      ref="colorRef"
                      borderless
                      dense
                      :model-value="hexColor"
                      @focus="focusColor"
                      @keypress.enter="updateColor"
                      class="vis-field--mini w-[calc(100%-41px)]"
                    />
                    <q-separator vertical inset class="!m-0" />
                    <q-input
                      borderless
                      dense
                      v-model="alpha"
                      @focus="focusAlpha"
                      @keypress.enter="updateAlpha"
                      @blur="updateAlpha"
                      class="vis-field--mini w-32px mx-1"
                    />
                  </template>
                  <template v-else-if="isGradient">
                    <q-input
                      borderless
                      dense
                      :modelValue="fillTypeName"
                      readonly
                      class="vis-field--mini w-[calc(100%-41px)]"
                    />
                    <q-separator vertical inset class="!m-0" />
                    <q-input
                      ref="alphaRef"
                      borderless
                      dense
                      v-model="alpha"
                      @focus="focusAlpha"
                      @keypress.enter="updateAlpha"
                      @blur="updateAlpha"
                      class="vis-field--mini w-32px mx-1"
                    />
                  </template>

                  <q-input v-else borderless :modelValue="fillTypeName" readonly class="vis-field--mini col-12" />
                </div>
              </div>
            </div>
            <!-- 边框设置按钮 -->
            <q-btn :class="{ active: positionIndex === 5 }" class="vis-field--mini btn-field">
              <img class="img-icon" :src="`./static-next/svg/stroke/active-${strokeIcon}.svg`" />
              <q-menu class="vis-menu" style="width: 120px">
                <q-list>
                  <q-item v-for="item in strokeOptions" :key="item.value" :active="item.active" clickable v-close-popup>
                    <q-item-section class="!flex-row items-center !justify-start" @click="handleStrokeChange(item)">
                      <img
                        class="img-icon mr-2"
                        :src="`./static-next/svg/stroke/${item.active ? 'active-' + item.icon : item.icon}.svg`"
                      />

                      <q-item-label class="w-8">
                        {{ item.label }}
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-separator class="!my-2" />
                  <q-item clickable v-close-popup>
                    <q-item-section
                      @click="handleStrokeChange({ icon: 'border-a', value: 5, active: false })"
                      class="!flex-row items-center !justify-start"
                    >
                      <ht-icon class="vis-icon mr-2 ml-1" name="hticon-vis-control" />
                      <q-item-label> 自定义 </q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <!-- 显隐按钮 -->
            <q-btn v-if="!onlyColor && isShowEyes" class="vis-field--mini btn-field" flat @click="handleVisible">
              <ht-icon class="vis-icon" :name="visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
            </q-btn>
          </div>
        </div>
      </div>
    </div>

    <div class="vis-form-inline" v-if="positionIndex === 5">
      <div :class="`vis-form-inline__content--minus-32`">
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[0]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-t.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[1]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-r.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
      </div>
    </div>

    <div class="vis-form-inline" v-if="positionIndex === 5">
      <div :class="`vis-form-inline__content--minus-32`">
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[2]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-b.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[3]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-l.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./index.ts" lang="ts"></script>
