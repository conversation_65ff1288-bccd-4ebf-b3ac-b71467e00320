<template>
  <vis-design-frame v-if="graph.type === GraphType.Frame" :graph="graph" :parent="parent" />
  <div v-else :className="className" :id="graph.id" :parent="graph.parent" :style="style">
    <div class="vis-graph-mask" v-if="graph.type === GraphType.Block"></div>
    <component :is="componentName" :graph="graph"></component>
  </div>
</template>

<script lang="ts" src="./entrance.ts"></script>
