import type { Records } from '@hetu/util';
import { WidgetBase, type WidgetGroup } from '../../../../base/models';
import type { AxisField } from '../data/axis-field';
import { UUID } from '@hetu/platform-shared';
import { WidgetSubGroup, WidgetType } from './widget-enum';

/**
 * 组件配置
 * <AUTHOR>
 */
export class WidgetConfig extends WidgetBase {
  /** 宽度 */
  width = 300;
  /** 高度 */
  height = 300;
  /** 子分组 */
  subGroup: WidgetSubGroup = WidgetSubGroup.Text;
  /** 属性 */
  options?: any;

  get record(): Records<WidgetConfig> {
    const record: Records<WidgetConfig> = {};
    record[this.type] = this;
    return record;
  }

  constructor(
    type?: string,
    title?: string,
    name?: string,
    group?: string | string[],
    subGroup?: WidgetSubGroup,
    width?: number,
    height?: number
  ) {
    super(type, title, name, group);
    width && (this.width = width);
    height && (this.height = height);
    subGroup && (this.subGroup = subGroup);
  }
}

/**
 * 组件数据匹配配置
 * <AUTHOR>
 */
export interface WidgetConfigDataMatch {
  /** 名称 */
  name: string;
  /** 字段 */
  field: AxisField[];
  /** 是否必填 */
  required?: boolean;
  /** 最少字段要求 */
  minLen?: number;
}

/**
 * 组件交互配置，用于约束组件交互配置的类型
 * <AUTHOR>
 */
export interface IWidgetConfigInteract {
  /** 事件 */
  events?: Records<WidgetConfigEvent> | boolean;
  /** 动作 */
  actions?: Records<WidgetConfigAction> | boolean;

  drilldown?: boolean;
}

/**
 * 组件交互配置
 * <AUTHOR>
 */
export class WidgetConfigInteract implements IWidgetConfigInteract {
  /** 事件 */
  events: IWidgetConfigInteract['events'];
  /** 动作 */
  actions: IWidgetConfigInteract['actions'];
  /** 过滤器 */
  filters = false;
  /** 下钻 */
  drilldown = false;

  constructor(events?: Records<WidgetConfigEvent>, actions?: Record<string, string>, drilldown?: boolean) {
    events && (this.events = events);
    actions && (this.actions = Object.assign(actions, this.actions));
    drilldown && (this.drilldown = drilldown);
  }

  /**
   * 获取多个事件
   * @param key 事件key
   * @param events 事件数据
   */
  getMultiEvents(key: string, events: any[]) {
    const multiEvents = events.filter((event) => event.type === key);
    const records: Records<WidgetConfigEvent> = {};
    const config = (this.events as Records<WidgetConfigEvent>)[key];
    multiEvents.forEach((event: any) => {
      const eventKey = [event.type, event.target].join('-');
      records[eventKey] = <WidgetConfigEvent>Object.assign({}, config, { name: event.name });
    });
    return records;
  }

  /**
   * 添加支持动态数据组件的默认事件
   */
  setRequestEvents() {
    let events: Records<WidgetConfigEvent> = {};
    switch (typeof this.events) {
      case 'object':
        events = this.events as Records<WidgetConfigEvent>;
        break;
      case 'string': {
        const name = this.events;
        events[name] = new WidgetConfigEvent(name);
        break;
      }
    }
    if (Array.isArray(this.events)) {
      this.events.forEach((name) => {
        typeof name === 'string' && (events[name] = new WidgetConfigEvent(name));
      });
    }
    this.events = Object.assign(events, {});
  }
}

/**
 * 事件动作数据定义
 * <AUTHOR>
 */
export class WidgetConfigDataDefine {
  /** 字段 */
  field = '';
  /** 名称 */
  name = '';
  /** 字段类型 */
  type = '';
  /** 说明 */
  description = '';
  /** 必填 */
  required = true;
  /** 默认值 */
  defaultValue: any = '';
  /** 绝对唯一值 (为以防万一, 请使用 `key`, 而不是 `field` 作为唯一值) */
  key = '';

  constructor(field: string, name: string, type: string, description?: string, required?: boolean, defaultValue?: any) {
    this.key = UUID();
    this.field = field;
    this.name = name;
    this.type = type;
    required !== undefined && (this.required = required);
    defaultValue !== undefined && (this.defaultValue = defaultValue);
    description !== undefined && (this.description = description);
  }
}

/**
 * 事件配置
 * <AUTHOR>
 */
export class WidgetConfigEvent {
  /** 事件名称 */
  name?: string;
  /** 事件类型 */
  type?: string;
  /** 说明 */
  description = '';
  /** 数据定义 */
  dataDefine?: WidgetConfigDataDefine;
  /** 数据示例 */
  dataExample?: any;
  /** 是否有多个 */
  multi?: boolean;

  constructor(name: string, description?: string, dataDefine?: any, dataExample?: any) {
    this.name = name;
    description !== undefined && (this.description = description);
    dataDefine !== undefined && (this.dataDefine = dataDefine);
    dataExample !== undefined && (this.dataExample = dataExample);
  }
}

/**
 * 动作配置
 * <AUTHOR>
 */
export class WidgetConfigAction {
  /** 动作名称 */
  name?: string;
  /** 动作参数 */
  params?: any;
  /** 说明 */
  description?: string = '';
  /** 数据定义 */
  dataDefine?: WidgetConfigDataDefine[];
  /** 数据示例 */
  dataExample?: any;

  constructor(name: string, description?: string, dataDefine?: any, dataExample?: any, params?: any) {
    this.name = name;
    params !== undefined && (this.params = params);
    description !== undefined && (this.description = description);
    dataDefine !== undefined && (this.dataDefine = dataDefine);
    dataExample !== undefined && (this.dataExample = dataExample);
  }
}
