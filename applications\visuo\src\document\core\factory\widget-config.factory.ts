import type { Records } from '@hetu/util';
import { Tab, WidgetConfig } from '../models';
import { WidgetName, WidgetGroup, WidgetSubGroup, WidgetType } from '../models/block/config/widget-enum';

/**
 * 组件配置工厂类
 * 用于管理和创建各种组件的配置信息
 * <AUTHOR>
 */
export class WidgetConfigFactory {
  private _configs: Records<WidgetConfig> = {};

  constructor() {
    this._configs = this.createConfigs();
  }

  /**
   * 获取所有组件配置
   */
  public get configs(): Records<WidgetConfig> {
    return this._configs;
  }

  /**
   * 获取指定组件的配置
   * @param type 组件名称
   */
  public getConfig(type: WidgetType): WidgetConfig | undefined {
    return this._configs[type];
  }

  /**
   * 创建组件配置
   * @private
   */
  private createConfigs(): Records<WidgetConfig> {
    const configs: Records<WidgetConfig> = {};

    // 段落组件配置
    configs[WidgetType.Paragraph] = new WidgetConfig(
      WidgetType.Paragraph,
      WidgetName.Paragraph,
      WidgetName.Paragraph,
      WidgetGroup.Words,
      WidgetSubGroup.Interactive,
      180,
      100
    );

    // 标题组件配置
    configs[WidgetType.Title] = new WidgetConfig(
      WidgetType.Title,
      WidgetName.Title,
      WidgetName.Title,
      WidgetGroup.Words,
      WidgetSubGroup.Interactive,
      150,
      50
    );

    // 选项卡组件配置
    configs[WidgetType.Tab] = new WidgetConfig(
      WidgetType.Tab,
      WidgetName.Tab,
      WidgetName.Tab,
      WidgetGroup.Words,
      WidgetSubGroup.Interactive,
      400,
      50
    );

    // 文本输入组件配置
    configs[WidgetType.Input] = new WidgetConfig(
      WidgetType.Input,
      WidgetName.Input,
      WidgetName.Input,
      WidgetGroup.Words,
      WidgetSubGroup.Form,
      240,
      40
    );

    // 柱状图组件配置
    configs[WidgetType.Bar] = new WidgetConfig(
      WidgetType.Bar,
      WidgetName.Bar,
      WidgetName.Bar,
      WidgetGroup.Chart,
      WidgetSubGroup.Bar,
      300,
      200
    );

    return configs;
  }
}
