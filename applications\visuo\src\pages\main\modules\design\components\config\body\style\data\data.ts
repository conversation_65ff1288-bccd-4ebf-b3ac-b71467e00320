import { computed, defineComponent, nextTick, onBeforeMount, onMounted, ref, watch } from 'vue';
import { AggregatorFunction, DatasetField, DatasetService, type DatasetTreeNode } from '@hetu/metadata-shared';
import {
  Block,
  Graph,
  useDocumentStore,
  WidgetBlock,
  WidgetType,
  type WidgetConfigDataMatch,
  AxisField,
  useWidgetConfigStore,
  type FilterAxisField
} from '@vis/document-core';
import { useDesignStore } from '../../../../../stores';
import { useQuasar } from 'quasar';
import VisDatasetTree from './dataset/dataset.vue';
import VisFieldList from './field-list/field-list.vue';
import VisFieldItem from './field-item/field-item.vue';
import VisDataSheet from './data-sheet/data-sheet.vue';
import { useDataset } from '../../../../../hooks';

/**
 * 数据配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-data',
  components: {
    VisDataSheet,
    VisDatasetTree,
    VisFieldList,
    VisFieldItem
  },
  setup() {
    const { active, rightWidth, aggregatorFunctions, visDataSheetRef } = useDesignStore();

    const activeGraph = computed(() => active.value.graph as Graph);

    const docStore = useDocumentStore();

    // 获取当前选中的组件
    const activeBlock = computed(() => {
      const blockId = (activeGraph.value as Block)?.decoration;
      if (!blockId) return null;
      return (docStore.document.value.blocks.find((b) => b.id === blockId) as WidgetBlock) || null;
    });

    const filters = computed(() => {
      if (!activeBlock.value?.filters) return [];
      return activeBlock.value.filters;
    });

    // 选择数据资源
    const selectDataset = (data: DatasetTreeNode) => {
      if (!activeBlock.value) return;
      activeBlock.value.datasetId = data.id;
      activeBlock.value.datasetType = data.datasetType || '';
      loadFieldList();
    };

    const datasetName = ref('');

    const fieldMapping = ref<{ datasetName: string; datasetField: DatasetField[] }>();

    const { getDataTypeIcon, getStaticDataField } = useDataset();

    // 获取字段列表
    const loadFieldList = async () => {
      if (!activeBlock.value?.datasetId) return;
      if (activeBlock.value.datasetType === 'static') {
        const datasetField = (await getStaticDataField(activeBlock.value.datasetId)).sort(
          (a: DatasetField, b: DatasetField) => a.fieldType?.localeCompare(b.fieldType || '') || 0
        );
        const res = {
          datasetField: datasetField,
          datasetName: activeBlock.value.datasetId === 'STATIC_DATA' ? '默认数据' : activeBlock.value.datasetId
        };
        datasetName.value = res.datasetName;

        fieldMapping.value = res;
        return;
      }
      DatasetService.getFieldListById(activeBlock.value.datasetId).then((res) => {
        if (res) {
          res.datasetField = res.datasetField?.sort((a, b) => a.fieldType?.localeCompare(b.fieldType || '') || 0);
          fieldMapping.value = res;
          datasetName.value = res.datasetName;
        }
      });
    };

    const { dataConfigs } = useWidgetConfigStore();

    const widgetDataConfig = computed(() => {
      if (!activeBlock.value?.type) return {};
      const config = dataConfigs.get(activeBlock.value.type as WidgetType);
      return (config?.matchs as Record<string, WidgetConfigDataMatch>) || {};
    });

    /**
     * 加载聚合函数
     */
    const loadAggFun = () => {
      if (!activeBlock.value?.datasetId) return;
      if (activeBlock.value.datasetType === 'static') {
        aggregatorFunctions.value = [
          new AggregatorFunction('sum', '求和', 'sum', 1, 1),
          new AggregatorFunction('avg', '平均值', 'avg', 2, 1),
          new AggregatorFunction('min', '最小值', 'min', 3, 1),
          new AggregatorFunction('max', '最大值', 'max', 4, 1),
          new AggregatorFunction('count', '计数', 'count', 5, 1)
        ];
        return;
      }
      DatasetService.getFunctions(activeBlock.value.datasetId).then((data) => {
        aggregatorFunctions.value = data;
      });
    };

    const getFieldIcon = (field: AxisField) => {
      console.log(field);
      return field.sortDir ? field.sortDir : 'field-' + getDataTypeIcon(field.fieldDatatype);
    };

    // 删除过滤字段
    const removeFilterField = (index: number) => {
      if (activeBlock.value?.filters && activeBlock.value.filters.length > index) {
        activeBlock.value.filters.splice(index, 1);
      }
    };

    const refreshTypeOptions = [
      {
        label: '不刷新',
        value: 'none'
      },
      {
        label: '受控模式',
        value: 'control'
      },
      {
        label: '自动刷新',
        value: 'auto'
      }
    ];

    const filterActiveIds = computed(
      () => activeBlock.value?.filters?.map((item: AxisField) => item.id || item.fieldName) || []
    );

    const addFilterField = (fields: FilterAxisField[]) => {
      !activeBlock.value?.filters && (activeBlock.value!.filters = []);

      // 获取可用字段的ID列表
      const availableFieldIds = fields.map((field) => field.id);

      // 移除不存在的过滤器字段
      (<WidgetBlock>activeBlock.value).filters = (activeBlock.value?.filters || []).filter((filter: AxisField) =>
        availableFieldIds.includes(filter.id || '')
      );

      // 添加新的字段
      fields.forEach((field: FilterAxisField) => {
        const existingFilterIndex = activeBlock.value?.filters.findIndex((filter: AxisField) => filter.id === field.id);

        if (existingFilterIndex === -1) {
          activeBlock.value?.filters.push({
            ...field
          });
        }
      });
    };

    // 打开过滤弹窗
    const openFilterDialog = (index: number) => {
      // console.log(dataMapping.value['filters'][index]);
    };

    const $q = useQuasar();

    const openDataSheetDialog = async () => {
      if (visDataSheetRef.value) {
        (visDataSheetRef.value as any).centerDialog();
        return;
      }

      // 打开data-sheet
      await $q.dialog({
        component: VisDataSheet,
        componentProps: {
          modelValue: docStore.staticDatas.value,
          datasetId: activeBlock.value?.datasetId,
          onClose: async (data: any) => {
            docStore.staticDatas.value = data;
            // 关闭后重置状态
            visDataSheetRef.value = undefined;
          },
          onShow: (instance: any) => {
            visDataSheetRef.value = instance;
          },
          update: (data: any) => {
            console.log(data);
          }
        }
      });
    };

    watch(
      () => activeBlock.value?.datasetId,
      () => {
        if (activeBlock.value?.datasetId) {
          loadFieldList();
          loadAggFun();
          activeBlock.value!.dataMapping = [];
        }
      }
    );

    onBeforeMount(async () => {
      if (activeBlock.value?.datasetId) {
        await loadFieldList();
        loadAggFun();
      }
    });

    return {
      docStore,
      rightWidth,
      activeBlock,
      selectDataset,
      filters,
      fieldMapping,
      datasetName,
      widgetDataConfig,
      removeFilterField,
      getFieldIcon,
      refreshTypeOptions,
      openFilterDialog,
      openDataSheetDialog,
      visDataSheetRef,
      filterActiveIds,
      addFilterField
    };
  }
});
