import {
  computed,
  defineComponent,
  ref,
  type CSSProperties,
  type PropType,
  onMounted,
  watch,
  nextTick,
  onBeforeUnmount
} from 'vue';
import {
  AxisField,
  Block,
  DirectionType,
  FillPaints,
  IconPosition,
  ImageType,
  Tab,
  TabResizeType,
  Text,
  TextAlign,
  VerticalAlign,
  StrokeAlign,
  TabStyle,
  IconOption,
  type TabData
} from '../../../../models';
import { useBase, useFill, useUiStyle } from '../../../../hooks';
import { AttachmentService } from '@hetu/platform-shared';

/**
 * <AUTHOR>
 * 选项卡组件
 */
export default defineComponent({
  name: 'vis-tab',
  props: {
    widget: {
      type: Object as PropType<Tab>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {
    const { getStrokeStyle, getEffectStyle, getTextStyle, getTextEffectStyle, getTextOverflow } = useUiStyle();
    const { getFillPaintsStyle } = useFill();
    const { getWidgetFieldData, loadStaticData, fields } = useBase();

    const data = ref<TabData[]>([]);

    const tabBlock = computed(() => props.block);
    const tabOption = computed(() => props.widget.options);

    const layout = computed(() => tabOption.value.layout);
    const marqueeRefs = ref<HTMLElement[]>([]);

    const scrollRef = ref();

    const tabContentRef = ref<HTMLElement[]>([]);

    const changLineRefs = ref<HTMLElement[]>([]);

    const tabRef = ref<HTMLElement[]>([]);

    const hoverIndex = ref(-1);

    //#region 选中
    const getIndex = () => {
      if (typeof tabOption.value.defaultIndex === 'number') return [tabOption.value.defaultIndex - 1];
      if (tabOption.value.multiple) {
        // 多选
        return tabOption.value.defaultIndex
          .split(',')
          .map(Number)
          .filter((num) => !isNaN(num))
          .map((num) => num - 1);
      } else {
        // 单选
        const defaultArr = tabOption.value.defaultIndex.split(',');
        if (defaultArr.length) {
          // 取第一个
          return isNaN(Number(defaultArr[0])) ? [] : [Number(defaultArr[0]) - 1];
        } else {
          // 没有项返回空数组
          return [];
        }
      }
    };
    /**
     * 选中项
     */
    const selectedIndexs = ref(getIndex());

    /**
     * 偏移距离
     */
    const offset = computed(() => {
      if (!tabOption.value.autoPosition) return 0;
      const {
        direction,
        gutter: [verticalGutter, horizontalGutter],
        flowWrap
      } = layout.value;
      if (direction === DirectionType.Grid) return 0;
      const isHorizontal = direction === DirectionType.Horizontal;

      const wrapperSize = isHorizontal ? props.block.width : props.block.height;
      const tabSize = isHorizontal ? tabOption.value.width : tabOption.value.height;
      const gutter = isHorizontal ? horizontalGutter : verticalGutter;
      const resizeType = isHorizontal ? tabOption.value.resizeX : tabOption.value.resizeY;

      const activeIndex = selectedIndexs.value.length ? selectedIndexs.value[0] : 0;
      if (!data.value[activeIndex]) return 0;

      if (resizeType === TabResizeType.Fixed) {
        if (flowWrap && isHorizontal) return 0;

        const allTabSize = tabSize * data.value.length + (data.value.length - 1) * gutter;
        if (allTabSize <= wrapperSize) return 0;

        if (wrapperSize < tabSize) {
          const selectedItemCenter = activeIndex * (tabSize + gutter) + tabSize / 2;
          return wrapperSize / 2 - selectedItemCenter;
        }

        if (activeIndex === 0) return 0;

        const selectedItemCenter = activeIndex * (tabSize + gutter) + tabSize / 2;
        const offset = wrapperSize / 2 - selectedItemCenter;

        const minOffset = 0;
        const maxOffset = wrapperSize - allTabSize;

        if (offset > minOffset) return minOffset;
        if (offset < maxOffset) return maxOffset;
        return offset;
      }

      return 0;
    });

    const handleActive = (idx: number) => {
      if (tabOption.value.multiple) {
        // 多选
        if (selectedIndexs.value.includes(idx)) {
          const index = selectedIndexs.value.findIndex((item) => item === idx);
          index != -1 && selectedIndexs.value.splice(index, 1);
        } else {
          selectedIndexs.value.push(idx);
        }
      } else {
        // 单选
        if (selectedIndexs.value.includes(idx)) return;
        selectedIndexs.value = [idx];

        if (tabOption.value.carousel) {
          // 设置点击停留
          isManualClick = true;
          clickEndTime = Date.now() + tabOption.value.carousel.clickTime * 1000;

          // 重新启动轮播（会优先使用clickTime）
          startCarousel();
        }
      }
    };

    watch(
      () => tabOption.value.defaultIndex,
      () => {
        selectedIndexs.value = getIndex();
      }
    );

    watch(
      () => tabOption.value.multiple,
      (val) => {
        if (!val) {
          selectedIndexs.value = getIndex();
        }
      }
    );

    watch(
      () => offset.value,
      (val) => {
        if (tabOption.value.layout.scrollbar) {
          const isHorizontal = layout.value.direction === DirectionType.Horizontal;
          scrollRef.value?.setScrollPosition(isHorizontal ? 'horizontal' : 'vertical', Math.abs(val), 300);
        }
      }
    );
    //#endregion 选中

    /**
     * 跑马灯滚动速度值
     */
    const SPEED = 30;

    /**
     * 存储每个tab的是否溢出
     */
    const textOverflowFlags = ref<boolean[]>(Array((props.widget as any)?.staticData?.length || 0).fill(false));

    const allStyle = computed(() => {
      // 容器是否设置边框
      const hasStroke =
        props.block.stroke &&
        (props.block.stroke.align === StrokeAlign.Inside || props.block.stroke.align === StrokeAlign.Center);
      // 容器边框宽度
      const strokeWidth = props.block.stroke ? props.block.stroke.position : [0, 0, 0, 0];

      const heightResize = tabOption.value.resizeY === TabResizeType.Adapt;
      const widthResize = tabOption.value.resizeX === TabResizeType.Adapt;

      return {
        height: heightResize ? '100%' : `calc( 100% - ${hasStroke ? strokeWidth[0] + strokeWidth[2] : 0}px )`,
        width: widthResize ? '100%' : `calc( 100% - ${hasStroke ? strokeWidth[1] + strokeWidth[3] : 0}px )`,
        marginLeft: widthResize ? '0px' : hasStroke ? (strokeWidth[1] + strokeWidth[3]) / 2 + 'px' : '0px',
        marginTop: heightResize ? '0px' : hasStroke ? (strokeWidth[0] + strokeWidth[2]) / 2 + 'px' : '0px'
      } as CSSProperties;
    });

    const tabStyle = computed(() => {
      let grid;
      if (layout.value.direction === DirectionType.Grid) {
        grid = {
          display: 'grid',
          gridTemplateColumns: `repeat(${layout.value.column}, ${
            tabOption.value.resizeX === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.width + 'px'
          })`,
          gridTemplateRows: `repeat(${layout.value.row},${
            tabOption.value.resizeY === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.height + 'px'
          })`,
          gridColumnGap: layout.value.gutter[1] + 'px',
          columnGap: layout.value.gutter[1] + 'px',
          gridRowGap: layout.value.gutter[0] + 'px',
          rowGap: layout.value.gutter[0] + 'px'
        };
      } else if (layout.value.direction === DirectionType.Horizontal) {
        if (layout.value.flowWrap && tabOption.value.resizeX === TabResizeType.Fixed) {
          //  行列间距都生效
          grid = {
            display: 'grid',
            gridTemplateColumns: `repeat(auto-fill, ${tabOption.value.width}px)`,
            gridColumnGap: layout.value.gutter[1] + 'px',
            columnGap: layout.value.gutter[1] + 'px',
            gridRowGap: layout.value.gutter[0] + 'px',
            rowGap: layout.value.gutter[0] + 'px',
            gridAutoRows: tabOption.value.resizeY === TabResizeType.Adapt ? 'auto' : tabOption.value.height + 'px'
          };
        } else {
          // 只有列间距
          grid = {
            display: 'grid',
            gridAutoFlow: 'column',
            gridTemplateRows: `repeat(1,${
              tabOption.value.resizeY === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.height + 'px'
            })`,
            gridColumnGap: layout.value.gutter[1] + 'px',
            columnGap: layout.value.gutter[1] + 'px',
            gridAutoColumns: tabOption.value.resizeX === TabResizeType.Adapt ? '1fr' : tabOption.value.width + 'px'
          };
        }
      } else if (layout.value.direction === DirectionType.Vertical) {
        // 说明垂直排布
        grid = {
          display: 'grid',
          gridTemplateColumns: `repeat(1, ${
            tabOption.value.resizeX === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.width + 'px'
          })`,
          gridRowGap: layout.value.gutter[0] + 'px',
          rowGap: layout.value.gutter[0] + 'px',
          gridAutoRows: tabOption.value.resizeY === TabResizeType.Adapt ? '1fr' : tabOption.value.height + 'px'
        };
      }
      return {
        height:
          tabOption.value.resizeX === TabResizeType.Fixed &&
          layout.value.scrollbar &&
          tabOption.value.resizeY === TabResizeType.Adapt
            ? props.block.height + 'px'
            : '100%',
        ...grid
      };
    });

    const tabItemStyle = (index: number) => {
      const style = tabOption.value.style;
      const width = tabOption.value.resizeX === TabResizeType.Adapt ? '100%' : tabOption.value.width + 'px';
      const height = tabOption.value.resizeY === TabResizeType.Adapt ? '100%' : tabOption.value.height + 'px';
      let fill, stroke, effects, isRow;
      if (hoverIndex.value === index && style.hover && style.hover.visible) {
        // 悬浮样式处理
        // 说明有样式
        fill = getFillPaintsStyle([style.hover.background]);
        stroke = getStrokeStyle(style.hover.border);
        effects = getEffectStyle(style.hover.shadow);
        isRow =
          style.hover.icon.position === IconPosition.Left || style.hover.icon.position === IconPosition.Right
            ? true
            : false;
      } else if (selectedIndexs.value.includes(index) && style.active && style.active.visible) {
        // 选中样式处理
        fill = getFillPaintsStyle([style.active.background]);
        stroke = getStrokeStyle(style.active.border);
        effects = getEffectStyle(style.active.shadow);

        isRow =
          style.active.icon.position === IconPosition.Left || style.active.icon.position === IconPosition.Right
            ? true
            : false;
      } else {
        // 普通样式处理
        fill = getFillPaintsStyle(style.background ? [style.background as FillPaints] : undefined);
        stroke = getStrokeStyle(style.border);
        effects = getEffectStyle(style.shadow);

        isRow = tabOption.value.icon
          ? tabOption.value.icon.position === IconPosition.Left || tabOption.value.icon.position === IconPosition.Right
            ? true
            : false
          : true;
      }

      // 删除未设置样式
      for (const key in fill) {
        if (fill[key] === 'initial') {
          delete fill[key];
        }
      }

      for (const key in stroke) {
        if (stroke[key] === 'initial') {
          delete stroke[key];
        }
      }

      for (const key in effects) {
        if (effects[key] === 'initial') {
          delete effects[key];
        }
      }

      // 文本溢出处理
      return {
        ...fill,
        ...stroke,
        ...effects,
        width,
        height,
        borderRadius: style.radius.map((r) => r + 'px').join(' '),
        display: 'flex',
        flexDirection: isRow ? 'row' : 'column',
        // 水平对齐
        [isRow ? 'justifyContent' : 'alignItems']:
          style.font.alignHorizontal === TextAlign.Center
            ? 'center'
            : style.font.alignHorizontal === TextAlign.Left
            ? 'flex-start'
            : style.font.alignHorizontal === TextAlign.Right
            ? 'flex-end'
            : 'space-around',
        // 垂直对齐
        [isRow ? 'alignItems' : 'justifyContent']:
          style.font.alignVertical === VerticalAlign.Center
            ? 'center'
            : style.font.alignVertical === VerticalAlign.Top
            ? 'flex-start'
            : 'flex-end',
        transform: `translate${layout.value.direction === DirectionType.Horizontal ? 'X' : 'Y'}(${
          !layout.value.scrollbar ? offset.value : 0
        }px)`,
        transition: 'transform 0.3s ease'
      } as CSSProperties;
    };

    const tabContentStyle = (index: number) => {
      const style = tabOption.value.style;

      let textStyle, textShadow, isFullHeight, isFullWidth;
      let allHeight = 0;
      let allWidth = 0;

      // 处理不同状态下的样式
      if (hoverIndex.value === index && style.hover && style.hover.visible) {
        ({ textStyle, textShadow, isFullHeight, isFullWidth, allHeight, allWidth } = getStatusStyle('hover', style));
      } else if (selectedIndexs.value.includes(index) && style.active && style.active.visible) {
        ({ textStyle, textShadow, isFullHeight, isFullWidth, allHeight, allWidth } = getStatusStyle('active', style));
      } else {
        const hasIcon = checkedStatusHasIcon();
        textStyle = getTextStyle(style.font as unknown as Text, style.font.direction === DirectionType.Vertical);
        textShadow = getTextEffectStyle(style.textEffects);

        isFullHeight = hasIcon ? isHorizontalPosition(tabOption.value.icon!.position) : true;

        isFullWidth = hasIcon ? isVerticalPosition(tabOption.value.icon!.position) : true;
      }

      // 清理样式中的初始值
      cleanInitialValues(textStyle);
      cleanInitialValues(textShadow);

      const { justifyContent, alignItems } = getTextStyle(
        style.font as unknown as Text,
        style.font.direction === DirectionType.Vertical
      );

      return {
        ...overflowStyle.value,
        ...textStyle,
        ...textShadow,
        justifyContent: style.overflow === 3 && textOverflowFlags.value[index] ? 'flex-start' : justifyContent,
        alignItems,
        writingMode: style.font.direction === DirectionType.Horizontal ? 'horizontal-tb' : 'vertical-lr',
        textOrientation: 'upright',
        flex: dealOverflow(index),
        height: getHeightValue(index, isFullHeight, allHeight),
        width: getWidthValue(index, isFullWidth, allWidth),
        borderRadius: '0px'
      } as CSSProperties;
    };

    /**
     * 获取状态样式
     * @param status
     * @param style
     * @returns
     */
    const getStatusStyle = (status: 'hover' | 'active', style: TabStyle) => {
      const statusStyle = style[status];
      const hasIcon = checkedStatusHasIcon(status);

      const textStyle = getTextStyle(
        statusStyle!.font as unknown as Text,
        style.font.direction === DirectionType.Vertical
      );
      const textShadow = getTextEffectStyle(statusStyle!.textEffects, status);

      const isFullHeight = hasIcon ? isHorizontalPosition(statusStyle!.icon.position) : true;
      const isFullWidth = hasIcon ? isVerticalPosition(statusStyle!.icon.position) : true;

      // 计算图标尺寸
      const { allHeight, allWidth } = calculateIconDimensions(statusStyle!.icon, hasIcon);

      return { textStyle, textShadow, isFullHeight, isFullWidth, allHeight, allWidth };
    };

    /** 计算图标尺寸 */
    const calculateIconDimensions = (iconConfig: IconOption, hasIcon: boolean) => {
      if (!hasIcon) return { allHeight: 0, allWidth: 0 };

      const type = iconConfig.type;
      const height = type === 'picture' ? iconConfig.height : iconConfig.icon.size;
      const width = type === 'picture' ? iconConfig.width : iconConfig.icon.size;
      const gutter = iconConfig.gutter;

      const allHeight = height + (isVerticalPosition(iconConfig.position) ? gutter : 0);
      const allWidth = width + (isHorizontalPosition(iconConfig.position) ? gutter : 0);

      return { allHeight, allWidth };
    };

    /**
     * 判断图标位置是否为水平方向
     * @param position
     * @returns
     */
    const isHorizontalPosition = (position: IconPosition) => {
      return position === IconPosition.Left || position === IconPosition.Right;
    };

    /**
     * 判断图标位置是否为垂直方向
     * @param position
     * @returns
     */
    const isVerticalPosition = (position: IconPosition) => {
      return position === IconPosition.Top || position === IconPosition.Bottom;
    };

    /**
     * 清理初始值
     * @param styleObj
     */
    const cleanInitialValues = (styleObj: any) => {
      for (const key in styleObj) {
        if (styleObj[key] === 'initial') {
          delete styleObj[key];
        }
      }
    };

    /**
     * 获取高度
     * @param index
     * @param isFullHeight
     * @param allHeight
     * @returns
     */
    const getHeightValue = (index: number, isFullHeight: boolean, allHeight: number) => {
      if (isFullHeight) return '100%';
      if (!textOverflowFlags.value[index]) return 'auto';

      if (tabOption.value.icon) {
        const contentRef = tabContentRef.value && tabContentRef.value[index];
        return contentRef && allHeight < contentRef.clientHeight ? `${contentRef.clientHeight - allHeight}px` : '0px';
      }

      return tabRef.value[index] ? `${tabRef.value[index].clientHeight}px` : 'auto';
    };

    /**
     * 获取宽度值
     * @param index
     * @param isFullWidth
     * @param allWidth
     * @returns
     */
    const getWidthValue = (index: number, isFullWidth: boolean, allWidth: number) => {
      if (isFullWidth) return '100%';
      if (!textOverflowFlags.value[index] || tabOption.value.style.font.direction !== DirectionType.Horizontal) {
        return 'auto';
      }

      if (tabOption.value.icon) {
        const contentRef = tabContentRef.value && tabContentRef.value[index];
        return contentRef && allWidth < contentRef.clientWidth ? `${contentRef.clientWidth - allWidth}px` : '0px';
      }

      return tabRef.value[index] ? `${tabRef.value[index].clientWidth}px` : 'auto';
    };

    const dealOverflow = (index: number) => {
      if (textOverflowFlags.value[index]) {
        return tabOption.value.style.font.direction === DirectionType.Horizontal ? '1' : '0 1 auto';
      }
      if (tabOption.value.style.overflow === 1) {
        if (tabOption.value.style.font.direction === DirectionType.Horizontal) {
          return marqueeRefs.value[index] && marqueeRefs.value[index].scrollWidth > marqueeRefs.value[index].clientWidth
            ? '1'
            : '0 1 auto';
        } else {
          return '0 1 auto';
        }
      } else if (tabOption.value.style.overflow === 2) {
        // 换行
        if (tabOption.value.style.font.direction === DirectionType.Horizontal) {
          // 文本水平 检测高度对比
          if (marqueeRefs.value[index] && changLineRefs.value[index]) {
            return marqueeRefs.value[index].clientHeight > changLineRefs.value[index].clientHeight ? '1' : '0 1 auto';
          } else {
            return '0 1 auto';
          }
        } else {
          // 文本垂直 检测宽度对比

          return '0 1 auto';
        }
      }
    };

    const overflowStyle = computed(() => {
      const style = tabOption.value.style;
      const overflow = getTextOverflow(style.overflow);
      return overflow as CSSProperties;
    });

    /**
     * 检测是否添加图标配置
     * @param status
     * @returns
     */
    const checkedStatusHasIcon = (status?: 'active' | 'hover') => {
      if (!status) {
        return !!tabOption.value.icon;
      }
      const icon = tabOption.value.style[status]?.icon;
      if (!icon) return false;

      return icon.type === 'picture' ? !!icon.image?.url : !!icon.icon.name;
    };

    // 检测文本是否溢出
    const checkTextOverflow = () => {
      nextTick(() => {
        if (!marqueeRefs.value || marqueeRefs.value.length === 0) return;

        // 确保textOverflowFlags数组长度与data相同
        if (textOverflowFlags.value.length !== data.value.length) {
          textOverflowFlags.value = Array(data.value.length).fill(false);
        }

        // 遍历所有元素检查溢出
        marqueeRefs.value.forEach((el, index) => {
          if (!el) return;
          const parentEl = el.parentElement;
          if (!parentEl) return;
          // 检查元素是否溢出
          const isOverflowing =
            tabOption.value.style.font.direction === DirectionType.Horizontal
              ? el.clientWidth > parentEl.clientWidth + 2
              : el.clientHeight > parentEl.clientHeight + 2;
          textOverflowFlags.value[index] = isOverflowing;
        });

        // 强制更新
        textOverflowFlags.value = [...textOverflowFlags.value];
      });
    };

    // 获取每个项目的溢出样式
    const getItemOverflowStyle = (index: number) => {
      const style = tabOption.value.style;
      // 确保索引有效
      if (index < 0 || index >= (textOverflowFlags.value.length || 0)) {
        return getTextOverflow(style.overflow) as CSSProperties;
      }
      // 只有在overflow为3（跑马灯）且文本确实溢出时才应用动画
      if (style.overflow === 3 && textOverflowFlags.value[index]) {
        const speed = tabOption.value.style.scrollSpeed > 0 ? SPEED * tabOption.value.style.scrollSpeed : SPEED;
        let duration = SPEED;
        if (marqueeRefs.value[index]) {
          const length =
            style.font.direction === DirectionType.Horizontal
              ? marqueeRefs.value[index].clientWidth
              : marqueeRefs.value[index].clientHeight;
          duration = length / speed;
        }
        return {
          display: 'inline-block',
          animation: `${
            style.font.direction === DirectionType.Horizontal ? DirectionType.Horizontal : DirectionType.Vertical
          }-scroll ${duration}s linear infinite`,
          paddingRight: (style.font.direction === DirectionType.Horizontal ? 5 : 0) + 'px',
          paddingBottom: (style.font.direction === DirectionType.Horizontal ? 0 : 5) + 'px'
        };
      }
      return getTextOverflow(style.overflow) as CSSProperties;
    };

    // 监听数据变化和样式变化，重新检测溢出
    watch(
      [() => data.value, () => props.block.width, () => props.block.height, () => tabOption.value],
      () => {
        checkTextOverflow();
      },
      { deep: true }
    );

    // 监听布局
    watch([() => tabOption.value.layout.direction], (val) => {
      const blockWidth = props.block.width;
      const blockHeight = props.block.height;
      if (val[0] === DirectionType.Horizontal) {
        // 切换为水平 区分情况
        if (tabOption.value.resizeX === TabResizeType.Adapt) {
          // 宽度为自适应 比较块的宽高，值大的为宽度，值小的为高度
          if (tabOption.value.resizeY === TabResizeType.Adapt) {
            tabBlock.value.height = blockWidth > blockHeight ? blockHeight : blockWidth;
          } else {
            tabBlock.value.height = tabOption.value.height;
          }
          tabBlock.value.width = blockWidth > blockHeight ? blockWidth : blockHeight;
        } else {
          // 宽度为固定值的情况下，块的宽度应是 选项卡的数量*宽度+ 横向间距*(选项卡数量-1)
          tabBlock.value.width =
            data.value.length * tabOption.value.width + (data.value.length - 1) * tabOption.value.layout.gutter[1];

          // 高度自适应
          tabBlock.value.height =
            tabOption.value.resizeY === TabResizeType.Adapt ? tabOption.value.width : tabOption.value.height;
        }
      } else if (val[0] === DirectionType.Vertical) {
        // 垂直显示
        if (tabOption.value.resizeY === TabResizeType.Adapt) {
          if (tabOption.value.resizeX === TabResizeType.Adapt) {
            // 行列都自适应的情况下块的宽高切换
            tabBlock.value.width = blockWidth > blockHeight ? blockHeight : blockWidth;
          } else {
            // 行高自适应  列宽有固定值
            tabBlock.value.width = tabOption.value.width;
          }
          tabBlock.value.height = blockWidth > blockHeight ? blockWidth : blockHeight;
        } else {
          tabBlock.value.width =
            tabOption.value.resizeX === TabResizeType.Adapt ? tabOption.value.height : tabOption.value.width;

          // 高度固定高度的情况下
          tabBlock.value.height =
            data.value.length * tabOption.value.height + (data.value.length - 1) * tabOption.value.layout.gutter[0];
        }
      } else {
        // 网格处理
        // 获取最优行列数
        const { cols, rows } = calculateOptimalGrid(data.value.length);
        tabOption.value.layout.column = cols;
        tabOption.value.layout.row = rows;
        // 行列都自适应 都按照每个tab 宽度为100px 高度为50px 进行计算 有固定值则按照固定值计算
        const width = tabOption.value.resizeX === TabResizeType.Adapt ? 100 : tabOption.value.width;
        const height = tabOption.value.resizeY === TabResizeType.Adapt ? 50 : tabOption.value.height;
        tabBlock.value.width = cols * width + (cols - 1) * tabOption.value.layout.gutter[1];
        tabBlock.value.height = rows * height + (rows - 1) * tabOption.value.layout.gutter[0];
      }
    });

    const calculateOptimalGrid = (length: number) => {
      if (length <= 0) return { rows: 0, cols: 0 };

      let bestRows = 1;
      let bestCols = length;
      let minDiff = Math.abs(bestRows - bestCols);

      // 从1到平方根遍历可能的行数
      for (let rows = 2; rows <= Math.sqrt(length); rows++) {
        const cols = Math.ceil(length / rows);
        const diff = Math.abs(rows - cols);

        // 如果找到更小的差值，更新最佳行列数
        if (diff < minDiff) {
          minDiff = diff;
          bestRows = rows;
          bestCols = cols;
        }
      }

      // 检查是否交换行列能得到更小的差值
      if (Math.abs(bestCols - bestRows) > Math.abs(bestRows - bestCols)) {
        [bestRows, bestCols] = [bestCols, bestRows];
      }

      return {
        rows: bestRows,
        cols: bestCols
      };
    };

    //#region 图标
    const getIconStyle = (type: 'picture' | 'icon', status?: 'active' | 'hover') => {
      let position = IconPosition.Right;
      const icon = !status ? tabOption.value.icon : tabOption.value.style[status]?.icon;
      if (icon?.position === IconPosition.Right) {
        position = IconPosition.Left;
      } else if (icon?.position === IconPosition.Top) {
        position = IconPosition.Bottom;
      } else if (icon?.position === IconPosition.Bottom) {
        position = IconPosition.Top;
      }
      const { r, g, b, a } = icon?.icon.color || {};

      return {
        [`margin-${position}`]: icon?.gutter + 'px',
        width: (type === 'picture' ? icon?.width : icon?.icon.size) + 'px',
        height: (type === 'picture' ? icon?.height : icon?.icon.size) + 'px',
        fill: `rgba(${r}, ${g}, ${b}, ${a})`
      };
    };

    /**
     * 默认状态下的图标url
     */
    const iconUrl = computed(() => {
      if (!tabOption.value.icon || !tabOption.value.icon.image) return '';
      const { type, url } = tabOption.value.icon.image;
      return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
    });

    const shouldShowIcon = (idx: number, position: string) => {
      if (hoverIndex.value === idx && tabOption.value.style.hover) {
        // 悬浮状态
        if (tabOption.value.style.hover?.visible) {
          // 悬浮状态显示
          if (showPositionIcon(position, 'hover')) {
            if (tabOption.value.style.hover.icon.type === 'picture') {
              return tabOption.value.style.hover.icon.image?.url;
            }
            return tabOption.value.style.hover.icon.icon.name;
          }

          return false;
        } else {
          // 悬浮状态隐藏
          if (selectedIndexs.value.includes(idx) && tabOption.value.style.active?.visible) {
            if (showPositionIcon(position, 'active')) {
              if (tabOption.value.style.active.icon.type === 'picture') {
                return tabOption.value.style.active.icon.image?.url;
              }
              return tabOption.value.style.active.icon.icon.name;
            }
            return false;
          }
          return !!tabOption.value.icon;
        }
      } else {
        if (selectedIndexs.value.includes(idx) && tabOption.value.style.active?.visible) {
          if (showPositionIcon(position, 'active')) {
            if (tabOption.value.style.active.icon.type === 'picture') {
              return tabOption.value.style.active.icon.image?.url;
            }
            return tabOption.value.style.active.icon.icon.name;
          }
          return false;
        }
        return !!tabOption.value.icon && showPositionIcon(position);
      }
    };

    const showPositionIcon = (position: string, status?: 'hover' | 'active') => {
      if (position === 'before') {
        if (status) {
          return tabOption.value.style[status]?.icon.position === IconPosition.Left ||
            tabOption.value.style[status]?.icon.position === IconPosition.Top
            ? true
            : false;
        } else {
          return tabOption.value.icon?.position === IconPosition.Left ||
            tabOption.value.icon?.position === IconPosition.Top
            ? true
            : false;
        }
      } else {
        if (status) {
          return tabOption.value.style[status]?.icon.position === IconPosition.Right ||
            tabOption.value.style[status]?.icon.position === IconPosition.Bottom
            ? true
            : false;
        } else {
          return tabOption.value.icon?.position === IconPosition.Right ||
            tabOption.value.icon?.position === IconPosition.Bottom
            ? true
            : false;
        }
      }
    };

    /**
     * 悬浮状态下的iconUrl
     */
    const statusIconUrl = (idx: number, status: 'hover' | 'active') => {
      const currentStatus = status === 'hover' ? hoverIndex.value === idx : selectedIndexs.value.includes(idx);
      if (currentStatus && tabOption.value.style[status]?.visible) {
        if (tabOption.value.style[status].icon.type === 'picture') {
          if (tabOption.value.style[status]?.icon.image?.url) {
            const { type, url } = tabOption.value.style[status].icon.image;
            return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
          }
          return;
        } else {
          return tabOption.value.style[status]?.icon?.icon.name;
        }
      }
      return;
    };

    //#endregion 图标

    //#region 数据
    const transformData = (originalData: any) => {
      return originalData.map((item: any) => {
        const newItem: { [key: string]: any } = {};

        ['rows', 'columns'].forEach((newKey) => {
          // 获取该 key 对应的映射配置（如 [{ fieldName: "A", ... }]）
          const mappingList = props.widget[newKey as keyof Tab] as AxisField[];

          // 如果该key有多个映射字段，则拼接这些字段的值
          if (mappingList.length > 1) {
            let concatenatedValue = '';
            mappingList.forEach((mapping) => {
              const originalField = mapping.fieldName;
              if (originalField && Object.prototype.hasOwnProperty.call(item, originalField)) {
                concatenatedValue += item[originalField];
              }
            });
            if (concatenatedValue) {
              newItem[newKey] = concatenatedValue;
            }
          }
          // 如果只有一个映射字段，则保持原逻辑
          else if (mappingList.length === 1) {
            const mapping = mappingList[0];
            const originalField = mapping.fieldName;
            if (originalField && Object.prototype.hasOwnProperty.call(item, originalField)) {
              newItem[newKey] = item[originalField];
            }
          }
        });

        return newItem;
      });
    };
    const loadWidgetData = async () => {
      return new Promise((resolve) => {
        if (props.widget.datasetType === 'static') {
          loadStaticData().then((res: any) => {
            if (res) {
              data.value = transformData(res);
            }
            resolve(res);
          });
          return;
        }
        getWidgetFieldData(
          new Promise((resolve) => {
            setTimeout(() => {
              console.log('ParagraphComponent: 重写加载选项卡数据', props.widget.id);
              resolve(true);
            }, 5000);
          })
        ).then((res) => {
          resolve(res);
        });
      });
    };
    //#endregion 数据

    //#region 自动轮播
    let timer: ReturnType<typeof setTimeout>;
    /**
     * 标记是否手动点击
     */
    let isManualClick = false;
    /** 点击停留结束时间戳 */
    let clickEndTime = 0;

    const startCarousel = () => {
      timer && clearInterval(timer);

      const interval =
        isManualClick && Date.now() < clickEndTime
          ? tabOption.value.carousel!.clickTime * 1000
          : tabOption.value.carousel!.interval * 1000;

      timer = setInterval(() => {
        // 如果在点击停留期内则不切换
        if (Date.now() < clickEndTime) return;
        // 重置手动标记
        isManualClick = false;
        const nextIndex = data.value[selectedIndexs.value[0] + 1] ? selectedIndexs.value[0] + 1 : 0;
        selectedIndexs.value = [nextIndex];

        // 使用正常间隔启动下一次轮播
        startCarousel();
      }, interval);
    };

    watch(
      [() => tabOption.value.carousel, () => tabOption.value.multiple],
      ([carousel, multiple]) => {
        // 清除旧定时器
        timer && clearInterval(timer);

        if (carousel && !multiple) {
          startCarousel();
        }
      },
      { immediate: true, deep: true }
    );
    //#endregion 自动轮播

    onMounted(() => {
      // 确保DOM渲染完成后再检测
      checkTextOverflow();
    });

    onBeforeUnmount(() => {
      timer && clearInterval(timer);
    });

    return {
      tabOption,
      data,
      tabStyle,
      layout,
      TabResizeType,
      overflowStyle,
      marqueeRefs,
      scrollRef,
      tabContentRef,
      tabRef,
      changLineRefs,
      textOverflowFlags,
      iconUrl,
      hoverIndex,
      selectedIndexs,
      StrokeAlign,
      allStyle,
      getIconStyle,
      tabItemStyle,
      tabContentStyle,
      getItemOverflowStyle,
      shouldShowIcon,
      statusIconUrl,
      handleActive,
      loadWidgetData
    };
  }
});
