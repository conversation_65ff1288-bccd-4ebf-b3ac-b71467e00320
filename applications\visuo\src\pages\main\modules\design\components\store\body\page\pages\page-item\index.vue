<template>
  <draggable
    :list="nodes"
    :group="{ name: 'vis-page-item', pull: true }"
    ghost-class="vis-store-page-ghost"
    drag-class="vis-store-page-drag"
    class="vis-store-page"
    item-key="id"
    :data-item-key="itemKey"
    :disabled="isSearchHide || isEditingStatus"
    :animation="0"
    :empty-insert-threshold="0"
    :move="onCheckMove"
    @add="onDragAdd"
    @start="onDragStart"
    @end="onDragEnd"
  >
    <template #item="{ element }">
      <div
        :class="[
          'vis-store-page-node',
          element.children && element.children.length ? 'vis-store-page-node__parent' : 'vis-store-page-node__child',
          isSelected(element) && 'selected',
          isDragTargetParent(element) && 'drag-target-parent'
        ]"
      >
        <div
          class="vis-store-page-header"
          @click="onSelect(element, $event)"
          @contextmenu.stop.prevent="onOpenContextMenu(element, $event)"
        >
          <div
            class="vis-store-page-header-expand"
            v-if="element.children && element.children.length"
            @click.stop="toggleExpand(element, $event)"
          >
            <q-icon
              name="keyboard_arrow_right"
              class="vis-store-page-header-expand-arrow"
              :class="{ rotate: isExpanded(element) }"
            />
          </div>
          <div class="vis-store-page-header-content" @dblclick="onDoubleClick(element, $event)">
            <!-- 标题 -->
            <div class="vis-store-page-header-content-title">
              <div :class="['vis-store-page-header-content-title-icon']">
                <q-icon v-if="isHomePage(element)" name="o_home" />
                <!-- 占位符 保证对齐 -->
                <q-icon v-else name="" />
              </div>
              <!-- 双击编辑 -->
              <q-input
                v-if="isEditing(element)"
                v-model="editingName"
                dense
                borderless
                class="vis-store-page-header-content-title-input"
                @blur="onSaveName(element)"
                @keyup.enter="onSaveName(element)"
                @keyup.esc="onCancelEdit"
                ref="editInputRef"
              />
              <div v-else class="vis-store-page-header-content-title-text">
                {{ element.name }}
              </div>
            </div>
            <div class="vis-store-page-header-content-side">
              <div v-if="isActivated(element)" class="vis-store-page-header-content-side-icon">
                <q-icon name="o_check" />
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="element.children && element.children.length"
          class="vis-store-page-collapsible"
          :class="{ folder: !isExpanded(element) }"
        >
          <!-- 递归渲染子节点 -->
          <div class="vis-store-page-children">
            <vis-page-item
              :nodes="element.children"
              :data-item-key="element.id"
              :selected-ids="selectedIds"
              @update:selected-ids="onEmitSelected"
              :expanded-map="expandedMap"
              @update:expanded-map="onEmitExpanded"
              :drag-target-parent-id="dragTargetParentId"
              :is-search-hide="isSearchHide"
              :is-editing-status="isEditingStatus"
              @drag-add="onDragAdd"
              @drag-end="onDragEnd"
              @drag-start="onDragStart"
              @drag-move="onCheckMove"
              @context-menu="onOpenContextMenu"
            />
          </div>
        </div>
      </div>
    </template>
  </draggable>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
