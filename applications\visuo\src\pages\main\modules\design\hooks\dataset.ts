import type { DatasetField } from '@hetu/metadata-shared';
import { FieldType } from '@hetu/platform-app';
import { AxisField, DocumentService, useDocumentStore } from '@vis/document-core';

/**
 * 数据集相关
 * <AUTHOR>
 */
export const useDataset = () => {
  const { staticDatas } = useDocumentStore();

  /**
   * 根据字段的数据类型返回图标
   * @param type
   */
  const getDataTypeIcon = (type?: string) => {
    switch (type) {
      case 'date':
      case 'date_1':
      case 'date_2':
      case 'datetime':
      case 'datetime_1':
      case 'datetime_2':
      case 'time':
      case 'time_1':
        return 'date';
      case 'integer':
      case 'number':
      case 'double':
        return 'integer';
      case 'string':
        return 'string';
      case 'area_province':
      case 'area_city':
      case 'area_district':
      case 'area':
        return 'string';
      default:
        return 'string';
    }
  };

  /**
   * 获取静态数据字段列表
   * @param name
   */
  const getStaticDataField = async (name: string) => {
    const allStaticData = { ...(await DocumentService.loadStaticData()), ...staticDatas.value }[name];
    const fields = allStaticData.fields || {};
    return fields
      .filter((f: AxisField) => f.fieldName && f.fieldAlias)
      .map((item: AxisField) => {
        return {
          id: item.fieldName,
          fieldName: item.fieldName,
          fieldAlias: item.fieldAlias,
          fieldDatatype: item.dataType
        } as DatasetField;
      });
  };

  /**
   * 获取静态数据
   * @param name
   */
  const getStaticData = (name: string) => {
    return staticDatas.value[name];
  };

  return {
    getDataTypeIcon,
    getStaticDataField,
    getStaticData
  };
};
