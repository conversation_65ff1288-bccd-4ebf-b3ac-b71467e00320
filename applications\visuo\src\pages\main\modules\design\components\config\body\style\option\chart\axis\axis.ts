import { computed, defineComponent, type PropType } from 'vue';
import type { Axis } from '@vis/document-core';

/**
 * 图表坐标轴配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-chart-axis',
  props: {
    options: {
      type: Object as PropType<Axis>,
      required: true
    }
  },
  setup(props) {
    const axisOptions = computed(() => props.options);

    const handleDelete = (index: number) => {
      axisOptions.value.grid.fillPaints.splice(index, 1);
    };

    return {
      axisOptions,
      handleDelete
    };
  }
});
