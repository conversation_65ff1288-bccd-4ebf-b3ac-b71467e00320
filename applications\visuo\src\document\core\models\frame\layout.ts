import { UUID } from '@hetu/platform-shared';
import { Color, FillPaints, FillType } from '../ui';

/**
 * 约束类型
 */
export enum HoriConsType {
  /** 靠左 */
  Left = 'left',

  /** 靠右 */
  Right = 'right',

  /** 居中 */
  Center = 'center',

  /** 左右固定 */
  Stretch = 'stretch',

  /** 拉伸 */
  Scale = 'scale'
}

export enum VertConsType {
  /** 靠上 */
  Top = 'top',

  /** 靠下 */
  Bottom = 'bottom',

  /** 居中 */
  Center = 'center',

  /** 左右固定 */
  Stretch = 'stretch',

  /** 拉伸 */
  Scale = 'scale'
}

/**
 * 固定行为
 */
export enum BehaviorType {
  /** 随父级滚动 */
  Follow = 'follow',

  /** 固定  */
  Fixed = 'fixed',

  /** 吸顶固定  */
  Sticky = 'sticky'
}

/**
 * 约束
 */
export class Constraints {
  /** 水平 */
  horizontal = HoriConsType.Left;

  /** 垂直 */
  vertical = VertConsType.Top;

  /**
   * 固定行为，针对子容器
   * */
  scrollBehavior = BehaviorType.Follow;
}

export enum DirectionType {
  /** 自由形态 */
  Freeform = 'freeform',

  /** 水平 */
  Horizontal = 'horizontal',

  /** 垂直 */
  Vertical = 'vertical',

  /** 网格 */
  Grid = 'grid'
}

export enum MainAlignment {
  Start = 'start',
  End = 'end',
  Center = 'center',
  Between = 'space-between'
}

export enum CrossAlignment {
  Start = 'start',
  End = 'end',
  Center = 'center',
  Between = 'space-between'
}

/**
 * 自动布局
 */
export class AutoLayout {
  /** 布局方向 */
  direction = DirectionType.Freeform;

  /** 是否自动换行 */
  flowWarp = false;

  /** 主轴对齐方式 */
  mainAxisAlignment = MainAlignment.Start;

  /** 交叉轴对齐方式 */
  crossAxisAlignment = CrossAlignment.Start;

  /** 水平间距 */
  horizontalGap: number | 'Auto' = 10;

  /** 垂直间距 */
  verticalGap: number | 'Auto' = 10;

  /** 内边距 */
  padding = [0, 0, 0, 0];

  /** 网格大小 */
  gridSize = [2, 2];

  /** 网格行高 */
  gridRowsSizing: Array<number | 'Auto'> = ['Auto', 'Auto'];

  /** 网格列宽 */
  gridColumnsSizing: Array<number | 'Auto'> = ['Auto', 'Auto'];

  /** 滚动方向 */
  scrollDirection: 'none' | 'v' | 'h' | 'all' = 'none';

  /** 对于根容器，固定或响应式布局 */
  type: 'fixed' | 'responsive' = 'fixed';
}

export enum LayoutGuideType {
  Row = 'row',
  Column = 'column',
  Grid = 'grid'
}

export enum PatternMode {
  Start = 'start',
  End = 'end',
  Center = 'center',
  Stretch = 'stretch'
}

/**
 * 布局网格
 */
export class LayoutGuide {
  id = UUID();
  /** 类型 */
  type: LayoutGuideType = LayoutGuideType.Grid;

  /** 可见性 */
  visible = true;

  /** 类型为网格时，表示尺寸; 类型为栅格列/栅格行是，表示为数量 */
  count = 8;

  /** 偏移 布局方式为拉伸时，表示边距 */
  offset = 10;

  /** 列度 */
  width: number | 'Auto' = 'Auto';

  /** 槽宽 */
  gutter = 10;

  /** 颜色 */
  color = new FillPaints(FillType.Solid, new Color(255, 0, 0, 0.1));

  /** 布局方式 */
  pattern: PatternMode = PatternMode.Stretch;
}
/**
 * grid布局中子图形的位置及对齐方式
 */
export class GridItem {
  /** 行位置 */
  rows: number[] = [1, 2];
  /** 列位置 */
  columns: number[] = [1, 2];
  /** 水平对齐方式 */
  justifySelf = CrossAlignment.Start;
  /** 垂直对齐方式 */
  alignSelf = CrossAlignment.Start;

  /** 在grid布局里的位置 栅格内生效 */
  x: number = 0;
  y: number = 0;

  constructor(rows: number[], columns: number[], justifySelf?: CrossAlignment, alignSelf?: CrossAlignment) {
    this.rows = rows;
    this.columns = columns;
    this.justifySelf = justifySelf || CrossAlignment.Start;
    this.alignSelf = alignSelf || CrossAlignment.Start;
  }
}
