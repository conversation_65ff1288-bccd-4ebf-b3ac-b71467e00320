import { computed, nextTick } from 'vue';
import { useActionStore, useDesignStore } from '../stores';
import { useGraph } from './graph';
import { usePage } from './page';
import { Block, useDocumentStore, Graph, Page, Frame, DirectionType, GraphType } from '@vis/document-core';
import { VIS_DESIGN_INFINITE_CANVAS, type ContextMenuInstance } from '../models';
import { copyToClipboard, Notify } from 'quasar';

/**
 * 设计器画布操作方法
 * <AUTHOR>
 */
export const useAction = () => {
  const actionStore = useActionStore();
  const docStore = useDocumentStore();
  const designStore = useDesignStore();

  const actions = computed(() => actionStore.actions.value);
  const doc = computed(() => docStore.document.value);
  const counter = computed(() => ++designStore.counter.value);

  const {
    findGraph,
    findGraphParents,
    activeGraphs,
    getGraphXYByCanvas,
    addGraphToPage,
    moveGraphs,
    getCanvasPosSize
  } = useGraph();
  const { findPage, customClone } = usePage();

  const move = () => {
    resetGroupActive();
    actions.value.move.active = true;
  };
  const hand = () => {
    resetGroupActive();
    actions.value.hand.active = true;
  };

  const frame = () => {
    resetGroupActive();
    actions.value.frame.active = true;
  };

  const textbox = () => {
    resetGroupActive();
    actions.value.textbox.active = true;
  };

  const dhyana = () => {
    //
    // designStore.moveableRef.value?.request('originDraggable', { origin: [100, 0] }, true);
  };

  /**
   * 还原同组操作状态
   * @param group
   */
  const resetGroupActive = (group: string = 'mouse') => {
    Object.values(actions.value).forEach((ele) => {
      if (ele.group === group) {
        ele.active = false;
      }
    });
  };

  /**
   * 对齐
   * @param type
   */
  const align = (type: string) => {
    const moveableRef = designStore.moveableRef.value;
    const activeGraphs = designStore.active.value.graphs;
    if (moveableRef) {
      const rect = moveableRef.getRect();
      const moveables = moveableRef.getMoveables();
      if (moveables.length <= 1 || activeGraphs.length <= 1) {
        alignGraph(type);
        return;
      }

      const leftArray: Array<number> = [];
      const rightArray: Array<number> = [];
      const topArray: Array<number> = [];
      const bottomArray: Array<number> = [];

      let allWidth = 0;
      let allHeight = 0;
      let activeLength = 0;
      activeGraphs.forEach((g) => {
        const { width, height } = g;
        const left = g.transform.translate[0];
        const top = g.transform.translate[1];

        leftArray.push(left);
        rightArray.push(left + width);
        topArray.push(top);
        bottomArray.push(top + height);
        allWidth += width;
        allHeight += height;
        activeLength++;
      });

      switch (type) {
        case 'left':
          activeGraphs.forEach((g) => {
            const x = Math.min(...leftArray);
            g.transform.translate[0] = x;
          });
          break;
        case 'right':
          activeGraphs.forEach((g) => {
            const x = Math.max(...rightArray) - g.width;
            g.transform.translate[0] = x;
          });
          break;
        case 'top':
          activeGraphs.forEach((g) => {
            const y = Math.min(...topArray);
            g.transform.translate[1] = y;
          });
          break;
        case 'bottom':
          activeGraphs.forEach((g) => {
            const y = Math.max(...bottomArray) - g.height;
            g.transform.translate[1] = y;
          });
          break;
        case 'center':
          activeGraphs.forEach((g) => {
            const x = Math.round(Math.min(...leftArray) + rect.width / 2 - g.width / 2);
            g.transform.translate[0] = x;
          });
          break;
        case 'middle':
          activeGraphs.forEach((g) => {
            const y = Math.round(Math.min(...topArray) + rect.height / 2 - g.height / 2);
            g.transform.translate[1] = y;
          });
          break;
        case 'horizontalSpace':
          {
            let left = rect.left;

            if (moveables.length <= 1) {
              return;
            }
            const gap =
              (rect.width -
                rect.children!.reduce((prev, cur) => {
                  return prev + cur.width;
                }, 0)) /
              (moveables.length - 1);

            moveables.sort((a, b) => {
              return a.state.left - b.state.left;
            });

            // rect.left是基于最外层的位置，如果在容器内，需要减去容器的x
            const isParent = activeGraphs[0].parent;
            let pLeft = 0;
            if (isParent) {
              const parents = findGraphParents(activeGraphs[0].id);
              pLeft = parents.reduce((sum, p) => {
                return sum + p.transform.translate[0];
              }, 0);
            }

            moveables.forEach((children) => {
              const rect = children.getRect();
              const id = children.props.target?.id;
              const graph = activeGraphs.find((g) => g.id === id);
              if (graph) {
                const x = Math.round(isParent ? left - pLeft : left);
                graph.transform.translate[0] = x;
              }
              left += rect.width + gap;
            });
          }
          break;
        case 'verticalSpace':
          {
            let top = rect.top;

            if (moveables.length <= 1) {
              return;
            }
            const gap =
              (rect.height -
                rect.children!.reduce((prev, cur) => {
                  return prev + cur.height;
                }, 0)) /
              (moveables.length - 1);

            moveables.sort((a, b) => {
              return a.state.top - b.state.top;
            });

            // rect.left是基于最外层的位置，如果在容器内，需要减去容器的x
            const isParent = activeGraphs[0].parent;
            let pTop = 0;
            if (isParent) {
              const parents = findGraphParents(activeGraphs[0].id);
              pTop = parents.reduce((sum, p) => {
                return sum + p.transform.translate[1];
              }, 0);
            }

            moveables.forEach((children) => {
              const rect = children.getRect();
              const id = children.props.target?.id;
              const graph = activeGraphs.find((g) => g.id === id);
              if (graph) {
                const y = Math.round(isParent ? top - pTop : top);
                graph.transform.translate[1] = y;
              }

              top += rect.height + gap;
            });
          }
          break;
      }
    }
  };

  /**
   * 单图形对齐
   * @param type 对齐方式
   * @param graph 图形
   * @param {
   * pw: 父组件宽度
   * ph: 父组件高度
   * }
   */
  const singleAlign = (type: string, graph: Graph, { pw, ph }: { pw: number; ph: number }) => {
    switch (type) {
      case 'left':
        graph.transform.translate[0] = 0;
        break;
      case 'right':
        graph.transform.translate[0] = pw - graph.width;
        break;
      case 'top':
        graph.transform.translate[1] = 0;
        break;
      case 'bottom':
        graph.transform.translate[1] = ph - graph.height;
        break;
      case 'center':
        graph.transform.translate[0] = Math.round((pw - graph.width) / 2);
        break;
      case 'middle':
        graph.transform.translate[1] = Math.round((ph - graph.height) / 2);
        break;
    }
  };

  const multipleAlign = (type: string, graphs: Graph[]) => {
    const leftArray: Array<number> = [];
    const rightArray: Array<number> = [];
    const topArray: Array<number> = [];
    const bottomArray: Array<number> = [];

    let allWidth = 0;
    let allHeight = 0;
    let activeLength = 0;
    graphs.forEach((g) => {
      const left = g.transform.translate[0];
      const top = g.transform.translate[1];
      leftArray.push(left);
      rightArray.push(left + g.width);
      topArray.push(left);
      bottomArray.push(top + g.height);
      allWidth += g.width;
      allHeight += g.height;
      activeLength++;
    });

    switch (type) {
      case 'left':
        graphs.forEach((g) => {
          const x = Math.min(...leftArray);
          g.transform.translate[0] = x;
        });
        break;
      case 'right':
        graphs.forEach((g, i) => {
          const x = Math.max(...rightArray) - g.width;
          g.transform.translate[0] = x;
        });
        break;
      case 'top':
        graphs.forEach((g) => {
          const y = Math.min(...topArray);
          g.transform.translate[1] = y;
        });
        break;
      case 'bottom':
        graphs.forEach((g) => {
          const y = Math.max(...bottomArray) - g.height;
          g.transform.translate[1] = y;
        });
        break;
      case 'center':
        {
          const centerX = (Math.max(...rightArray) - Math.min(...leftArray)) / 2 + Math.min(...leftArray);
          graphs.forEach((g) => {
            const x = Math.round(centerX - g.width / 2);
            g.transform.translate[0] = x;
          });
        }
        break;
      case 'middle':
        {
          const centerY = (Math.max(...bottomArray) - Math.min(...topArray)) / 2 + Math.min(...topArray);
          graphs.forEach((g) => {
            const y = Math.round(centerY - (g.height as number) / 2);
            g.transform.translate[1] = y;
          });
        }
        break;
    }
  };

  /**
   * 选中单个图形对齐
   * 规则：1. 选中图形有父元素，对齐是基于父元素的
   *      2. 选中图形无父元素，对齐的是图形的子元素
   *      3. 选中图形无父元素、无子元素，不对齐
   */
  const alignGraph = (type: string) => {
    const moveableRef = designStore.moveableRef.value;
    const activeGraphs = designStore.active.value.graphs;
    if (moveableRef) {
      const moveables = moveableRef.getMoveables();
      if (moveables.length == 1 && activeGraphs.length == 1) {
        const graph = activeGraphs[0];
        if (graph.parent) {
          // 选中图形有父元素，对齐是基于父元素的
          const parent = findGraph(graph.parent);
          if (parent) {
            const pw = parent?.width || 0;
            const ph = parent?.height || 0;
            singleAlign(type, graph, { pw, ph });
          }
        } else {
          // 选中图形无父元素，对齐的是图形的子元素
          const len = graph.children?.length || 0;
          if (graph.children?.length) {
            if (len === 1) {
              // 只有一个图形时，基于选中图形对齐
              singleAlign(type, graph.children[0], { pw: graph.width, ph: graph.height });
            } else {
              // 多个图形时，多图形对齐
              multipleAlign(type, graph.children);
            }
          }
        }

        //moveableRef.updateRect();
      }
    }
  };

  /**
   * 删除组件
   */
  const deleteGraphs = () => {
    const graphs = designStore.active.value.graphs;
    if (!graphs.length) {
      return;
    }
    let parent: any;
    let parentChildren: Graph[] = [];

    if (!graphs[0].parent) {
      parentChildren = designStore.active.value.page.children;
    } else {
      parent = findGraph(graphs[0].parent);
      parentChildren = parent.children;
    }
    graphs.forEach((graph) => {
      if ((graph as Block).decoration) {
        const cIndex = doc.value.blocks.findIndex((b) => b.id === (graph as Block).decoration);
        doc.value.blocks.splice(cIndex, 1);
      }
      const index = parentChildren.findIndex((g) => g.id === graph.id);
      parentChildren.splice(index, 1);
    });

    activeGraphs();
  };

  /**
   * 重命名
   * */
  const renameGraph = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      designStore.setEditingGraph(activeGraph);
    }
  };

  /**
   * 锁定/解锁图层
   * */
  const lockOrUnlockGraph = () => {
    const activeGraph = designStore.active.value.graph;
    const graph = findGraph((activeGraph as Graph).id);
    if (graph) {
      graph.locked = !graph.locked;
    }
  };

  /**
   * 显示/隐藏图层
   * */
  const showOrHideGraph = () => {
    const activeGraph = designStore.active.value.graph;
    const graph = findGraph((activeGraph as Graph).id);
    if (graph) {
      graph.visible = !graph.visible;
    }
  };

  /**
   * 将图形定位在画布的中央位置
   * */
  const locationToCanvas = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      designStore.rulerState.value.zoom = 1;
      designStore.infiniteCanvasRef.value?.setZoom(1);
      const { x, y } = getGraphXYByCanvas(activeGraph);

      // 获取dock高度
      const dockDom = document.querySelector('.vis-design-dock') as HTMLDivElement;
      const dockHeight = dockDom ? dockDom.offsetHeight + 10 : 0;

      const canvasDom = document.querySelector(`#${VIS_DESIGN_INFINITE_CANVAS}`) as HTMLDivElement;
      const canvasWidth = canvasDom.offsetWidth;
      const canvasHeight = canvasDom.offsetHeight - dockHeight;
      const offsetX = (canvasWidth - activeGraph.width) / 2;
      const offsetY = (canvasHeight - activeGraph.height) / 2;
      designStore.infiniteCanvasRef.value?.scrollTo(x - offsetX, y - offsetY);
    }
  };

  /**
   * 设置为主容器
   * */
  const setAsMainFrame = () => {
    const activeGraph = designStore.active.value.graph;
    const activePage = designStore.active.value.page;
    if (activeGraph && activePage) {
      const page = findPage(activePage.id);
      (page as Page).main = activeGraph.id;
    }
  };

  /**
   * 创建容器
   * */
  const createFrame = () => {
    const activeGraph = designStore.active.value.graph;
    if (!activeGraph) return;
    const graph = findGraph((<Block | Frame>activeGraph).id);
    if (graph) {
      // 创建容器
      const frame = new Frame();
      frame.name = `容器 ${counter.value}`;
      frame.width = graph.width;
      frame.height = graph.height;

      // 获取容器在画布的位置
      const { x, y } = getGraphXYByCanvas(graph);
      const targetId = graph.parent || VIS_DESIGN_INFINITE_CANVAS;

      // 查找父级
      let parent: any;
      let parentChildren: Graph[] = [];
      if (!graph.parent) {
        parentChildren = designStore.active.value.page.children;
      } else {
        parent = findGraph(graph.parent);
        parentChildren = parent.children;
      }

      // 在父级中删除图形
      const index = parentChildren.findIndex((item) => item.id === (<Block | Frame>graph).id);
      if (index !== -1) {
        parentChildren.splice(index, 1);
      }

      // 重置图形位置
      graph.transform.translate = [0, 0];
      // 维护父子关系
      graph.parent = frame.id;
      frame.children.push(<Block | Frame>graph);

      // 移动容器到目标位置
      frame.transform.translate[0] = x;
      frame.transform.translate[1] = y;
      moveGraphs([frame], targetId, index);
    }
  };

  /**
   * 取消容器
   * */
  const cancelFrame = () => {
    const activeGraph = designStore.active.value.graph;
    if (!activeGraph) return;
    const graph = findGraph((<Block | Frame>activeGraph).id);
    if (graph) {
      // 查找父级
      let parent: any;
      let parentChildren: Graph[] = [];
      let targetId: string = '';
      if (!graph.parent) {
        parentChildren = designStore.active.value.page.children;
        targetId = VIS_DESIGN_INFINITE_CANVAS;
      } else {
        parent = findGraph(graph.parent);
        parentChildren = parent.children;
        targetId = parent.id;
      }

      // 移动子级到当前容器
      if (graph.children?.length) {
        const insertIndex = parentChildren.findIndex((item) => item.id === (<Block | Frame>graph).id);
        moveGraphs([...graph.children], targetId, insertIndex);
      }

      // 在父级中删除容器
      const index = parentChildren.findIndex((item) => item.id === (<Block | Frame>graph).id);
      if (index !== -1) {
        parentChildren.splice(index, 1);
        parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
      }
    }
  };

  /**
   * 移动
   * @param direction 方向 up 上移一层 down 下移一层 top 移至顶层 bottom 移至底层
   * */
  const moveGraph = (direction: 'up' | 'down' | 'top' | 'bottom') => {
    const activeGraph = designStore.active.value.graph;
    if (!activeGraph) return;
    const graph = findGraph((<Graph>activeGraph).id);
    if (graph) {
      // 查找父级
      let parent: any;
      let parentChildren: Graph[] = [];
      if (!graph.parent) {
        parentChildren = designStore.active.value.page.children;
      } else {
        parent = findGraph(graph.parent);
        parentChildren = parent.children;
      }
      // 查找图形位置
      const index = parentChildren.findIndex((item) => item.id === graph.id);
      if (direction === 'up') {
        if (index === 0) return;
        if (index !== -1) {
          parentChildren.splice(index, 1);
        }
        parentChildren.splice(index - 1, 0, graph);
      } else if (direction === 'down') {
        if (index === parentChildren.length - 1) return;
        if (index !== -1) {
          parentChildren.splice(index, 1);
        }
        parentChildren.splice(index + 1, 0, graph);
      } else if (direction === 'top') {
        if (index === 0) return;
        if (index !== -1) {
          parentChildren.splice(index, 1);
        }
        parentChildren.unshift(graph);
      } else if (direction === 'bottom') {
        if (index === parentChildren.length - 1) return;
        if (index !== -1) {
          parentChildren.splice(index, 1);
        }
        parentChildren.push(graph);
      }

      // 更新order
      parentChildren.forEach((graph, index, _) => (graph.order = _.length - 1 - index));

      nextTick(() => {
        activeGraphs([graph]);
      });
    }
  };

  /**
   * 同层级上移一层
   * */
  const moveUpGraph = () => moveGraph('up');
  /**
   * 同层级下移一层
   * */
  const moveDownGraph = () => moveGraph('down');
  /**
   * 同层级移到顶层
   * */
  const moveTopGraph = () => moveGraph('top');
  /**
   * 同层级移到底层
   * */
  const moveBottomGraph = () => moveGraph('bottom');

  /**
   * 复制
   * */
  const copyGraph = () => {
    const activeGraph = designStore.active.value.graph;
    designStore.copyGraphId.value = activeGraph ? activeGraph.id : '';
  };

  /**
   * 粘贴
   * */
  const pasteGraph = () => {
    const copyGraphId = designStore.copyGraphId.value;
    if (copyGraphId) {
      // 复制图形
      const copyGraph = findGraph(copyGraphId);
      if (!copyGraph) return;
      const graphWidth = copyGraph.width;
      // const graphHeight = copyGraph.height;

      // 克隆图形
      const newGraph = customClone(copyGraph);
      // 有无选中
      const activeGraph = designStore.active.value.graph;
      if (activeGraph) {
        // 获取图形在画布位置
        const { x: activeX, y: activeY } = getGraphXYByCanvas(activeGraph as Graph);

        // 目标位置
        let targetId: string = '';

        // 选中为容器，添加到容器中
        if (activeGraph.type === GraphType.Frame) {
          // 是否为自身
          if (activeGraph.id !== copyGraphId) {
            // 添加到容器中，0 0 位置
            newGraph.transform.translate[0] = activeX;
            newGraph.transform.translate[1] = activeY;
            targetId = activeGraph.id;
          } else {
            // 添加到容器中，0 20 位置
            newGraph.transform.translate[0] = activeX + graphWidth + 20;
            newGraph.transform.translate[1] = activeY;
            targetId = activeGraph.parent;
          }
        } else {
          // 非容器类型，是否有父级
          if (activeGraph.parent) {
            newGraph.transform.translate[0] = activeX + graphWidth + 20;
            newGraph.transform.translate[1] = activeY;
            targetId = activeGraph.parent;
          } else {
            newGraph.transform.translate[0] = activeX + graphWidth + 20;
            newGraph.transform.translate[1] = activeY;
            targetId = VIS_DESIGN_INFINITE_CANVAS;
          }
        }
        moveGraphs([newGraph], targetId ? targetId : VIS_DESIGN_INFINITE_CANVAS, 0);
        return;
      }

      // 获取图形在画布的位置
      const { x: graphX, y: graphY } = getGraphXYByCanvas(copyGraph as Graph);

      // 有无父级
      if (copyGraph.parent) {
        // 移动到该元素右侧20px位置
        newGraph.transform.translate[0] = graphX + graphWidth + 20;
        newGraph.transform.translate[1] = graphY;
        moveGraphs([newGraph], copyGraph.parent, 0);
      } else {
        // 根节点，粘贴到该元素右侧20px位置
        newGraph.transform.translate[0] = graphX + graphWidth + 20;
        newGraph.transform.translate[1] = graphY;
        // 否则添加到根层级
        newGraph.parent = '';
        addGraphToPage(newGraph);
      }
    }
  };

  /**
   * 复制图形ID
   * */
  const copyGraphId = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      copyToClipboard(activeGraph.id)
        .then(() => {
          Notify.create({
            classes: 'vis-design-notify',
            position: 'top',
            type: 'positive',
            message: '复制成功'
          });
        })
        .catch(() => {
          Notify.create({
            classes: 'vis-design-notify',
            position: 'top',
            type: 'negative',
            message: '复制失败'
          });
        });
    }
  };

  /**
   * 复制图形样式
   * */
  const copyGraphStyle = () => {
    const activeGraph = designStore.active.value.graph;
    if (activeGraph) {
      const ele = document.querySelector(`[id="${activeGraph.id}"]`) as HTMLElement;
      // const computedStyles = window.getComputedStyle(ele);
      const computedStyles = ele.style;
      const properties: any = [];
      // 过滤无效样式
      for (let i = 0; i < computedStyles.length; i++) {
        const property = computedStyles[i];
        const value = computedStyles.getPropertyValue(property);
        const isImportant = computedStyles.getPropertyPriority(property) === 'important';
        if (!value || value === 'initial') continue;
        properties.push({
          property,
          value,
          isImportant
        });
      }

      // 布局样式
      const wrap = document.querySelector(`[id="${activeGraph.id}"] > .wrap`) as HTMLElement;
      const computedWrapStyles = wrap.style;
      const layoutProperties: any = [];
      for (let i = 0; i < computedWrapStyles.length; i++) {
        const property = computedWrapStyles[i];
        const value = computedWrapStyles.getPropertyValue(property);
        const isImportant = computedWrapStyles.getPropertyPriority(property) === 'important';
        if (!value || value === 'initial') continue;
        layoutProperties.push({
          property,
          value,
          isImportant
        });
      }

      // 格式化样式
      const graphBlocks = properties
        .map((item: any) => {
          return `${item.property}: ${item.value}${item.isImportant ? ' !important' : ''};`;
        })
        .join('\n');

      let layoutBlocks: string = '';
      if (activeGraph.type === GraphType.Frame) {
        if ((activeGraph as Frame).autoLayout.direction !== DirectionType.Freeform) {
          layoutBlocks = layoutProperties
            .map((item: any) => {
              return `${item.property}: ${item.value}${item.isImportant ? ' !important' : ''};`;
            })
            .join('\n');
        }
      }

      const layoutMap: Record<DirectionType, string> = {
        [DirectionType.Freeform]: '/** 自由布局 */',
        [DirectionType.Horizontal]: '/** 水平布局 */',
        [DirectionType.Vertical]: '/** 垂直布局 */',
        [DirectionType.Grid]: '/** 网格布局 */'
      };

      const styleBlocks = [
        graphBlocks,
        layoutBlocks && layoutMap[(activeGraph as Frame).autoLayout.direction],
        layoutBlocks
      ]
        .filter((item) => item)
        .join('\n');

      // 复制
      copyToClipboard(styleBlocks)
        .then(() => {
          Notify.create({
            classes: 'vis-design-notify',
            position: 'top',
            type: 'positive',
            message: '复制成功'
          });
        })
        .catch(() => {
          Notify.create({
            classes: 'vis-design-notify',
            position: 'top',
            type: 'negative',
            message: '复制失败'
          });
        });
    }
  };

  /**
   * 复制图形配置
   * */
  const copyGraphConfig = () => {
    //
  };

  /**
   * 粘贴图形配置
   * */
  const pasteGraphConfig = () => {
    //
  };

  /**
   * 粘贴并替换
   * */
  const pasteAndReplaceGraph = () => {
    //
  };

  /**
   * 粘贴到鼠标位置
   * */
  const pasteToMousePosition = () => {
    const copyGraphId = designStore.copyGraphId.value;
    if (copyGraphId) {
      // 复制图形
      const copyGraph = findGraph(copyGraphId);
      if (!copyGraph) return;
      // 克隆图形
      const newGraph = customClone(copyGraph);
      // 鼠标位置
      const mousePosition = (designStore.contextMenuRef.value as ContextMenuInstance).position;
      // 获取位置
      const { x, y } = getCanvasPosSize(mousePosition.x, mousePosition.y);
      newGraph.transform.translate[0] = x;
      newGraph.transform.translate[1] = y;
      // 添加图形
      newGraph.parent = '';
      addGraphToPage(newGraph);
    }
  };

  /**
   * 全选
   * */
  const selectGraphAll = () => {
    const graphs = designStore.active.value.page.children;
    // 过滤锁定/隐藏的图形
    const activeIds = graphs.filter((g: Graph) => !g.locked && g.visible).map((g: Graph) => g.id);
    nextTick(() => {
      activeGraphs(activeIds);
    });
  };

  /**
   * 显示/隐藏操作面板
   * */
  const showOrHideOperationPanel = () => {
    designStore.leftWidth.value = designStore.leftWidth.value ? 0 : 240;
    designStore.rightWidth.value = designStore.rightWidth.value ? 0 : 240;
  };

  return {
    move,
    hand,
    frame,
    textbox,

    dhyana,

    align,

    deleteGraphs,

    renameGraph,
    lockOrUnlockGraph,
    showOrHideGraph,
    locationToCanvas,
    setAsMainFrame,
    createFrame,
    cancelFrame,
    moveUpGraph,
    moveDownGraph,
    moveTopGraph,
    moveBottomGraph,
    copyGraph,
    pasteGraph,
    copyGraphId,
    copyGraphStyle,
    copyGraphConfig,
    pasteGraphConfig,
    pasteAndReplaceGraph,
    pasteToMousePosition,
    selectGraphAll,
    showOrHideOperationPanel
  };
};
