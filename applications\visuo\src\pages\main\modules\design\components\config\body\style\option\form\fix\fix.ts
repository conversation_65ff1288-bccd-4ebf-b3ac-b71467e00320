import { computed, defineComponent, ref, type PropType } from 'vue';
import { FontConfig, useDocumentStore, type FormFix } from '@vis/document-core';
/**
 * 前后缀面板
 */
export default defineComponent({
  name: 'vis-config-form-fix',
  props: {
    options: {
      type: Object as PropType<FormFix>,
      required: true
    }
  },
  setup(props) {
    const computedOptions = computed({
      get() {
        return props.options;
      },
      set(value) {
        Object.assign(props.options, value);
      }
    });

    const isIcon = computed(() => computedOptions.value.type === 'icon');

    const showMenuType = ref(false);

    const handleType = (value: 'text' | 'icon') => {
      computedOptions.value.type = value;
    };

    const handleVisible = () => {
      computedOptions.value.show = !computedOptions.value.show;
    };

    // #region 弹窗
    const popupRef = ref();
    const iconPickerRef = ref();
    const showPopup = (e: Event) => {
      e.stopPropagation();
      if (isIcon.value) {
        iconPickerRef.value?.showPopup(e);
      } else {
        popupRef.value?.handleShow(e);
      }
    };

    const docStore = useDocumentStore();
    const fontFamilyOptions = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });
    const { fontWeights: fontWeightOptions, fontSizes: fontSizeOptions } = new FontConfig();

    // #endregion

    const fixIconName = computed(() => {
      return isIcon.value && computedOptions.value.icon.name;
    });

    return {
      computedOptions,
      isIcon,
      showMenuType,
      handleType,

      handleVisible,

      popupRef,
      showPopup,
      fontFamilyOptions,
      fontWeightOptions,
      fontSizeOptions,
      iconPickerRef,

      fixIconName
    };
  }
});
