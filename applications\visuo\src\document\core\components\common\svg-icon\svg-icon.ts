import { computed, defineComponent, onBeforeUnmount, onMounted, ref, watch, type PropType } from 'vue';
import { Icon } from '../../../models';

export default defineComponent({
  name: 'vis-svg-icon',
  props: {
    icon: {
      type: Object as PropType<Icon>,
      required: true
    },
    lazy: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const href = ref('');
    const observer = ref<IntersectionObserver>();
    const svgRef = ref();

    const viewBox = computed(() => {
      return props.icon.viewBox || '0 0 32 32';
    });

    const setHref = () => {
      const src = `./static/icons/${props.icon.typeName}/${props.icon.name}.svg`;
      href.value = `${src}#${props.icon.linkId || 'shape'}`;
    };

    watch(
      [() => props.icon.name, () => props.icon.typeName, () => props.icon.linkId],
      (newVal, oldVal) => {
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return;
        setHref();
      },
      { deep: true }
    );

    const addObserver = () => {
      observer.value = new IntersectionObserver((entries) => {
        entries.forEach(function (entry) {
          if (entry.isIntersecting) {
            setHref();
            svgRef.value && observer.value?.unobserve(svgRef.value);
          }
        });
      });
      observer.value.observe(svgRef.value);
    };

    onMounted(() => {
      if (props.lazy) {
        addObserver();
      } else {
        setHref();
      }
    });

    onBeforeUnmount(() => {
      props.lazy && observer.value?.disconnect();
    });

    return {
      href,
      svgRef,
      viewBox,
      setHref
    };
  }
});
