<template>
  <div class="vis-fill-image w-full">
    <vis-select
      v-if="!isIcon"
      v-model="image.objectFit"
      :options="fitOptions"
      class="q-mb-xs !px-0 no-outline w-12"
    ></vis-select>
    <q-card flat bordered fit="scale-down">
      <q-img
        :src="imageUrl"
        @click="selectImage"
        fit="contain"
        :placeholder-src="'./static/img/placeholder.png'"
        width="100%"
        :height="`${isSidebar ? 100 : 125}px`"
      >
        <template v-slot:error>
          <img :src="'./static/img/placeholder.png'" />
        </template>
        <div
          v-if="!imageUrl"
          class="absolute-full vis-field--mini flex flex-center cursor-pointer bg-transparent text-grey-7"
          :style="{ 'font-size': isSidebar ? '10px' : '12px' }"
        >
          <!-- 选择图片 -->
          <ht-icon class="vis-icon" name="vis-navigation-pic"></ht-icon>
        </div>
      </q-img>
      <q-separator />
      <q-card-section class="!p-0">
        <q-input
          v-model="inputUrl"
          @change="onSetUrl"
          dense
          borderless
          placeholder="请输入图片地址"
          class="vis-field--mini q-pl-xs"
          :input-style="`font-size: ${isSidebar ? 10 : 12}px`"
        >
          <template v-slot:prepend>
            <q-icon name="link"></q-icon>
          </template>
        </q-input>
      </q-card-section>
    </q-card>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
