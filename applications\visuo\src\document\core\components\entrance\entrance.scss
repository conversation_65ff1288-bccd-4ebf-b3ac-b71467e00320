.#{$vis-prefix}-graph {
  box-shadow: var(--box-shadow, initial);
  filter: var(--filter, initial);
  backdrop-filter: var(--backdrop-filter, initial);

  * {
    border-radius: inherit;
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    border-radius: inherit;
    background: var(--background, initial);
  }

  &::after {
    content: '';
    position: absolute;
    display: block;
    z-index: -1;
    border-radius: inherit;
    left: var(--border-offset-left, initial);
    top: var(--border-offset-top, initial);
    width: var(--border-offset-width, initial);
    height: var(--border-offset-height, initial);
    border: var(--border, initial);
    border-width: var(--border-width, initial);
    border-image: var(--border-image, initial);
  }
}