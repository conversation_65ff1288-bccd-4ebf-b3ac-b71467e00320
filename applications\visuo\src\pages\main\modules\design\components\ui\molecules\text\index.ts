import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import {
  Text,
  FontConfig,
  Font,
  TextAlign,
  VerticalAlign,
  TextAdapt,
  useDocumentStore,
  TextEffects
} from '@vis/document-core';
import { useDesignStore } from '../../../../stores';

/**
 * 字体设置
 * 支持四种模式: base-极简模式, simple-基本模式, full-全量模式, data-数据模式
 * 极简模式下支持字体、字号、粗细、颜色
 * 基本模式在极简模式下添加支持字间距、行高、对齐方式和适应方式
 * 数据模式为根据传入的option自动计算设置列表
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-text',
  props: {
    option: {
      type: Object as PropType<Text | Font>,
      required: true
    },
    // 模式：默认根据数据展示设置列表
    mode: {
      type: String,
      default: 'data'
    },
    /**
     * 非全量模式下允许自定义设置列表，可选值为：
     * letterSpacing-字间距
     * lineHeight-行高
     * alignHorizontal-对齐方式
     * adapt-适应方式
     * fontStyle-文字修饰
     * alignVertical-垂直对齐
     * listStyle-列表样式
     */
    extra: {
      type: Array,
      default: () => []
    },
    mini: {
      type: Boolean,
      default: false
    },

    /**
     * 填充颜色标签
     */
    fillLabel: String,

    /**
     * 字符title
     */
    textTitle: String,

    /**
     * 文字特效，只有mini模式下显示
     */
    textEffects: {
      type: Object as PropType<TextEffects>
    }
  },
  setup(props) {
    /**
     * 计算extra的值，判定哪些设置需要显示
     */
    const extraValue = computed(() => {
      let extra = props.extra;
      if (props.mode === 'data') {
        const keys = Object.keys(props.option);
        if (keys.includes('italic') || keys.includes('underlined') || keys.includes('through')) {
          keys.push('fontStyle');
        }
        // 过滤掉极简模式支持的属性和修饰属性
        extra = keys.filter(
          (item) => !['fontSize', 'fontFamily', 'fontWeight', 'color', 'italic', 'underlined', 'through'].includes(item)
        );
      }
      // 处理特殊情况（extra的值在基本模式下已被支持）
      /*   if (props.mode === 'simple' || props.mode === 'data') {
        return extra.filter((item: any) => !['letterSpacing', 'lineHeight', 'alignHorizontal', 'adapt'].includes(item));
      } */
      return extra;
    });

    const isBase = computed(() => {
      return props.mode === 'base';
    });

    const isFull = computed(() => {
      return props.mode === 'full';
    });

    // 是否显示更多按钮
    const isMore = computed(() => {
      return props.mode === 'full' || extraValue.value.length > 0;
    });

    const isData = computed(() => {
      return props.mode === 'data';
    });

    /**
     * 外层设置是否显示
     * @param name
     * @returns
     */
    const showOuter = (name: string) => {
      if (isData.value) {
        // 数据模式下显示哪些设置
        return Object.keys(props.option).includes(name);
      }
      return !isBase.value;
    };

    const showSetting = (name: string) => {
      return extraValue.value.includes(name);
    };

    const { fontSizes, fontWeights } = new FontConfig();

    const docStore = useDocumentStore();

    const fontFamilys = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });

    const computedOption = computed({
      get() {
        return props.option as Text;
      },

      set(value) {
        Object.assign(props.option, value);
      }
    });

    // 设置斜体、下划线和删除线
    const fontStyles = [
      { value: 'italic', label: '', icon: 'vis-format_italic', tip: '斜体' },
      { value: 'underlined', label: '', icon: 'vis-format_underline', tip: '下划线' },
      { value: 'through', label: '', icon: 'vis-format_through', tip: '删除线' }
    ];
    // 解决vue文件类型报错
    const fontStyleKeys: ('italic' | 'underlined' | 'through')[] = ['italic', 'underlined', 'through'];

    const isIconFont = (name: string) => {
      return name.startsWith('vis');
    };

    const visible = ref(props.option.color.visible);
    // 文本颜色显隐
    const handleVisible = () => {
      visible.value = !visible.value;
      computedOption.value.color.visible = visible.value;
    };

    /**
     * 设置文字修饰--斜体、下划线和删除线
     * @param val
     * @returns
     */
    const setFontStyle = (val: 'italic' | 'underlined' | 'through') => {
      if (computedOption.value[val] === undefined) {
        return;
      }
      computedOption.value[val] = !computedOption.value[val];

      // 下划线和删除线不能同时选中
      if (computedOption.value[val]) {
        if (val === 'underlined') {
          computedOption.value.through = false;
        } else if (val === 'through') {
          computedOption.value.underlined = false;
        }
      }
    };

    // 对齐方式
    const alignHorizontalOptions = [
      { value: TextAlign.Left, label: '', icon: 'vis-text-left', tip: '左对齐' },
      { value: TextAlign.Center, label: '', icon: 'vis-text-center', tip: '居中对齐' },
      { value: TextAlign.Right, label: '', icon: 'vis-text-right', tip: '右对齐' }
    ];

    const alignHorizontalPopOptions = alignHorizontalOptions.concat([
      { value: TextAlign.Justify, label: '', icon: 'vis-text-justify', tip: '两端对齐' }
    ]);

    // 垂直对齐
    const alignVerticalOptions = [
      { value: VerticalAlign.Top, label: '', icon: 'vis-vertical_t', tip: '顶部对齐' },
      { value: VerticalAlign.Center, label: '', icon: 'vis-vertical_c', tip: '居中对齐' },
      { value: VerticalAlign.Bottom, label: '', icon: 'vis-vertical_b', tip: '底部对齐' }
    ];

    // 展示模式
    const adaptOptions = [
      { value: TextAdapt.Single, label: '', icon: 'vis-single', tip: '单行模式' },
      { value: TextAdapt.Auto, label: '', icon: 'vis-auto', tip: '自动高度' },
      { value: TextAdapt.Fixed, label: '', icon: 'vis-fixed', tip: '固定宽高' }
    ];

    const adaptPopOptions = adaptOptions.concat([
      { value: TextAdapt.Ellipsis, label: '', icon: 'vis-ellipsis', tip: '省略文本' }
    ]);

    // 列表样式
    // const listOptions = [
    //   { value: 'none', label: '—', icon: '', tip: '无列表' },
    //   { value: 'unordered', label: '', icon: 'format_list_bulleted', tip: '无序列表' },
    //   { value: 'ordered', label: '', icon: 'format_list_numbered', tip: '有序列表' }
    // ];

    const hoverOption = ref<Text>(JSON.parse(JSON.stringify(computedOption.value)) as Text);

    const isHover = ref(false);

    const handlerHoverOption = (val: string | boolean, label: string) => {
      isHover.value = val === false ? false : true;

      if (val) {
        if (label === 'fontStyle') {
          hoverOption.value.italic = val === 'italic';
          hoverOption.value.underlined = val === 'underlined';
          hoverOption.value.through = val === 'through';
        } else {
          (hoverOption.value[label as keyof Text] as string) = val as string;
        }
      } else {
        hoverOption.value = JSON.parse(JSON.stringify(computedOption.value)) as Text;
      }
    };

    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    return {
      showOuter,
      showSetting,
      isBase,
      isFull,
      isMore,

      fontFamilys,
      fontSizes,
      fontWeights,
      computedOption,
      isHover,
      hoverOption,
      visible,
      handlerHoverOption,
      handleVisible,

      fontStyles,
      fontStyleKeys,
      isIconFont,
      setFontStyle,

      alignHorizontalOptions,
      alignHorizontalPopOptions,
      alignVerticalOptions,
      adaptOptions,
      adaptPopOptions,
      extraValue,
      // listOptions

      popupRef,
      popupShow,
      showPopup
    };
  }
});
