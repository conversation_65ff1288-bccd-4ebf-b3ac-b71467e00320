import { Color, FillPaints } from './fill';
/**
 * 描边
 * <AUTHOR>
 */
export class Stroke {
  /** 描边宽度 */
  position: [number, number, number, number] = [1, 1, 1, 1];
  /** 对齐方式 */
  align = StrokeAlign.Inside;
  /** 描边填充 */
  paints: FillPaints = new FillPaints();
  /** 描边类型 */
  style = StrokeType.Solid;

  constructor(
    position?: [number, number, number, number],
    align?: StrokeAlign,
    paints?: FillPaints,
    style?: StrokeType
  ) {
    position && (this.position = position);
    align && (this.align = align);
    paints && (this.paints = paints);
    style && (this.style = style);
  }
}

export enum StrokeAlign {
  /** 居中 */
  Center = 'center',
  /** 内部 */
  Inside = 'inside',
  /** 外部 */
  Outside = 'outside'
}
export enum StrokeType {
  /** 无 */
  None = 'none',
  /** 实线 */
  Solid = 'solid',
  /** 虚线 */
  Dashed = 'dashed',
  /** 点线 */
  Dotted = 'dotted'
}
