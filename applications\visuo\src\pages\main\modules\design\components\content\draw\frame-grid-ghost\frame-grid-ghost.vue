<template>
  <div class="vis-frame-grid-layout-ghost" v-if="isGhost && isMoveable" :style="style" @click="onGhostClick">
    <div class="left" :style="ghostLeftStyle">
      <div class="hand" :style="rowsStyle">
        <div v-for="i in gridSize[0]" :key="i" class="item" :class="{ active: rowsSize[i - 1].state === 'active' }">
          <div
            class="tip ghost"
            :class="{
              '!visible': i === hoverRowCol[0] || rowsSize[i - 1].state === 'edit' || rowsSize[i - 1].state === 'active'
            }"
            @click="onClickTip(i - 1, 'row')"
          >
            <span v-if="rowsSize[i - 1].state === '' || rowsSize[i - 1].state === 'active'" class="text ghost">{{
              rowsSize[i - 1].value
            }}</span>
            <q-input
              v-else
              ref="inputRef"
              v-model="rowsSize[i - 1].value"
              @blur="onBlurSize(i - 1, 'row')"
              @keyup.enter="onBlurSize(i - 1, 'row')"
              class="vis-input rounded-borders px-0 pr-0 vis-field--mini"
              type="text"
              borderless
              dense
            />
          </div>
          <div class="rect ghost" v-if="rowsSize[i - 1].state !== ''" :style="{ width: activeGraph.width + 'px' }">
            <div
              :class="`drag ${po}`"
              v-for="po in ['t', 'b']"
              :key="po"
              @mousedown="onMouseDown($event, i - 1, 'row')"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div class="top" :style="ghostTopStyle">
      <div class="hand" :style="colsStyle">
        <div class="ghost item" v-for="i in gridSize[1]" :key="i">
          <div
            class="tip"
            :class="{
              '!visible':
                i === hoverRowCol[1] || columnsSize[i - 1].state === 'edit' || columnsSize[i - 1].state === 'active'
            }"
            @click="onClickTip(i - 1, 'col')"
          >
            <span v-if="columnsSize[i - 1].state === '' || columnsSize[i - 1].state === 'active'" class="text ghost">{{
              columnsSize[i - 1].value
            }}</span>
            <q-input
              v-else
              ref="inputRef"
              v-model="columnsSize[i - 1].value"
              @blur="onBlurSize(i - 1, 'col')"
              @keyup.enter="onBlurSize(i - 1, 'col')"
              class="vis-input rounded-borders px-0 pr-0 vis-field--mini"
              type="text"
              borderless
              dense
            />
          </div>
          <div class="rect ghost" v-if="columnsSize[i - 1].state !== ''" :style="{ height: activeGraph.height + 'px' }">
            <div
              :class="`drag  ${po}`"
              v-for="po in ['l', 'r']"
              :key="po"
              @mousedown="onMouseDown($event, i - 1, 'col')"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./frame-grid-ghost.ts"></script>
<style lang="scss" src="./frame-grid-ghost.scss"></style>
