import { FillPaints, Stroke } from '../../ui';
import { Label } from './label';
import { Title } from './title';

/**
 * 图表坐标轴类
 * 用于定义图表的X轴、Y轴等坐标轴的属性和样式
 */
export class Axis {
  /** 是否显示坐标轴 */
  visible: boolean = true;

  /** 数值最小范围 */
  minExtent: number = 0;

  /** 数值最大范围 */
  maxExtent: number = 100;

  /** 是否反转坐标轴方向 */
  inverse: boolean = false;

  /** 是否使用对数刻度 */
  logarithmic: boolean = false;

  /** 坐标轴两端间距 */
  spacing: number = 0;

  /** 坐标轴标题 */
  title: Title = new Title();

  /** 坐标轴标签 */
  label: Label = new Label();

  /** 网格线配置 */
  grid: AxisGrid = new AxisGrid();

  /** 轴线配置 */
  line: AxisLine = new AxisLine();

  /** 刻度线配置 */
  tick: AxisTick = new AxisTick();
}

/**
 * 坐标轴网格线配置类
 * 控制坐标轴网格线的显示和样式
 */
export class AxisGrid {
  /** 是否显示网格线 */
  visible: boolean = false;

  /** 网格区域填充样式 */
  fillPaints: Array<FillPaints> = [new FillPaints(), new FillPaints()];

  /** 网格线宽度 */
  width: number = 1;

  /** 网格线描边样式 */
  stroke: Stroke = new Stroke();
}

/**
 * 坐标轴轴线配置类
 * 控制坐标轴主线的显示和样式
 */
export class AxisLine {
  /** 是否显示轴线 */
  visible: boolean = false;

  /** 轴线宽度 */
  width: number = 1;

  /** 轴线描边样式 */
  stroke: Stroke = new Stroke();
}

/**
 * 坐标轴刻度线配置类
 * 控制坐标轴刻度线的显示和样式
 */
export class AxisTick {
  /** 是否显示刻度线 */
  visible: boolean = false;

  /** 刻度线宽度 */
  width: number = 0;

  /** 刻度线长度 */
  length: number = 0;

  /** 刻度初始值 */
  minSize: number = 0;

  /** 刻度最小步长 */
  minStep: number = 0;

  /** 刻度线描边样式 */
  stroke: Stroke = new Stroke();
}
