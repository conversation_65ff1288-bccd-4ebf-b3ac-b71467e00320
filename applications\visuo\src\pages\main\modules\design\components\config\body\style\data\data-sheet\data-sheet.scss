@import '../../../../../index.scss';

.#{$vis-prefix}-data {

  &-sheet {
    display: flex;
    flex-direction: column;
    font-size: $primary-font-size;
    border: 1px solid $separator-color;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);


    &__title {
      display: flex;
      background: $input-bg;
      border-bottom: 1px solid $separator-color;
    }

    &__text {
      @apply px-3;
      font-weight: $title-font-weight;
      font-size: $primary-font-size;
    }

    &__toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      &-right {
        display: flex;
        align-items: center;
        gap: $field-gap;
      }

      .q-btn {
        background: transparent;
        border-radius: $border-radius;
        transition: all 0.15s ease;
        min-width: 24px;
        height: 24px;

        .q-icon {
          font-size: 14px;
        }

        &.active {
          background: rgba($select-active-color, 0.1);
          color: $select-active-color;
        }

        &:hover {
          background: $input-hover-color;
        }

        &:disabled {
          opacity: 0.5;
        }

      }

      .q-separator {
        margin: 0 2px;
        width: 1px;
        height: 28px;
      }

    }

    &__container-wrapper {
      outline: none;
      background: $input-bg;

      &:focus {
        outline: none;
      }
    }

    // 移除整个组件的焦点轮廓
    &:focus {
      outline: none;
    }

    // 提示信息样式
    &__tips {
      position: absolute;
      top: $secondary-margin;
      right: $secondary-margin;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: $field-gap;
    }

    &__tip {
      padding: 6px 12px;
      border-radius: $border-radius;
      font-size: $primary-font-size;
      font-weight: $title-font-weight;
      color: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      animation: slideIn 0.3s ease-out;

      &--warning {
        background: $info;
      }

      &--info {
        background: $primary;
      }

      &--success {
        background: $positive;
      }
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateX(100%);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    &__tabs {
      display: flex;
      align-items: center;
      background: $input-bg;
      border-bottom: 1px solid $separator-color;
      min-height: 40px;
      padding: 0 $primary-margin;

      &-container {
        align-items: center;
        flex: 1;
        gap: 0;

        .q-scrollarea__content {
          display: flex;
          align-items: center;
          gap: 0;
        }

      }
    }


    &__tab {
      display: flex;
      align-items: center;
      background: transparent;
      border: none;
      border-bottom: 2px solid transparent;
      border-radius: $border-radius $border-radius 0 0;
      cursor: pointer;
      user-select: none;
      max-width: 150px;
      min-width: 60px;
      transition: all 0.2s ease;
      position: relative;
      height: 28px;
      flex-shrink: 0;

      &:hover {
        background: rgba(0, 0, 0, 0.04);
      }

      &--active {
        background: transparent;
        border-bottom-color: $select-active-color;
        color: $select-active-color;
        font-weight: 500;

        &:hover {
          background: rgba($select-active-color, 0.04);
          color: $select-active-color;
        }
      }

      &--readonly {
        opacity: 0.7;
        color: $label-color;
        cursor: default;

        &:hover {
          background: transparent;
        }

        &--active {
          border-bottom-color: $label-color;
          color: $label-color;
        }
      }

      &-text {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 24px;
        padding: 2px 8px;
      }

      &-rename-input {
        flex: 1;
        border: none;
        outline: none;
        background: transparent;
        font-family: inherit;
        color: inherit;
        padding: 0;
        margin: 0;
        min-width: 60px;
        font-weight: inherit;
      }

      &-close {
        opacity: 0;
        min-width: 24px;
        height: 24px;

        &:hover {
          opacity: 1;
          background: none !important;
          color: $negative;
        }
      }
    }

    &__tab:hover &__tab-close {
      opacity: 0.6;
    }

    &__add-sheet {
      display: flex;
      align-items: center;
      margin-left: 4px;
      min-width: 24px;
      max-width: 24px;
      height: 24px;
      flex-shrink: 0;

      .q-btn {
        background: transparent;
        transition: all 0.2s ease;
        min-width: 24px;
        height: 24px;

        &:hover {
          background: $input-hover-color;
        }
      }
    }

    &__header {
      display: flex;
      background: $input-bg;
      border-bottom: 1px solid $separator-color;

      &-cell {
        position: relative;
        min-width: 100px;
        height: 36px;
        padding: 0 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid $separator-color;
        cursor: pointer;
        user-select: none;
        color: $label-color;
        background: $input-bg;
        box-sizing: border-box;

        &:not(&--selected):hover {
          background: $input-hover-color;
        }

        &--selected {
          background: rgba($select-active-color, 0.3);

        }
      }

      &-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
        font-size: 14px;
      }

      &-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 4px;
      }
    }

    &__container {
      position: relative;
      overflow: hidden;
      background: transparent;
      outline: none;

      &:focus {
        outline: none;
      }

      // 冻结时的滚动容器样式
      &.has-frozen {
        .scroll {
          overflow: hidden;
        }
      }
    }

    // 字段类型选择器样式
    &__type-selector {
      position: relative;
      display: flex;
      align-items: center;

      .q-btn {
        color: $select-active-color;
        background: none;

        .vis-icon {
          font-size: 16px;
        }

        &:hover {
          background: none;
          color: $select-active-color;
        }
      }

    }

    &__resize-handle {
      position: absolute;
      z-index: 10;
      right: -6px;
      top: 0;
      bottom: 0;
      width: 12px;
      cursor: col-resize;
      background: transparent;
      transition: background-color 0.15s ease;

      &:hover {
        background: rgba($select-active-color, 0.4);
      }

      &--disabled {
        cursor: default;
        pointer-events: none;

        &:hover {
          background: transparent;
        }
      }
    }

    &__corner {
      width: 50px;
      min-width: 50px;
      height: 36px;
      background: $input-bg;
      border-right: 1px solid $separator-color;
    }

    &__body {
      display: flex;
      flex-direction: column;
      background: #fff;
    }

    &__row {
      display: flex;
      border-bottom: 1px solid $separator-color;
      transition: background-color 0.15s ease;

    }

    &__row-header {
      width: 50px;
      min-width: 50px;
      height: 32px;
      padding: 0 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $input-bg;
      border-right: 1px solid $input-hover-color;
      cursor: pointer;
      user-select: none;
      transition: all 0.15s ease;


      &:not(&--selected):hover {
        background: $input-hover-color;
      }

      &--selected {
        // 背景不使用透明度设置同样的颜色
        background: mix($select-active-color, white, 30%);
      }
    }

    &__cell {
      position: relative;
      min-width: 100px;
      height: 32px;
      padding: 0 8px;
      border-right: 1px solid $separator-color;
      cursor: cell;
      outline: none;
      transition: all 0.15s ease;
      display: flex;
      align-items: center;
      box-sizing: border-box;

      &:not(&--selected):hover {
        background: $input-hover-color;
      }

      &--selected {
        background: rgba($select-active-color, 0.1);
        box-sizing: border-box;

        &-top {
          border-top: 1px solid $select-active-color;
        }

        &-bottom {
          border-bottom: 1px solid $select-active-color;
        }

        &-left {
          box-shadow: inset 1px 0 0 0 $select-active-color;
        }

        &-right {
          border-right: 1px solid $select-active-color;
        }
      }

      &:not(&--selected)--focused {
        background: rgba($select-active-color, 0.2);
        box-shadow: inset 0 0 0 1px $select-active-color;
      }

      &--editing {
        padding: 0;
        background: #fff;
        box-shadow: inset 0 0 0 1px $select-active-color;
        overflow: hidden;
      }

      &--readonly {
        cursor: default;
        opacity: 0.8;
        background: $input-bg;

        &:hover {
          background: $input-bg;
        }

        &:not(&--selected):hover {
          background: $input-bg;
        }
      }

    }



    &__cell-content {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      line-height: 1.4;
      flex: 1;
    }

    &__edit-input {
      width: 100%;
      height: 100%;
      border: none;
      outline: none;
      padding: 0 8px;
      background: transparent;
      box-sizing: border-box;
      line-height: 1.4;
      margin: 0;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }


    // 调整列宽时的全局样式
    &.resizing {
      cursor: col-resize;
      user-select: none;

      // 拖动时禁用文本选择
      * {
        user-select: none;
      }

    }

    // 列宽调整线样式
    &__resize-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 1px;
      background: $select-active-color;
      z-index: 1000;
      pointer-events: none;
      opacity: 0.8;

      transition: none;

      // 添加更明显的视觉效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -1px;
        right: -1px;
        bottom: 0;
        background: rgba($select-active-color, 0.1);
        z-index: -1;
      }


      left: 0;
    }

  }
}

// 对话框样式优化
.q-dialog {

  &__inner {
    padding: 0;
  }

  // 全屏模式样式
  &.fullscreen {
    .vis-data-sheet {
      max-width: 100vw;
      max-height: 100vh;
      margin: 0;

      &__tips {
        top: 40px;
        right: 40px;
      }
    }
  }
}