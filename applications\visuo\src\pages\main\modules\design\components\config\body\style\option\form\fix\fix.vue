<template>
  <div class="vis-form-field">
    <div class="vis-fill__content flex flex-nowrap" :class="{ disabled: !computedOptions.show }">
      <q-btn flat dense @click="showPopup">
        <vis-svg-icon v-if="fixIconName" :icon="computedOptions.icon" style="width: 12px; height: 12px" />
        <ht-icon v-else :name="isIcon ? 'hticon-vis-suffix' : 'hticon-vis-textbox'" class="vis-icon" />

        <vis-popup v-if="!isIcon" title="字体设置" ref="popupRef" :target="false">
          <div class="vis-form-inline">
            <div class="vis-form-field">
              <div class="vis-form-field__label">颜色</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="computedOptions.font.color" :showEyes="false" :minusWidth="0" />
              </div>
            </div>
            <!-- 字体 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">字体</div>
              <div class="vis-form-field__content">
                <vis-select
                  v-model="computedOptions.font.fontFamily"
                  class="w-full"
                  :options="fontFamilyOptions"
                  :popHeight="240"
                >
                  <template #option="{ opt, itemProps }">
                    <q-item class="flex items-center" v-bind="itemProps">
                      <q-item-section>
                        <span :style="{ fontWeight: computedOptions.font.fontWeight, fontFamily: opt }">
                          {{ opt }}
                        </span>
                      </q-item-section>
                    </q-item>
                  </template>
                </vis-select>
              </div>
            </div>

            <!-- 粗细 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">粗细</div>
              <div class="vis-form-field__content">
                <vis-select v-model="computedOptions.font.fontWeight" :options="fontWeightOptions" class="w-full">
                  <template #option="{ opt, itemProps }">
                    <q-item class="flex items-center" v-bind="itemProps">
                      <q-item-section>
                        <span :style="{ fontWeight: opt.value, fontFamily: computedOptions.font.fontFamily }">
                          {{ opt.label }}
                        </span>
                      </q-item-section>
                    </q-item>
                  </template>
                </vis-select>
              </div>
            </div>

            <!-- 字号 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">字号</div>
              <div class="vis-form-field__content">
                <vis-select v-model="computedOptions.font.fontSize" :options="fontSizeOptions" editable />
              </div>
            </div>
          </div>
        </vis-popup>
      </q-btn>
      <q-separator vertical class="!m-0" />

      <!-- 内容 -->
      <div v-if="isIcon" class="flex-1">
        <q-input
          :model-value="computedOptions.icon.name || '请选择'"
          @click="showPopup"
          borderless
          class="rounded-borders flex-1 px-2 cursor-pointer"
          input-class="cursor-pointer"
          placeholder="请输入"
          readonly
        />
        <vis-icon-picker ref="iconPickerRef" v-model="computedOptions.icon" class="flex-1"></vis-icon-picker>
      </div>
      <template v-else>
        <q-input
          v-model="computedOptions.text"
          borderless
          class="rounded-borders flex-1 px-2 cursor-pointer"
          placeholder="请输入"
        />
      </template>
      <q-separator vertical class="!m-0" />

      <!-- 切换类型 -->
      <q-btn flat dense>
        <q-icon name="keyboard_arrow_down" class="vis-icon" />
        <q-menu v-model="showMenuType" style="width: 88px" class="vis-menu" dense>
          <q-list dense>
            <q-item :active="isIcon" @click="handleType('icon')" clickable v-close-popup>
              <q-item-section>图标</q-item-section>
            </q-item>
            <q-item :active="!isIcon" @click="handleType('text')" clickable v-close-popup>
              <q-item-section>文本</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>
  </div>
  <q-btn @click="handleVisible">
    <ht-icon class="vis-icon" :name="computedOptions.show ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
  </q-btn>
</template>
<script lang="ts" src="./fix.ts"></script>
