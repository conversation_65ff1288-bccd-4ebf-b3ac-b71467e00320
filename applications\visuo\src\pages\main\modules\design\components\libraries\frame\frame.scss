@import '../../index';
.#{$vis-prefix}-frame {
  .#{$vis-prefix}-frame-title {
    position: absolute;
    top: -20px;
    left: 0;
    color: #909399;
    font-size: 12px;
    white-space: nowrap;
    cursor: pointer;

    input {
      padding: 0;
      border: none;
      outline: none;
      background: transparent;
      color: #909399;
    }
  }

  &.active {
    > .#{$vis-prefix}-frame-title {
      z-index: 99999;
      color: $primary;
    }
  }

  &.chosen-frame {
    > .wrap {
      outline: 1px solid $canvas-theme-color;
    }
  }

  &.active,
  &.chosen-frame,
  &.active-frame {
    > .#{$vis-prefix}-frame-grid-ghost {
      @apply absolute w-full h-full pointer-events-none;

      z-index: $active-ghost-z-index;

      div {
        border: 1px solid rgba($canvas-theme-color, 0.4);

        &.active {
          background: rgba($canvas-theme-color, 0.2);
        }
      }
    }
  }

  &.grid > .wrap {
    > .#{$vis-prefix}-graph.dragging {
      opacity: 0.1 !important;
    }
  }

  &.horizontal > .wrap,
  &.vertical > .wrap {
    > .#{$vis-prefix}-graph.dragging {
      opacity: 0.1 !important;
    }
  }

  // 布局网格
  &-layout-guides {
    @apply absolute top-0 left-0 w-full h-full z--1 overflow-hidden;

    [class*='-layout-ghost'] {
      @apply absolute w-full h-full;

      border-radius: initial;
    }
  }
}

.#{$vis-prefix}-flex-item-ghost-dragging {
  @apply absolute;

  z-index: $active-ghost-z-index !important;
  border: 1px solid rgba($canvas-theme-color, 0.5);
  background: rgba($canvas-theme-color, 0.1);
}
