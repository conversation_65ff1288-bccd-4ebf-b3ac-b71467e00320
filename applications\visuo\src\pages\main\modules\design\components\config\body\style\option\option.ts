import { computed, defineComponent, ref } from 'vue';
import { useDocumentStore, Block, WidgetBlock, WidgetType } from '@vis/document-core';
import { useDesignStore } from '../../../../../stores';
import VisConfigTabOption from './tab/tab.vue';
import VisConfigParagraphOption from './paragraph/paragraph.vue';
import { VisConfigInputOption } from './form';
import VisConfigChartOption from './chart/chart.vue';
import { useWidget } from '../../../../../hooks';

/**
 * 组件配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-option',
  components: {
    VisConfigTabOption,
    VisConfigParagraphOption,
    VisConfigInputOption,
    VisConfigChartOption
  },
  setup() {
    const designStore = useDesignStore();
    const activeGraph = computed(() => designStore.active.value.graphs?.[0]);

    const docStore = useDocumentStore();

    const { getWidgetGroup } = useWidget();

    // 获取当前选中的组件
    const activeBlock = computed(
      () => docStore.document.value.blocks.find((b) => b.id === (activeGraph.value as Block)?.decoration) as WidgetBlock
    );

    // 获取组件所属组
    const widgetGroup = computed(() => getWidgetGroup(activeBlock.value?.type as WidgetType));

    return {
      activeBlock,
      widgetGroup
    };
  }
});
