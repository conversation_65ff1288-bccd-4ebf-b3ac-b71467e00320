<template>
  <div>
    <div class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__label">{{ title }}</div>
        <div class="vis-form-field__content">
          <div class="vis-form-inline__content--minus-0">
            <!-- <vis-fill v-model="computedStyle.background" :showEyes="false" :minusWidth="0" /> -->
            <vis-surface
              v-model:fill="computedStyle.background"
              v-model:stroke="computedStyle.border"
              v-model:effects="computedStyle.shadow"
              :minusWidth="0"
              :isShowEyes="false"
              :isShowTitle="false"
            ></vis-surface>
          </div>
          <!-- <q-btn flat dense @click="deleteStatus('hover')">
            <ht-icon class="vis-icon" name="vis-remove" />
          </q-btn> -->
        </div>
      </div>
    </div>

    <!-- 图标组件 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-0">
        <!-- 显示图标 -->
        <div class="vis-form-field__content">
          <vis-tab-icon-selector :minusWidth="0" :isStatus="true" :icon-option="computedStyle.icon" />
        </div>
        <!-- <vis-fill v-model="computedStyle.background" :showEyes="false" :minusWidth="0" /> -->
      </div>
      <!-- 字体按钮 -->
      <div class="btn-field">
        <vis-text-more :text="computedStyle.font" :textEffext="computedStyle.textEffects" />
      </div>
      <!-- 显隐按钮 -->
      <q-btn class="btn-field" @click="computedStyle.visible = !computedStyle.visible">
        <ht-icon class="vis-icon" :name="`hticon-vis-eye-${computedStyle.visible ? 'o' : 'c'}`" />
      </q-btn>
      <q-btn class="btn-field" @click="removeStatus">
        <ht-icon class="vis-icon" :name="`hticon-vis-remove`" />
      </q-btn>
    </div>
  </div>
</template>
<script lang="ts" src="./status.ts"></script>
