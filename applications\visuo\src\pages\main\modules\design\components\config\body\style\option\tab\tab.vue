<template>
  <div class="vis-config-tab-option">
    <!-- 选项 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">选项</div>
      <div class="vis-config-card__body">
        <!-- 模式 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">模式</div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="selectMode" :options="selectOptions" @change="onChangeSelectMode" />
              </div>
            </div>
            <div class="w-[120px]">
              <div class="vis-form-field__label row items-center">
                <div class="q-mr-xs">默认选中项</div>
                <q-icon name="help_ouline" size="12px" class="cursor-pointer !display-block">
                  <q-tooltip :offset="[10, 10]" max-width="120px" class="bg-white !text-gray-600"
                    >默认状态下的选中项。从1开始，0即不选中,多选模式下以逗号分隔默认选中的选项（例如: 1,2,3）</q-tooltip
                  >
                </q-icon>
              </div>
              <div class="vis-form-field__content">
                <q-input
                  v-model="tabOptions.defaultIndex"
                  class="vis-number rounded-borders flex-1 px-2 pr-0 vis-field--mini"
                  type="text"
                  borderless
                  dense
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 文本 -->
        <vis-text :option="tabOptions.style.font" mode="base" :mini="true" :textEffects="tabOptions.style.textEffects">
        </vis-text>

        <!-- 文本溢出 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">文本溢出</div>
              <div class="vis-form-field__content">
                <vis-select v-model="tabOptions.style.overflow" :options="overflowOptions" :popHeight="240">
                </vis-select>
              </div>
            </div>

            <div class="vis-form-field" :class="tabOptions.style.overflow === 3 ? 'visible' : 'invisible'">
              <div class="vis-form-field__label">滚动速度</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.style.scrollSpeed" :min="1" />
              </div>
            </div>
          </div>
        </div>

        <!-- 背景 -->
        <vis-surface
          v-model:fill="tabOptions.style.background"
          v-model:stroke="tabOptions.style.border"
          v-model:effects="tabOptions.style.shadow"
        ></vis-surface>

        <!-- 选中项自动定位 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-checkbox v-model="tabOptions.autoPosition" label="选中项自动定位" />
              </div>
            </div>
          </div>
        </div>
        <q-separator />
      </div>
    </div>

    <!-- 布局 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">布局</div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label"></div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="tabOptions.layout.direction" :options="directionOptions" />
              </div>
            </div>
          </div>
          <q-btn
            v-if="
              tabOptions.layout.direction === DirectionType.Horizontal && tabOptions.resizeX === TabResizeType.Fixed
            "
            class="btn-field"
            :class="{ active: tabOptions.layout.flowWrap }"
            @click="tabOptions.layout.flowWrap = !tabOptions.layout.flowWrap"
          >
            <ht-icon name="vis-layout-warp" />
            <q-tooltip> 换行 </q-tooltip>
          </q-btn>
        </div>

        <!-- 列宽 行高 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">宽度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="tabOptions.width"
                  :min="10"
                  :icon="`hticon-vis-${tabOptions.resizeX === TabResizeType.Adapt ? 'resize-adapt-x' : 'letter-w'}`"
                  class="pr-0"
                  :input-class="tabOptions.resizeX !== TabResizeType.Fixed ? 'hidden' : ''"
                >
                  <template v-slot:default v-if="tabOptions.resizeX !== TabResizeType.Fixed">
                    <span class="text-font-regular"> 适应 </span>
                  </template>
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu v-model="showMenuWidth" style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item
                            :active="tabOptions.resizeX === TabResizeType.Adapt"
                            @click="onChangeResize('resizeX', TabResizeType.Adapt)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-adapt-x" />
                            </q-item-section>
                            <q-item-section>适应容器</q-item-section>
                          </q-item>
                          <q-item
                            :active="tabOptions.resizeX === TabResizeType.Fixed"
                            @click="onChangeResize('resizeX', TabResizeType.Fixed)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-fixed-x" />
                            </q-item-section>
                            <q-item-section>固定尺寸</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">高度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="tabOptions.height"
                  :min="10"
                  :icon="`hticon-vis-${tabOptions.resizeY === TabResizeType.Adapt ? 'resize-adapt-y' : 'letter-h'}`"
                  class="pr-0"
                  :input-class="tabOptions.resizeY !== TabResizeType.Fixed ? 'hidden' : ''"
                >
                  <template v-slot:default v-if="tabOptions.resizeY !== TabResizeType.Fixed">
                    <span class="text-font-regular"> 适应 </span>
                  </template>
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu v-model="showMenuHeight" style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item
                            :active="tabOptions.resizeY === TabResizeType.Adapt"
                            @click="onChangeResize('resizeY', TabResizeType.Adapt)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-adapt-y" />
                            </q-item-section>
                            <q-item-section>适应容器</q-item-section>
                          </q-item>
                          <q-item
                            :active="tabOptions.resizeY === TabResizeType.Fixed"
                            @click="onChangeResize('resizeY', TabResizeType.Fixed)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-fixed-y" />
                            </q-item-section>
                            <q-item-section>固定尺寸</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>
          </div>
        </div>
        <!-- 网格布局 -->
        <div class="vis-form-inline" v-if="tabOptions.layout.direction === DirectionType.Grid">
          <div class="vis-form-inline__content--minus-32 !items-start">
            <div class="vis-form-field">
              <div class="vis-form-field__label">网格</div>
              <div class="vis-form-field__content">
                <div
                  v-if="tabOptions.layout.direction === DirectionType.Grid"
                  class="vis-layout-grid flex flex-center text-xs"
                  @click="onShowPopup"
                >
                  {{ tabOptions.layout.row }} × {{ tabOptions.layout.column }}
                </div>
                <vis-popup title="网格" ref="popupRef" :target="false" @hide="popupShow = false" width="260px">
                  <div class="vis-form-inline !flex-nowrap">
                    <div class="vis-form-inline__content--minus-32 !items-center">
                      <div class="vis-form-field !flex-1">
                        <div class="vis-form-field__content">
                          <vis-number
                            v-model="tabOptions.layout.row"
                            precision="0"
                            :min="1"
                            icon="hticon-vis-grid-row"
                          />
                        </div>
                      </div>
                      ×
                      <div class="vis-form-field !flex-1">
                        <div class="vis-form-field__content">
                          <vis-number
                            v-model="tabOptions.layout.column"
                            precision="0"
                            :min="1"
                            icon="hticon-vis-grid-col"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="vis-menu-grid_picker row gap-1 pt-3"
                    @mousemove="onMouseMovePicker"
                    @mouseleave="onMouseLeavePicker"
                  >
                    <div class="row gap-1" v-for="row in 12" :key="row">
                      <div
                        :class="{
                          hand: true,
                          hover: row <= hoverGrid[0] && col <= hoverGrid[1],
                          active: row <= tabOptions.layout.row && col <= tabOptions.layout.column
                        }"
                        :data-row="row"
                        :data-col="col"
                        v-for="col in 12"
                        :key="col"
                        @click="onClickGrid(row, col)"
                      >
                        <q-tooltip> {{ row }} × {{ col }} </q-tooltip>
                      </div>
                    </div>
                  </div>
                </vis-popup>
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">间距</div>
              <div class="vis-form-field__content column">
                <template v-if="tabOptions.layout.direction === DirectionType.Grid">
                  <vis-number
                    v-model="tabOptions.layout.gutter[1]"
                    :min="0"
                    precision="0"
                    icon="hticon-vis-gap-horizontal"
                  />
                  <vis-number
                    v-model="tabOptions.layout.gutter[0]"
                    :min="0"
                    precision="0"
                    icon="hticon-vis-gap-vertical"
                  />
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- 间距 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field" v-if="tabOptions.layout.direction === DirectionType.Horizontal">
              <div class="vis-form-field__label">列距</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.layout.gutter[1]" :min="0" precision="0" icon="hticon-vis-padding-x" />
              </div>
            </div>

            <div class="vis-form-field" v-if="getShowLineGutter">
              <div class="vis-form-field__label">行距</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.layout.gutter[0]" :min="0" precision="0" icon="hticon-vis-padding-y" />
              </div>
            </div>
            <div class="vis-form-field" v-if="isAllRadius">
              <div class="vis-form-field__label">圆角</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="allRadius"
                  icon="hticon-vis-radius"
                  @update:model-value="(val:number) => radiusChange(val, 'all')"
                  :min="0"
                />
              </div>
            </div>
          </div>

          <q-btn
            v-if="isAllRadius"
            flat
            class="btn-field"
            :class="{ active: showRadius }"
            @click="showRadius = !showRadius"
          >
            <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
            <q-tooltip> 单独设置 </q-tooltip>
          </q-btn>
        </div>
        <template v-if="showRadius && isAllRadius">
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label"></div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[0]" precision="0" icon="hticon-vis-radius-lt" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[1]" precision="0" icon="hticon-vis-radius-rt" :min="0" />
                </div>
              </div>
            </div>
          </div>
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[3]" precision="0" icon="hticon-vis-radius-lb" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[2]" precision="0" icon="hticon-vis-radius-rb" :min="0" />
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 圆角 -->
        <div class="vis-form-inline" v-if="!isAllRadius && !showRadius">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">圆角</div>
              <div class="vis-form-field__content">
                <!-- <vis-number
                  v-model="tlbrRadius"
                  icon="hticon-vis-radius-tb"
                  @update:model-value="(val:number) => radiusChange(val, 'tl')"
                  precision="0"
                  :min="0"
                /> -->
                <vis-mix-input
                  v-model="leftRadius"
                  icon="hticon-vis-radius-tb"
                  @update:model-value="(val:number) => radiusChange(val, 'left')"
                  :min="0"
                />
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <!-- <vis-number
                  v-model="bottomRadius"
                  icon="hticon-vis-radius-lr"
                  @update:model-value="(val:number) => radiusChange(val, 'bottom')"
                  precision="0"
                  :min="0"
                /> -->
                <vis-mix-input
                  v-model="rightRadius"
                  icon="hticon-vis-radius-tb"
                  @update:model-value="(val:number) => radiusChange(val, 'right')"
                  :min="0"
                />
              </div>
            </div>
          </div>
          <q-btn flat class="btn-field" :class="{ active: showRadius }" @click="showRadius = !showRadius">
            <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
            <q-tooltip> 单独设置 </q-tooltip>
          </q-btn>
        </div>

        <template v-if="showRadius && !isAllRadius">
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label">圆角</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[0]" precision="0" icon="hticon-vis-radius-lt" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[1]" precision="0" icon="hticon-vis-radius-rt" :min="0" />
                </div>
              </div>
            </div>
            <q-btn flat class="btn-field" :class="{ active: showRadius }" @click="showRadius = !showRadius">
              <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
              <q-tooltip> 单独设置 </q-tooltip>
            </q-btn>
          </div>
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[3]" precision="0" icon="hticon-vis-radius-lb" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[2]" precision="0" icon="hticon-vis-radius-rb" :min="0" />
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 滚动条 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-checkbox v-model="tabOptions.layout.scrollbar" label="滚动条" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <q-separator />
    </div>

    <!-- 文本 -->
    <!-- <div class="vis-config-card">
      <div class="vis-config-card__header">文本</div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">文本方向</div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="tabOptions.style.textDirection" :options="textDirectionOptions" />
              </div>
            </div>
          </div>
        </div>

        <vis-text
          :option="tabOptions.style.font"
          mode="base"
          :extra="['alignHorizontal', 'alignVertical', 'letterSpacing', 'fontStyle', 'lineHeight']"
          fillLabel="文本颜色"
          :mini="true"
          textTitle="文本"
        >
        </vis-text>
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-0">
            <div class="vis-form-field">
              <div class="vis-form-field__label">文本阴影</div>
              <div class="vis-form-field__content">
                <vis-effects v-model="tabOptions.style.font.textEffects" :isText="true" :minusWidth="28" />
              </div>
            </div>
          </div>
        </div>

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">文本溢出</div>
              <div class="vis-form-field__content">
                <vis-select v-model="tabOptions.style.overflow" :options="overflowOptions" :popHeight="240">
                </vis-select>
              </div>
            </div>

            <div class="vis-form-field" :class="tabOptions.style.overflow === 3 ? 'visible' : 'invisible'">
              <div class="vis-form-field__label">滚动速度</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.style.scrollSpeed" :min="1" />
              </div>
            </div>
          </div>
        </div>
        <q-separator />
      </div>
    </div> -->

    <!-- 图标 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>图标</span>
        <q-btn flat dense @click="toggle('icon')">
          <ht-icon class="vis-icon" :name="`vis-${tabOptions.icon ? 'remove' : 'add'}`" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div v-if="tabOptions.icon" class="position-relative">
          <vis-tab-icon-selector :icon-option="tabOptions.icon" />

          <div class="vis-form-inline" v-if="tabOptions.icon.type === 'picture'">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label">宽度</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.icon.width" precision="0" :min="0" icon="hticon-vis-letter-w" />
                </div>
              </div>

              <div class="vis-form-field">
                <div class="vis-form-field__label">高度</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.icon.height" precision="0" :min="0" icon="hticon-vis-letter-h" />
                </div>
              </div>
            </div>
            <q-btn class="btn-field" :class="{ active: tabOptions.icon.aspectRatio }" @click="onAspectRatio">
              <ht-icon class="vis-icon" name="vis-associate" />
              <q-tooltip> 锁定纵横比 </q-tooltip>
            </q-btn>
          </div>

          <div class="vis-form-inline" v-else>
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label">颜色</div>
                <div class="vis-form-field__content">
                  <!-- <vis-number v-model="tabOptions.icon.width" precision="0" :min="0" icon="hticon-vis-letter-w" /> -->
                  <vis-fill v-model="tabOptions.icon.icon.color" :minusWidth="0" tooltip="颜色" :onlyColor="true" />
                </div>
              </div>

              <div class="w-[100px]">
                <div class="vis-form-field__label">大小</div>
                <div class="vis-form-field__content">
                  <vis-select v-model="tabOptions.icon.icon.size" :options="fontSizes" editable />
                </div>
              </div>
            </div>
          </div>

          <!-- 位置 -->
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label">位置</div>
                <div class="vis-form-field__content">
                  <vis-button-group v-model="tabOptions.icon.position" :options="iconPositionOptions" />
                </div>
              </div>

              <div class="w-[100px]">
                <div class="vis-form-field__label">与文字间距</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.icon.gutter" precision="0" :min="0" icon="hticon-vis-max-width" />
                </div>
              </div>
            </div>
            <!-- 显隐按钮 -->
            <q-btn class="btn-field" @click="tabOptions.icon.visible = !tabOptions.icon.visible">
              <ht-icon class="vis-icon" :name="`hticon-vis-eye-${tabOptions.icon.visible ? 'o' : 'c'}`" />
            </q-btn>
          </div>

          <!-- 与文字间距 -->
          <!-- <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label">与文字间距</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.icon.gutter" precision="0" :min="0" icon="hticon-vis-max-width" />
                </div>
              </div>
            </div>
          </div> -->
        </div>
        <q-separator />
      </div>
    </div>

    <!-- <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>效果</span>
      </div>
      <div class="vis-config-card__body">
        <div class="position-relative" v-if="tabOptions.style.background">
          <div class="vis-form-inline">
            <div class="vis-form-field">
              <div class="vis-form-field__label">背景</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="tabOptions.style.background" />
              </div>
            </div>
          </div>

          <vis-border
            v-if="tabOptions.style.border"
            v-model="tabOptions.style.border"
            :showMore="false"
            :minusWidth="0"
          >
          </vis-border>

          <div class="vis-form-inline">
            <div class="vis-form-field">
              <div class="vis-form-field__label">阴影</div>
              <div class="vis-form-field__content">
                <vis-effects v-model="tabOptions.style.shadow" />
              </div>
            </div>
          </div>
        </div>
        <q-separator />
      </div>
    </div> -->

    <!-- 状态 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>状态</span>
        <q-btn flat dense>
          <ht-icon class="vis-icon" name="vis-add" v-if="!(tabStyle.hover && tabStyle.active)" />
          <q-menu class="vis-menu">
            <q-list dense style="width: 80px; font-size: 10px">
              <q-item clickable v-close-popup @click="addStatus('hover')" :disable="!!tabStyle.hover">
                <q-item-section class="text-center">悬浮</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="addStatus('active')" :disable="!!tabStyle.active">
                <q-item-section class="text-center">选中</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <template v-if="tabStyle.hover">
          <!-- <div class="vis-form-inline">
            <div class="vis-form-field">
              <div class="vis-form-field__label">悬浮</div>
              <div class="vis-form-field__content position-relative"> -->
          <vis-config-tab-status v-model="tabStyle.hover" title="悬浮" @remove="deleteStatus('hover')" />
          <!-- <q-btn flat dense @click="deleteStatus('hover')">
                  <ht-icon class="vis-icon" name="vis-remove" />
                </q-btn>
              </div>
            </div>
          </div> -->
        </template>

        <template v-if="tabStyle.active">
          <!-- <div class="vis-form-inline">
            <div class="vis-form-field">
              <div class="vis-form-field__label">选中</div>
              <div class="vis-form-field__content position-relative"> -->
          <vis-config-tab-status v-model="tabStyle.active" title="选中" @remove="deleteStatus('active')" />
          <!-- <q-btn flat dense @click="deleteStatus('active')" class="delete-btn">
                  <ht-icon class="vis-icon" name="vis-remove" />
                </q-btn>
              </div>
            </div>
          </div> -->
        </template>
        <q-separator />
      </div>
    </div>

    <!-- 自动轮播 -->
    <div class="vis-config-card" v-if="!tabOptions.multiple">
      <div class="vis-config-card__header">
        <span>轮播</span>
        <q-btn flat dense @click="toggle('carousel')">
          <ht-icon class="vis-icon" :name="`vis-${tabOptions.carousel ? 'remove' : 'add'}`" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline" v-if="tabOptions.carousel">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">间隔时长</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.carousel.interval">
                  <template #append>
                    <span>s</span>
                  </template>
                </vis-number>
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">点击停留</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.carousel.clickTime">
                  <template #append>
                    <span>s</span>
                  </template>
                </vis-number>
              </div>
            </div>
          </div>
        </div>
        <q-separator />
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./tab.ts"></script>
