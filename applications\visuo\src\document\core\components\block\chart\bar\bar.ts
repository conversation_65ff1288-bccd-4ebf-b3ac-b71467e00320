import type { Block, BarOptions } from '../../../../models';
import { defineComponent, type PropType } from 'vue';

/**
 * 柱状图
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-bar',
  props: {
    widget: {
      type: Object as PropType<BarOptions>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {
    return {};
  }
});
