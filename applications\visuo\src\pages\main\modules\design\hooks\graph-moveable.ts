import type { OnResize, OnResizeEnd } from 'vue3-moveable';
import { useDesignStore } from '../stores';
import {
  Group,
  GraphType,
  useDocumentStore,
  Graph,
  GridItem,
  CrossAlignment,
  Frame,
  useLayout,
  ResizeType,
  GRAPH_SIZE_MIN
} from '@vis/document-core';
import { useGraph } from './graph';

/**
 * 图形moveable操作
 * <AUTHOR>
 */
export const useGraphMoveable = () => {
  const designStore = useDesignStore();
  const documentStore = useDocumentStore();

  const { isFlex, isFreeform, isGrid } = useLayout();
  const { gridItemRowBySize, gridItemColBySize, resetGraphXYByDom } = useGraph();

  /**
   * 栅格布局时moveable组件四角方向控制器
   * @param graph
   */
  const getGraphGridDirections = (graph: Graph) => {
    const directions = [];
    const { justifySelf, alignSelf } = graph.gridItem as GridItem;
    switch (justifySelf) {
      case CrossAlignment.Start:
        switch (alignSelf) {
          case CrossAlignment.Start:
            directions.push('se');
            break;
          case CrossAlignment.Center:
            directions.push('se', 'ne');
            break;
          case CrossAlignment.End:
            directions.push('ne');
            break;
        }
        break;
      case CrossAlignment.Center:
        switch (alignSelf) {
          case CrossAlignment.Start:
            directions.push('se', 'sw');
            break;
          case CrossAlignment.Center:
            directions.push('nw', 'ne', 'se', 'sw');
            break;
          case CrossAlignment.End:
            directions.push('nw', 'ne');
            break;
        }
        break;
      case CrossAlignment.End:
        switch (alignSelf) {
          case CrossAlignment.Start:
            directions.push('sw');
            break;
          case CrossAlignment.Center:
            directions.push('nw', 'sw');
            break;
          case CrossAlignment.End:
            directions.push('nw');
            break;
        }
        break;
    }
    return directions;
  };

  /**
   * 栅格布局时moveable组件四边方向控制器
   * @param graph
   */
  const getGraphGridEdges = (graph: Graph) => {
    const directions = [];
    const { justifySelf, alignSelf } = graph.gridItem as GridItem;
    switch (justifySelf) {
      case CrossAlignment.Start:
        switch (alignSelf) {
          case CrossAlignment.Start:
            directions.push('e', 's');
            break;
          case CrossAlignment.Center:
            directions.push('e', 's', 'n');
            break;
          case CrossAlignment.End:
            directions.push('n', 'e');
            break;
        }
        break;
      case CrossAlignment.Center:
        switch (alignSelf) {
          case CrossAlignment.Start:
            directions.push('s', 'e', 'w');
            break;
          case CrossAlignment.Center:
            directions.push('n', 's', 'e', 'w');
            break;
          case CrossAlignment.End:
            directions.push('n', 'e', 'w');
            break;
        }
        break;
      case CrossAlignment.End:
        switch (alignSelf) {
          case CrossAlignment.Start:
            directions.push('w', 's');
            break;
          case CrossAlignment.Center:
            directions.push('n', 's', 'w');
            break;
          case CrossAlignment.End:
            directions.push('n', 'w');
            break;
        }
        break;
    }
    return directions;
  };

  /**
   * 拖拽过程中事件：parent是grid或flex布局时，处理graph的宽高及transform
   * @param e
   * @param graph
   * @param parent
   */
  const onResizeFlexGrid = (e: OnResize, graph: Graph, parent: Frame) => {
    let width = Math.round(e.width);
    let height = Math.round(e.height);
    const x = Math.round(e.drag.translate[0]);
    const y = Math.round(e.drag.translate[1]);

    // 限制大小
    if (e.width < GRAPH_SIZE_MIN) {
      width = GRAPH_SIZE_MIN;
    }
    if (e.height < GRAPH_SIZE_MIN) {
      height = GRAPH_SIZE_MIN;
    }

    if (isFlex(parent)) {
      // 拖拽的是上下的手柄 调整的是高度
      if (e.direction[0] === 0) {
        e.target.style.height = `${height}px`;
        graph.height = height;
        graph.limitSize.height.resize = ResizeType.Fixed;
      }
      // 拖拽的是左右的手柄 调整的是宽度
      else if (e.direction[1] === 0) {
        e.target.style.width = `${width}px`;
        graph.width = width;
        graph.limitSize.width.resize = ResizeType.Fixed;
      }
      // 拖拽的是四角的手柄调整的是宽高
      else {
        graph.limitSize.width.resize = ResizeType.Fixed;
        graph.limitSize.height.resize = ResizeType.Fixed;

        e.target.style.width = `${width}px`;
        e.target.style.height = `${height}px`;

        graph.width = width;
        graph.height = height;
      }
    } else if (isGrid(parent)) {
      if (graph.parent) {
        // 上：[0, -1] 下：[0, 1] 左：[-1, 0] 右：[1, 0]
        // 左上：[-1, -1] 右上：[1, -1] 左下：[-1, 1] 右下：[1, 1]
        // 右：宽度
        if (e.direction[0] === 1 && e.direction[1] === 0) {
          graph.width = width;
          e.target.style.width = `${width}px`;
          graph.limitSize.width.resize = ResizeType.Fixed;
        }

        // 下：高度
        if (e.direction[0] === 0 && e.direction[1] === 1) {
          graph.height = height;
          e.target.style.height = `${height}px`;
          graph.limitSize.height.resize = ResizeType.Fixed;
        }

        //左：宽度、transform
        if (e.direction[0] === -1 && e.direction[1] === 0) {
          graph.width = width;
          e.target.style.width = `${width}px`;
          graph.transform.translate = [x, y];
          e.target.style.transform = e.drag.transform;
          graph.limitSize.width.resize = ResizeType.Fixed;
        }

        // 上：高度、transform
        if (e.direction[0] === 0 && e.direction[1] === -1) {
          graph.height = height;
          e.target.style.height = `${height}px`;
          graph.transform.translate = [x, y];
          e.target.style.transform = e.drag.transform;
          graph.limitSize.height.resize = ResizeType.Fixed;
        }

        // 右下：宽度、高度
        if (e.direction[0] === 1 && e.direction[1] === 1) {
          graph.width = width;
          graph.height = height;
          e.target.style.width = `${width}px`;
          e.target.style.height = `${height}px`;
          graph.limitSize.width.resize = ResizeType.Fixed;
          graph.limitSize.height.resize = ResizeType.Fixed;
        }

        //宽、高、transform
        if (
          // 右上
          (e.direction[0] === 1 && e.direction[1] === -1) ||
          // 左上
          (e.direction[0] === -1 && e.direction[1] === -1) ||
          // 左下
          (e.direction[0] === -1 && e.direction[1] === 1)
        ) {
          graph.width = width;
          graph.height = height;
          e.target.style.width = `${width}px`;
          e.target.style.height = `${height}px`;
          graph.transform.translate = [x, y];
          e.target.style.transform = e.drag.transform;
          graph.limitSize.width.resize = ResizeType.Fixed;
          graph.limitSize.height.resize = ResizeType.Fixed;
        }
      }
    }
  };

  /**
   * 拖拽结束事件： parent是grid布局时，计算graph在父元素格子中占据的位置
   * @param e
   * @param graph
   * @param parent
   */
  const onResizeEndGrid = (e: OnResizeEnd, graph: Graph, parent: Frame) => {
    if (isGrid(parent) && graph.gridItem) {
      // 上：[0, -1] 下：[0, 1] 左：[-1, 0] 右：[1, 0]
      // 左上：[-1, -1] 右上：[1, -1] 左下：[-1, 1] 右下：[1, 1]
      const direction = e.lastEvent.direction;

      if (
        // 右
        (direction[0] === 1 && direction[1] === 0) ||
        // 下
        (direction[0] === 0 && direction[1] === 1) ||
        // 右下
        (direction[0] === 1 && direction[1] === 1)
      ) {
        // 改变的是grid-rows、grid-columns的end值
        gridItemRowBySize(graph, parent, 1);
        gridItemColBySize(graph, parent, 1);
      }

      if (
        // 左
        (direction[0] === -1 && direction[1] === 0) ||
        // 上
        (direction[0] === 0 && direction[1] === -1) ||
        // 左上
        (direction[0] === -1 && direction[1] === -1)
      ) {
        // 改变的是grid-rows、grid-columns的start值
        gridItemRowBySize(graph, parent, -1);
        gridItemColBySize(graph, parent, -1);
      }
      // 右上
      if (direction[0] === 1 && direction[1] === -1) {
        // 改变的是grid-rows的start, grid-columns的end值
        gridItemRowBySize(graph, parent, -1);
        gridItemColBySize(graph, parent, 1);
      }

      // 左下
      if (direction[0] === -1 && direction[1] === 1) {
        // 改变的是grid-rows的end, grid-columns的start值
        gridItemRowBySize(graph, parent, 1);
        gridItemColBySize(graph, parent, -1);
      }

      if (graph.gridItem.justifySelf === CrossAlignment.Center || graph.gridItem.alignSelf === CrossAlignment.Center) {
        resetGraphXYByDom([graph]);
      }
    }
  };

  return {
    onResizeFlexGrid,
    onResizeEndGrid,

    getGraphGridDirections,
    getGraphGridEdges
  };
};
