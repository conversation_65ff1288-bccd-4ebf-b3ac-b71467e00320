import type { AxisFields, FilterAxisField } from './data/axis-field';

/**
 * 组件块
 * <AUTHOR>
 */
export class WidgetBlock {
  /** 唯一标识 */
  id = '';

  /** 组件名称 */
  name = '';

  /** 组件类型 */
  type = '';

  /** 数据集id */
  datasetId = '';

  /** 数据集类型 */
  datasetType = '';

  /** x轴字段 */
  xField: AxisFields = [];

  /** y轴字段 */
  yField: AxisFields = [];

  /** 数据映射 */
  dataMapping: AxisFields = [];

  /** 列字段 用于表格，透视表格，透视图表*/
  columns: AxisFields = [];

  /** 行字段 用于表格*/
  rows: AxisFields = [];

  /** 下钻层级字段 */
  levels: AxisFields = [];

  /** 过滤字段 */
  filters: Array<FilterAxisField> = [];

  /** 数据刷新方式 auto:自动刷新 control:受控模式 none:不刷新 */
  refreshType: 'auto' | 'control' | 'none' = 'none';

  /** 刷新频率 */
  refreshSecond = 1;

  /** 开始行数 */
  startRow = 1;

  /** 行数限制 */
  rowsLimit = 100;

  /** 组件属性 */
  options: any = {};
}
