import { computed, defineComponent, ref } from 'vue';
import { useDesignStore } from '../../../../../stores';
import {
  Block,
  Graph,
  GraphStyle,
  QuickStyle,
  useDocumentStore,
  useWidgetConfigStore,
  WidgetBlock,
  WidgetType
} from '@vis/document-core';

/**
 * 快速切换样式组件
 */
export default defineComponent({
  name: 'vis-config-quick-style',
  setup() {
    const designStore = useDesignStore();
    const activeGraph = computed(() => designStore.active.value.graph as Graph);

    const docStore = useDocumentStore();

    const { quickStyleConfigs } = useWidgetConfigStore();

    const selectedStyleId = ref('');

    const popupRef = ref();

    // 获取当前选中的组件
    const activeBlock = computed(
      () => docStore.document.value.blocks.find((b) => b.id === (activeGraph.value as Block)?.decoration) as WidgetBlock
    );
    const styleOptions = computed(() => {
      const widgetType = activeBlock.value.type as WidgetType;
      return activeBlock.value ? quickStyleConfigs[widgetType] : [];
    });

    /**
     * 选择类型
     * @param item
     */
    const selectItemStyle = (item: QuickStyle) => {
      selectedStyleId.value = item.id;
      // 覆盖当前选中组件的样式
      activeBlock.value.options = JSON.parse(JSON.stringify(item.widgetBlockOptions));
      if (item.graphStyle) {
        // 清空未定义的项
        for (const key in item.graphStyle) {
          // 确保是两者共有的key
          const commonKey = key as keyof Graph & keyof GraphStyle;
          if (commonKey in activeGraph.value && item.graphStyle[commonKey] !== undefined) {
            activeGraph.value[commonKey] = item.graphStyle[commonKey] as never;
          }
        }
      }
    };

    const showPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    return {
      selectedStyleId,
      activeBlock,
      styleOptions,
      popupRef,
      selectItemStyle,
      showPopup
    };
  }
});
