import { defineComponent, nextTick, ref } from 'vue';

/**
 * 弹窗
 * <AUTHOR>
 * 弹窗显示需通过外部调用handleShow方法实现
 */
export default defineComponent({
  name: 'vis-popup',
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '240px'
    },
    height: {
      type: String,
      default: 'auto'
    }
  },
  setup(props, { emit }) {
    const popShow = ref(false);
    // 弹窗刚开始隐藏，计算好位置后显示
    const visible = ref(false);

    const className = 'pop' + `${Date.now()}`.slice(7);

    const handleHide = () => {
      popShow.value = false;
      emit('hide');
      emit('update:modelValue', false);
    };

    /**
     * 弹窗位置计算
     * 显示时绑定鼠标拖拽事件，弹窗允许拖动
     */
    const handleShow = (e: Event) => {
      popShow.value = true;
      // 右侧偏移
      const popOffset = (document.querySelector('.vis-design-config') as HTMLElement)?.offsetWidth || 0;

      nextTick(() => {
        const popup = document.querySelector(`.vis-popup.${className}`) as HTMLElement;
        // 计算弹窗显示位置
        const winWidth = window.innerWidth;
        const winHeight = window.innerHeight;

        const parentPop = (e.target as HTMLElement)?.closest('.vis-popup') as HTMLElement; // 是否为子弹窗
        const parentLeft = parentPop ? parseInt(parentPop.style.left) : winWidth - popOffset;
        let popLeft = parentLeft - popup.offsetWidth - 4;
        popLeft = Math.min(Math.max(10, popLeft), winWidth - popup.offsetWidth - 10); // 位置限制到可视区域

        const inlineEle = (e.target as HTMLElement)?.closest('.vis-form-inline') as HTMLElement; // 是否为子弹窗
        const inlineTop = inlineEle ? inlineEle.getBoundingClientRect().top : 0;

        let popTop = inlineTop || (e as MouseEvent).clientY;
        popTop = Math.min(Math.max(24, popTop), winHeight - popup.offsetHeight - 10); // 位置限制到可视区域

        popup.style.visibility = 'visible';
        popup.style.left = `${popLeft}px`;
        popup.style.top = `${popTop}px`;
        visible.value = true;

        // 标题位置用于拖拽
        const title = popup.querySelector('.vis-popup__title') as HTMLElement;
        let isDragging = false;
        let posX: number, posY: number;

        // 鼠标按下时开始拖动
        title.addEventListener('mousedown', (e) => {
          // 检查是否点击了标题栏（这里整个div都可拖动）
          isDragging = true;
          posX = e.clientX - popup.offsetLeft;
          posY = e.clientY - popup.offsetTop;

          // 防止文本选中
          e.preventDefault();
        });

        // 鼠标移动时更新位置
        document.addEventListener('mousemove', (e) => {
          if (!isDragging) return;

          popup.style.left = (e as MouseEvent).clientX - posX + 'px';
          popup.style.top = (e as MouseEvent).clientY - posY + 'px';
        });

        // 鼠标释放时停止拖动
        document.addEventListener('mouseup', () => {
          isDragging = false;
        });
      });

      emit('show');
      emit('update:modelValue', true);
    };

    return {
      popShow,
      visible,
      className,
      handleHide,
      handleShow
    };
  }
});
