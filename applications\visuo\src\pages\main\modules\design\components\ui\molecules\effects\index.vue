<template>
  <div class="vis-effects w-full">
    <div class="vis-form-inline">
      <div :class="`vis-form-inline__content--minus-${minusWidth}`">
        <q-btn :class="{ active: popupShow }" @click.stop="showPopup">
          <img
            class="img-icon"
            :src="`./static-next/svg/effects/${popupShow ? 'active-' + effectIcon : effectIcon}.svg`"
          />
        </q-btn>
        <vis-popup title="特效" ref="popupRef" :target="false" @hide="popupShow = false">
          <div class="vis-form-inline">
            <template v-if="isOffset">
              <div class="vis-form-field">
                <div class="vis-form-field__label">位置</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="computedEffects.offset.x" icon="hticon-vis-letter-x"></vis-number>
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__label"></div>
                <div class="vis-form-field__content">
                  <vis-number v-model="computedEffects.offset.y" icon="hticon-vis-letter-y"></vis-number>
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__label">效果</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="computedEffects.blur" icon="hticon-vis-blur" :min="0"></vis-number>
                </div>
              </div>
              <div class="vis-form-field" v-if="!isText">
                <div class="vis-form-field__label">扩展</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="computedEffects.spread" icon="hticon-vis-spread" :min="0"></vis-number>
                </div>
              </div>

              <div class="vis-form-field">
                <div class="vis-form-field__label">颜色</div>
                <div class="vis-form-field__content">
                  <vis-fill :minusWidth="0" v-model="computedEffects.color" :onlyColor="true" :hideTitle="true" />
                </div>
              </div>
            </template>
            <template v-if="isBlur">
              <div class="vis-form-inline">
                <div class="vis-form-field">
                  <div class="vis-form-field__label">模糊</div>
                  <div class="vis-form-field__content">
                    <vis-number v-model="computedEffects.blur" icon="hticon-vis-blur" :min="0"></vis-number>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </vis-popup>
        <vis-select
          class="flex-1"
          v-if="computedEffects.type && !isText"
          v-model="computedEffects.type"
          :options="effectOptions"
          :disable="!visible || isText"
        />

        <vis-fill
          v-else-if="isText"
          :minusWidth="0"
          v-model="computedEffects.color"
          :onlyColor="true"
          :hideTitle="true"
        />
      </div>
      <!-- 显隐按钮 -->
      <q-btn class="btn-field" @click="handleVisible">
        <ht-icon class="vis-icon" :name="visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
      </q-btn>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
