@import '../../../../../index.scss';

.#{$vis-prefix}-layout {
  .aspect-ratio-locked-left {
    box-shadow: 6px 0 0 0 transparent;
    animation: fadeInLeftShadow 0.3s ease-out forwards;
    border-radius: 4px 0 0 4px;
    background-color: $input-bg;
  }

  .aspect-ratio-locked-right {
    box-shadow: -6px 0 0 0 transparent;
    animation: fadeInRightShadow 0.3s ease-out 0.1s forwards;
    border-radius: 0 4px 4px 0;
    background-color: $input-bg;
  }

  @keyframes fadeInLeftShadow {
    0% {
      box-shadow: 6px 0 0 0 transparent;
    }

    100% {
      box-shadow: 6px 0 0 0 $input-bg;
    }
  }

  @keyframes fadeInRightShadow {
    0% {
      box-shadow: -6px 0 0 0 transparent;
    }

    100% {
      box-shadow: -6px 0 0 0 $input-bg;
    }
  }

  &-align {
    border: 1px solid $input-hover-color;
    background: $input-bg;

    @apply w-full h-56px relative rounded grid grid-cols-3 grid-rows-3;

    .hand {
      @apply flex items-center justify-center cursor-pointer;

      .dot {
        @apply w-2px h-2px rounded-full;
        background: rgba(black, 0.4);
      }
      i {
        @apply hidden;
      }

      &.active,
      &:hover {
        .dot {
          @apply hidden;
        }
        i {
          @apply block;
        }
      }
      &.active {
        i {
          color: $primary;
        }
      }
    }
  }

  &-between-align {
    border: 1px solid $input-hover-color;
    background: $input-bg;

    @apply w-full h-56px relative rounded grid grid-cols-1 grid-rows-3 py-0.5;

    .hand {
      @apply cursor-pointer;
      .bar {
        @apply hidden  justify-around h-full;

        div {
          @apply h-12px w-2px;
          background: $grey-5;

          &:nth-last-of-type(2) {
            @apply h-6px;
          }
        }
      }
      .dot {
        @apply flex items-center justify-around h-full;

        div {
          @apply w-2px h-2px rounded-full;
          background: black;
        }
      }

      &.active,
      &:hover {
        .bar {
          @apply flex;
        }
        .dot {
          @apply hidden;
        }
      }
      &.active {
        .bar {
          div {
            background: $primary;
          }
        }
      }
    }

    &.vertical {
      @apply grid-cols-3 grid-rows-1 px-2;

      .hand {
        .bar {
          @apply flex-col;

          div {
            @apply h-2px w-12px;

            &:nth-last-of-type(2) {
              @apply w-6px;
            }
          }
        }
        .dot {
          @apply flex-col;
        }
      }
    }
  }

  &-grid {
    border: 1px solid $input-hover-color;
    background: $input-bg;

    @apply w-full h-56px relative rounded;
  }
}
.#{$vis-prefix}-menu-grid {
  @apply px-2 py-3;

  &_picker {
    .hand {
      @apply w-16px h-16px rounded border border-solid border-gray-300 cursor-pointer;

      &.hover {
        border-color: rgba($primary, 0.5);
        background: rgb(217, 236, 255, 0.5);
      }

      &.active {
        border-color: rgba($primary, 0.5);
        background: rgb(64 158 255 / 54%);
      }
    }
  }
}
