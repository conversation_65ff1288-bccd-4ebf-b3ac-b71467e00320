import {
  DirectionType,
  Frame,
  HoriConsType,
  ResizeType,
  useLayout,
  VertConsType,
  type Graph
} from '@vis/document-core';
import { useDesignStore } from '../stores';
import { computed } from 'vue';
import type { Records } from '@hetu/util';

/**
 * 约束
 * <AUTHOR>
 */
export const useConstraints = () => {
  const designStore = useDesignStore();
  const active = computed(() => designStore.active.value);
  const activeFrame = computed(() => active.value.frame);

  const { isLayoutRowColumn, getLayoutRowColumn, getLayoutGuideGridSize } = useLayout();

  /**
   * 计算辅助线的位置,多值
   * @param graph
   */
  const guideLineStyles = (graph: Graph) => {
    // 约束必须在frame内
    if (!graph.constraints || !activeFrame.value) {
      return;
    }

    const left = graph.transform.translate[0];
    const top = graph.transform.translate[1];
    const { width, height } = graph;
    const primaryColor = '#4af';

    const leftLine = {
      position: 'absolute',
      width: `${left}px`,
      height: `1px`,
      transform: `translateX(-${left}px)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const rightLine = {
      position: 'absolute',
      width: `${activeFrame.value.width - left - width}px`,
      height: `1px`,
      transform: `translateX(${width}px)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const hLine = {
      position: 'absolute',
      width: `${width / 2}px`,
      height: `1px`,
      transform: `translateX(50%)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const topLine = {
      position: 'absolute',
      width: `1px`,
      height: `${top}px`,
      transform: `translateY(-${top}px)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const bottomLine = {
      position: 'absolute',
      width: `1px`,
      height: `${activeFrame.value.height - top - height}px`,
      transform: `translateY(${height}px)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const vLine = {
      position: 'absolute',
      width: `1px`,
      height: `${height / 2}px`,
      transform: `translateY(50%)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const lines = [];
    switch (graph.constraints.horizontal) {
      case HoriConsType.Left:
        lines.push(leftLine);
        break;
      case HoriConsType.Right:
        lines.push(rightLine);
        break;
      case HoriConsType.Stretch:
        lines.push(...[leftLine, rightLine]);
        break;
      case HoriConsType.Center:
        lines.push(hLine);
        break;
    }

    switch (graph.constraints.vertical) {
      case VertConsType.Top:
        lines.push(topLine);
        break;
      case VertConsType.Bottom:
        lines.push(bottomLine);
        break;
      case VertConsType.Stretch:
        lines.push(...[topLine, bottomLine]);
        break;
      case VertConsType.Center:
        lines.push(vLine);
        break;
    }
    return lines;
  };

  /**
   * 栅格列、栅格行时辅助线
   * @param graph
   */
  const layoutGuideLineStyles = (graph: Graph, frameWidth: number, frameHeight: number) => {
    // 约束必须在frame内
    if (!graph.constraints) {
      return;
    }

    const left = graph.transform.translate[0];
    const top = graph.transform.translate[1];
    const { width, height } = graph;
    const primaryColor = '#4af';

    const leftLine = {
      position: 'absolute',
      width: `${left}px`,
      height: `1px`,
      transform: `translateX(-${left}px)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const rightLine = {
      position: 'absolute',
      width: `${frameWidth - left - width}px`,
      height: `1px`,
      transform: `translateX(${width}px)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const hLine = {
      position: 'absolute',
      width: `${width / 2}px`,
      height: `1px`,
      transform: `translateX(50%)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const topLine = {
      position: 'absolute',
      width: `1px`,
      height: `${top}px`,
      transform: `translateY(-${top}px)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const bottomLine = {
      position: 'absolute',
      width: `1px`,
      height: `${frameHeight - top - height}px`,
      transform: `translateY(${height}px)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const vLine = {
      position: 'absolute',
      width: `1px`,
      height: `${height / 2}px`,
      transform: `translateY(50%)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const lines = [];
    switch (graph.constraints.horizontal) {
      case HoriConsType.Left:
        lines.push(leftLine);
        break;
      case HoriConsType.Right:
        lines.push(rightLine);
        break;
      case HoriConsType.Stretch:
        lines.push(...[leftLine, rightLine]);
        break;
      case HoriConsType.Center:
        lines.push(hLine);
        break;
    }

    switch (graph.constraints.vertical) {
      case VertConsType.Top:
        lines.push(topLine);
        break;
      case VertConsType.Bottom:
        lines.push(bottomLine);
        break;
      case VertConsType.Stretch:
        lines.push(...[topLine, bottomLine]);
        break;
      case VertConsType.Center:
        lines.push(vLine);
        break;
    }
    return lines;
  };

  /**
   * 是否是默认left、top约束
   * @param graph
   * @returns
   */
  const isDefaultConstraints = (graph: Graph) => {
    return (
      graph.constraints &&
      graph.constraints.horizontal === HoriConsType.Left &&
      graph.constraints.vertical === VertConsType.Top
    );
  };

  /**
   * 计算容器内子元素固定不变的值
   * 思路：在onResizeStart时，记录子元素在容器内固定不变的值，在onResize中，根据固定不变的值计算变化的值
   * @param graph
   * @return {"子元素id": RectPosition}
   */
  const getRectPosition = (frame: Frame) => {
    // 图形在自由布局中，并配置了约束
    // 1. frame未配置栅格：父容器为frame
    // 2. frame配置了栅格：父容器的宽高为栅格的尺寸，图形的位置为gridItem.x、gridItem.y

    let parentWidth = frame.width;
    let parentHeight = frame.height;

    let gridWidth = frame.width;
    let girdHeight = frame.height;

    let isLayoutRC = false;

    // 约束在栅格内，父级为栅格，父级的宽高为栅格的宽高
    if (isLayoutRowColumn(frame)) {
      isLayoutRC = true;
      const { rowLayoutGuide, colLayoutGuide } = getLayoutRowColumn(frame);
      // 栅格的尺寸
      const gridSize = getLayoutGuideGridSize(rowLayoutGuide, colLayoutGuide, frame.width, frame.height);
      gridWidth = gridSize.width;
      girdHeight = gridSize.height;
    }

    const positions: Records<RectPosition> = {};

    frame.children?.forEach((g) => {
      if (g.constraints) {
        let x = g.transform.translate[0];
        let y = g.transform.translate[1];

        // 约束在栅格内，父级为栅格，父级的宽高为栅格的宽高
        if (isLayoutRC && g.gridItem) {
          const { rows, columns } = g.gridItem;
          parentWidth = gridWidth * (columns[1] - columns[0]);
          parentHeight = girdHeight * (rows[1] - rows[0]);
          x = g.gridItem.x;
          y = g.gridItem.y;
        }

        // 子元素不变的数据，离父元素的距离
        const position = {} as RectPosition;

        const ch = g.constraints.horizontal;
        const cv = g.constraints.vertical;

        // ---- 水平----

        // 水平靠右固定：右侧不变的值
        if (ch === HoriConsType.Right) {
          position.right = parentWidth - g.width - x;
        }
        // 水平左右固定：左侧、右侧不变的值
        if (ch === HoriConsType.Stretch) {
          position.left = x;
          position.right = parentWidth - g.width - x;
        }
        // 水平居中：宽度不变，左右按比例变化
        if (ch === HoriConsType.Center) {
          position.pWidth = parentWidth;
          position.left = x;
        }
        // 水平跟随缩放：宽度、左右都按比例变化
        if (ch === HoriConsType.Scale) {
          position.pWidth = parentWidth;
          position.left = x;
          position.width = g.width;
        }

        // ---- 垂直----

        // 垂直靠下固定：下侧不变的值
        if (cv === VertConsType.Bottom) {
          position.bottom = parentHeight - g.height - y;
        }
        // 垂直上下固定：上侧、下侧不变的值
        if (cv === VertConsType.Stretch) {
          position.top = y;
          position.bottom = parentHeight - g.height - y;
        }
        // 垂直上下居中：高度不变，上下按比例变化
        if (cv === VertConsType.Center) {
          position.pHeight = parentHeight;
          position.top = y;
        }

        // 垂直跟随缩放：高度、上下都按比例变化
        if (ch === HoriConsType.Scale) {
          position.pHeight = parentHeight;
          position.top = y;
          position.height = g.height;
        }

        positions[g.id] = position;
      }
    });
    return positions;
  };

  /**
   * 父容器改变尺寸开始时，子元素根据约束改变位置大小
   * @param graph
   * @param positions
   */
  const calcGraphPosition = (frame: Frame, positions: Records<RectPosition>) => {
    // 图形在自由布局中，并配置了约束
    // 1. frame未配置栅格：父容器为frame
    // 2. frame配置了栅格：父容器的宽高为栅格的尺寸，图形的位置为gridItem.x、gridItem.y

    if (frame.children) {
      let parentWidth = frame.width;
      let parentHeight = frame.height;

      let gridWidth = frame.width;
      let girdHeight = frame.height;

      let isLayoutRC = false;

      // 约束在栅格内，父级为栅格，父级的宽高为栅格的宽高
      if (isLayoutRowColumn(frame)) {
        isLayoutRC = true;
        const { rowLayoutGuide, colLayoutGuide } = getLayoutRowColumn(frame);
        // 栅格的尺寸
        const gridSize = getLayoutGuideGridSize(rowLayoutGuide, colLayoutGuide, frame.width, frame.height);
        gridWidth = gridSize.width;
        girdHeight = gridSize.height;
      }
      frame.children.forEach((g) => {
        if (g.constraints) {
          // 约束在栅格内，父级为栅格，父级的宽高为栅格的宽高
          if (isLayoutRC && g.gridItem) {
            const { rows, columns } = g.gridItem;
            parentWidth = gridWidth * (columns[1] - columns[0]);
            parentHeight = girdHeight * (rows[1] - rows[0]);
          }

          const ch = g.constraints.horizontal;
          const cv = g.constraints.vertical;

          // 靠右固定，左侧距离变化
          if (ch === HoriConsType.Right) {
            const x = parentWidth - g.width - positions[g.id].right;
            if (isLayoutRC && g.gridItem) {
              g.gridItem.x = x;
            } else {
              g.transform.translate[0] = x;
            }
          }
          // 左右固定，宽度变化
          if (ch === HoriConsType.Stretch) {
            g.width = parentWidth - positions[g.id].left - positions[g.id].right;
          }
          // 水平居中，宽度不变，左右按比例变化
          if (ch === HoriConsType.Center) {
            // 比例计算：（父旧宽度-子宽度）/ （父新宽度-子宽度）；
            const ratio = (positions[g.id].pWidth - g.width) / (parentWidth - g.width);
            const x = Math.round(positions[g.id].left / ratio);
            if (isLayoutRC && g.gridItem) {
              g.gridItem.x = x;
            } else {
              g.transform.translate[0] = x;
            }
          }
          // 水平跟随缩放：宽度、左右都按比例变化
          if (ch === HoriConsType.Scale) {
            const ratio = positions[g.id].pWidth / parentWidth;
            const x = Math.round(positions[g.id].left / ratio);
            if (isLayoutRC && g.gridItem) {
              g.gridItem.x = x;
            } else {
              g.transform.translate[0] = x;
            }
            g.width = Math.round(positions[g.id].width / ratio);
          }

          // ------------------------------------------------------------------

          // 靠下固定，上侧距离变化
          if (cv === VertConsType.Bottom) {
            const y = parentHeight - g.height - positions[g.id].bottom;
            if (isLayoutRC && g.gridItem) {
              g.gridItem.y = y;
            } else {
              g.transform.translate[1] = y;
            }
          }
          // 上下固定，高度变化
          if (cv === VertConsType.Stretch) {
            g.height = parentHeight - positions[g.id].top - positions[g.id].bottom;
          }
          // 垂直居中，高度不变，上下按比例变化
          if (cv === VertConsType.Center) {
            // 比例计算：（父旧高度-子高度）/ （父新高度-子高度）；
            const ratio = (positions[g.id].pHeight - g.height) / (parentHeight - g.height);

            const y = Math.round(positions[g.id].top / ratio);
            if (isLayoutRC && g.gridItem) {
              g.gridItem.y = y;
            } else {
              g.transform.translate[1] = y;
            }
          }
          // 垂直跟随缩放：高度、上下都按比例变化
          if (cv === VertConsType.Scale) {
            const ratio = positions[g.id].pHeight / parentHeight;
            const y = Math.round(positions[g.id].top / ratio);
            if (isLayoutRC && g.gridItem) {
              g.gridItem.y = y;
            } else {
              g.transform.translate[1] = y;
            }
            g.height = Math.round(positions[g.id].height / ratio);
          }
        }
      });
    }
  };

  /**
   * frame尺寸改变后，重新计算子元素位置及大小
   * @param frame 容器
   * @param oldFrameWidth 不传是不计算约束
   * @param oldFrameHeight
   */
  const setChildrenPosition = (frame: Frame, oldFrameWidth?: number, oldFrameHeight?: number) => {
    const isChange =
      oldFrameWidth && oldFrameHeight && (frame.width !== oldFrameWidth || frame.height !== oldFrameHeight);
    // isChange && console.log('width:', oldFrameWidth, frame.width, 'height:', oldFrameHeight, frame.height);
    // 忽略了自动布局不需要计算
    if (!frame.ignoreAutoLayout) {
      // 自由布局时：子元素约束不是top、left需要重新计算
      if (frame.autoLayout.direction === DirectionType.Freeform) {
        if (isChange) {
          const conList = frame.children.filter((g) => !isDefaultConstraints(g));
          if (conList.length) {
            const rectPositions = getRectPosition({ ...frame, ...{ width: oldFrameWidth, height: oldFrameHeight } });
            calcGraphPosition(frame, rectPositions);
          }
        }
      } else {
        // 自动布局时，分两种情况：
        // 1. 子元素是flexItem,并且有一个子元素配置了充满容器时,根据dom重新计算位置和大小。（子元素都是固定宽度时不需要重新计算宽，子元素都是固定高度时不需要重新计算高）
        // 2. 子元素是忽略自动布局(约束不是top、left)时,需要根据frame的位置重新计算子元素的位置

        // 内部是否包含需要计算的约束
        let isConstraints = false;
        const isWidthFIll = frame.children.find((g) => g.limitSize.width.resize === ResizeType.Fill);
        const isHeightFIll = frame.children.find((g) => g.limitSize.height.resize === ResizeType.Fill);

        frame.children.forEach((g) => {
          if (g.ignoreAutoLayout) {
            if (!isDefaultConstraints(g)) {
              isConstraints = true;
            }
          } else {
            const gDom = document.querySelector(`[id="${g.id}"]`) as HTMLElement;
            if (gDom) {
              isWidthFIll && (g.width = Math.round(gDom.clientWidth));
              isHeightFIll && (g.height = Math.round(gDom.clientHeight));
              g.transform.translate = [Math.round(gDom.offsetLeft), Math.round(gDom.offsetTop)];
            }
          }
        });

        // 子元素配置了忽略自动布局，重新计算位置
        if (isConstraints && isChange) {
          const rectPositions = getRectPosition({ ...frame, ...{ width: oldFrameWidth, height: oldFrameHeight } });
          calcGraphPosition(frame, rectPositions);
        }
      }
    }
  };

  return {
    guideLineStyles,
    layoutGuideLineStyles,

    isDefaultConstraints,

    getRectPosition,
    calcGraphPosition,

    setChildrenPosition
  };
};

export type RectPosition = {
  // 子元素离父容器的距离
  left: number;
  right: number;
  top: number;
  bottom: number;
  // 子元素宽高
  width: number;
  height: number;
  // 父元素宽高
  pWidth: number;
  pHeight: number;
};
