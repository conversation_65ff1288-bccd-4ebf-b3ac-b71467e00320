import { useDocumentStore } from '../stores';
import {
  CrossAlignment,
  DirectionType,
  HoriConsType,
  LayoutGuide,
  LayoutGuideType,
  MainAlignment,
  PatternMode,
  ResizeType,
  VertConsType,
  type Frame,
  type Graph
} from '../models';
import type { Records } from '@hetu/util';
import { isNumber } from 'lodash-es';

/**
 * 图形布局
 * @returns
 */
export const useLayout = () => {
  const docStore = useDocumentStore();

  const isFreeform = (frame: Frame) => frame.autoLayout.direction === DirectionType.Freeform;

  const isFlex = (frame: Frame) =>
    [DirectionType.Horizontal, DirectionType.Vertical].includes(frame.autoLayout.direction);

  const isGrid = (frame: Frame) => frame.autoLayout.direction === DirectionType.Grid;

  /**
   * 是否配置栅格行、栅格列
   * @param frame
   * @returns
   */
  const isLayoutRowColumn = (frame: Frame) =>
    frame.layoutGuide.length && !!frame.layoutGuide.filter((lg) => lg.type !== LayoutGuideType.Grid);

  /**
   * 返回栅格列和栅格行，配置多个栅格时，最后的一个生效
   * @param frame
   * @returns
   */
  const getLayoutRowColumn = (frame: Frame) => {
    const rowColumnLayoutGuides = frame.layoutGuide.filter((lg) => lg.type !== LayoutGuideType.Grid);
    const rowLayoutGuide = [...rowColumnLayoutGuides].reverse().find((lg) => lg.type === LayoutGuideType.Row);
    const colLayoutGuide = [...rowColumnLayoutGuides].reverse().find((lg) => lg.type === LayoutGuideType.Column);
    return { rowLayoutGuide, colLayoutGuide };
  };
  /**
   * 自由布局样式
   * @param graph
   */
  const freeformStyle = (graph: Graph) => {
    const { width, height } = graph;
    const x = graph.transform.translate[0];
    const y = graph.transform.translate[1];
    const transform = `translate(${x}px, ${y}px) rotate(${graph.transform.rotate}deg)`;

    return {
      position: 'absolute',
      width: `${width}px`,
      height: `${height}px`,
      transform: transform,
      transformOrigin: graph.transform.origin
    };
  };

  /**
   * flex布局时的frame样式
   * @param graph
   * @param parent
   * @returns
   */
  const frameFlexStyle = (frame: Frame) => {
    const x = frame.transform.translate[0];
    const y = frame.transform.translate[1];
    const transform = `translate(${x}px, ${y}px) rotate(${frame.transform.rotate}deg)`;

    let style: Records<string | number> = {};

    // 父容器配置了flex布局，flex布局的样式在frame.ts里处理
    if (frame.autoLayout) {
      // flex布局和grid布局在wrap上处理，这里不需要设置宽高
      if (!isFreeform(frame)) {
        style = {
          position: 'absolute',
          width: `auto`,
          height: `auto`,
          transform: transform,
          transformOrigin: frame.transform.origin
        };
      }
    }
    return style;
  };

  /**
   * flex布局下的子元素样式
   * @param graph
   * @param parent
   * @returns
   */
  const flexItemStyle = (graph: Graph, parent: Frame) => {
    const { width, height } = graph;

    let itemStyle: Records<string | number> = {};

    // 父容器配置了自动布局后，处理子元素的样式
    if (parent.autoLayout) {
      const { direction } = parent.autoLayout;
      if (!isFreeform(parent) && !graph.ignoreAutoLayout) {
        itemStyle = {
          position: 'relative',
          order: graph.order
        };

        // 子元素： 固定尺寸 / 充满容器
        switch (graph.limitSize.width.resize) {
          case ResizeType.Fixed:
            itemStyle.width = `${width}px`;
            break;
          case ResizeType.Fill: {
            if (direction === DirectionType.Horizontal) {
              // 水平时，宽：占用剩余宽度
              itemStyle.flex = 1;
            } else {
              // 垂直时，宽：充满整个容器
              itemStyle.alignSelf = 'stretch';
            }
          }
        }

        switch (graph.limitSize.height.resize) {
          case ResizeType.Fixed:
            itemStyle.height = `${height}px`;
            break;
          case ResizeType.Fill:
            if (direction === DirectionType.Horizontal) {
              // 水平时，高度充满
              itemStyle.alignSelf = 'stretch';
            } else {
              // 垂直时，高度占剩余宽度
              itemStyle.flex = 1;
            }
        }

        // 最大最小限制
        if (isNumber(graph.limitSize.width.max)) {
          itemStyle.maxWidth = `${graph.limitSize.width.max}px`;
        }
        if (isNumber(graph.limitSize.width.min)) {
          itemStyle.minWidth = `${graph.limitSize.width.min}px`;
        }
        if (isNumber(graph.limitSize.height.max)) {
          itemStyle.maxHeight = `${graph.limitSize.height.max}px`;
        }
        if (isNumber(graph.limitSize.height.min)) {
          itemStyle.minHeight = `${graph.limitSize.height.min}px`;
        }
      }
    }
    return itemStyle;
  };

  /**
   * 返回frame wrap的样式， flex、grid在此元素上
   * @param frame
   * @returns
   */
  const frameWrapStyle = (frame: Frame) => {
    const { width, height } = frame;

    // wrap处理滚动条
    const style: Records<string | number> = {
      position: 'absolute',
      width: '100%',
      height: '100%',
      overflow: frame.clip ? 'hidden' : ''
    };

    const {
      direction,
      flowWarp,
      mainAxisAlignment,
      crossAxisAlignment,
      horizontalGap,
      verticalGap,
      padding,
      gridRowsSizing,
      gridColumnsSizing
    } = frame.autoLayout;

    let layoutStyle: Records<string | number> = {};

    const rowColumnLayoutGuides = frame.layoutGuide.filter((lg) => lg.type !== LayoutGuideType.Grid && lg.visible);

    // flex布局
    if (isFlex(frame)) {
      const isH = direction === DirectionType.Horizontal;
      if (isH) {
        layoutStyle = {
          position: 'relative',
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'nowrap',
          justifyContent: mainAxisAlignment,
          alignItems: crossAxisAlignment,
          columnGap: `${horizontalGap}px`,
          rowGap: `${verticalGap}px`,
          padding: padding.join('px ') + 'px'
        };

        if (flowWarp) {
          layoutStyle.flexWrap = 'wrap';
        }

        if (horizontalGap === 'Auto') {
          layoutStyle.justifyContent = MainAlignment.Between;
        }
        if (verticalGap === 'Auto') {
          layoutStyle.alignContent = MainAlignment.Between;
        } else {
          layoutStyle.alignContent = crossAxisAlignment;
        }
      } else {
        layoutStyle = {
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          flexWrap: 'nowrap',
          justifyContent: crossAxisAlignment,
          alignItems: mainAxisAlignment,
          rowGap: `${verticalGap}px`,
          padding: padding.join('px ') + 'px'
        };
        if (verticalGap === 'Auto') {
          layoutStyle.justifyContent = CrossAlignment.Between;
          layoutStyle.alignItems = crossAxisAlignment;
        }
      }

      // 父级：固定尺寸 / 适应内容
      switch (frame.limitSize.width.resize) {
        case ResizeType.Fixed:
          layoutStyle.width = `${width}px`;
          break;
        case ResizeType.Adapt:
          layoutStyle.width = 'auto';
      }

      switch (frame.limitSize.height.resize) {
        case ResizeType.Fixed:
          layoutStyle.height = `${height}px`;
          break;
        case ResizeType.Adapt:
          layoutStyle.height = 'auto';
      }
    }
    // 网格布局
    else if (isGrid(frame)) {
      const templateRows = gridRowsSizing.map((size) => (isNaN(parseInt(`${size}`)) ? 'minmax(0, 1fr)' : `${size}px`));
      const templateColumns = gridColumnsSizing.map((size) =>
        isNaN(parseInt(`${size}`)) ? 'minmax(0, 1fr)' : `${size}px`
      );
      layoutStyle = {
        display: 'grid',
        gridTemplateRows: templateRows.join(' '),
        gridTemplateColumns: templateColumns.join(' '),
        columnGap: `${horizontalGap}px`,
        rowGap: `${verticalGap}px`,
        padding: padding.join('px ') + 'px'
      };
    }
    // 栅格行、栅格列
    else if (isLayoutRowColumn(frame)) {
      const { rowLayoutGuide, colLayoutGuide } = getLayoutRowColumn(frame);

      if (colLayoutGuide) {
        const { width, offset, gutter, count, pattern } = colLayoutGuide;
        layoutStyle = {
          display: 'grid',
          gridTemplateColumns: `repeat(${count}, ${width === 'Auto' ? 'minmax(0px, 1fr)' : width + 'px'})`,
          columnGap: `${gutter}px`,
          paddingLeft: `${offset}px`,
          paddingRight: `${offset}px`,
          justifyContent: pattern
        };
      }

      if (rowLayoutGuide) {
        const { width, offset, gutter, count, pattern } = rowLayoutGuide;
        Object.assign(layoutStyle, {
          display: 'grid',
          gridTemplateRows: `repeat(${count}, ${width === 'Auto' ? 'minmax(0px, 1fr)' : width + 'px'})`,
          rowGap: `${gutter}px`,
          paddingTop: `${offset}px`,
          paddingBottom: `${offset}px`,
          alignContent: pattern
        });
      }
    }
    return { ...style, ...layoutStyle };
  };

  /**
   * grid布局时的frame样式
   * @param frame
   */
  const frameGridStyle = (frame: Frame) => {
    const { width, height } = frame;
    const x = frame.transform.translate[0];
    const y = frame.transform.translate[1];
    const transform = `translate(${x}px, ${y}px) rotate(${frame.transform.rotate}deg)`;

    let style: Records<string | number> = {};

    // 父容器配置了flex布局，flex布局的样式在frame.ts里处理
    if (frame.autoLayout) {
      // flex布局和grid布局在wrap上处理，这里不需要设置宽高
      style = {
        position: 'absolute',
        width: `${width}px`,
        height: `${height}px`,
        transform: transform,
        transformOrigin: frame.transform.origin
      };
    }
    return style;
  };

  /**
   * grid布局下的子元素样式
   * @param graph
   * @param parent
   * @returns
   */
  const gridItemStyle = (graph: Graph, parent: Frame) => {
    const { width, height } = graph;

    let itemStyle: Records<string | number> = {};

    // 父容器配置了自动布局后，处理子元素的样式
    if (parent.autoLayout && graph.gridItem) {
      const { rows, columns, justifySelf, alignSelf } = graph.gridItem;
      itemStyle = {
        position: 'relative',
        order: graph.order,
        gridRowStart: rows[0],
        gridRowEnd: rows[1],
        gridColumnStart: columns[0],
        gridColumnEnd: columns[1]
      };

      // 子元素： 固定尺寸 / 充满容器
      switch (graph.limitSize.width.resize) {
        case ResizeType.Fixed:
          itemStyle.width = `${width}px`;
          itemStyle.justifySelf = justifySelf;
          break;
        case ResizeType.Fill:
          break;
      }

      switch (graph.limitSize.height.resize) {
        case ResizeType.Fixed:
          itemStyle.height = `${height}px`;
          itemStyle.alignSelf = alignSelf;
          break;
        case ResizeType.Fill:
          break;
      }

      // 最大最小限制
      if (isNumber(graph.limitSize.width.max)) {
        itemStyle.maxWidth = `${graph.limitSize.width.max}px`;
      }
      if (isNumber(graph.limitSize.width.min)) {
        itemStyle.minWidth = `${graph.limitSize.width.min}px`;
      }
      if (isNumber(graph.limitSize.height.max)) {
        itemStyle.maxHeight = `${graph.limitSize.height.max}px`;
      }
      if (isNumber(graph.limitSize.height.min)) {
        itemStyle.minHeight = `${graph.limitSize.height.min}px`;
      }
    }
    return itemStyle;
  };

  /**
   * 返回配置约束图形的样式
   * @param graph
   * @param frame
   */
  const constraintsStyle = (graph: Graph, frame: Frame) => {
    const style: Records = {
      position: 'absolute'
    };
    if (!graph.constraints) {
      return style;
    }

    const { width, height } = graph;
    let x = graph.transform.translate[0];
    let y = graph.transform.translate[1];

    let frameWidth = frame.width;
    let frameHeight = frame.height;

    let isLayout = false;

    // 约束在栅格内，父级为栅格，父级的宽高为栅格的宽高， x,y为gridItem中的x,y
    if (isLayoutRowColumn(frame) && graph.gridItem) {
      isLayout = true;
      const { rowLayoutGuide, colLayoutGuide } = getLayoutRowColumn(frame);
      // 每个栅格的尺寸
      const gridSize = getLayoutGuideGridSize(rowLayoutGuide, colLayoutGuide, frame.width, frame.height);
      const gridWidth = gridSize.width;
      const girdHeight = gridSize.height;

      const { rows, columns } = graph.gridItem;
      frameWidth = gridWidth * (columns[1] - columns[0]);
      frameHeight = girdHeight * (rows[1] - rows[0]);

      x = graph.gridItem.x;
      y = graph.gridItem.y;
    }

    const ch = graph.constraints.horizontal;
    const cv = graph.constraints.vertical;

    // 靠左固定: 宽度、左侧距离不变，右侧距离变化
    if (ch === HoriConsType.Left) {
      style.left = `${x}px`;
      style.width = `${width}px`;
    }

    // 靠右固定: 宽度、右侧距离不变，左侧距离变化
    if (ch === HoriConsType.Right) {
      style.right = `${frameWidth - width - x}px`;
      style.width = `${width}px`;
    }
    // 水平左右固定：左侧、右侧不变,宽度变化
    if (ch === HoriConsType.Stretch) {
      style.left = `${x}px`;
      style.right = `${frameWidth - width - x}px`;
    }
    // 水平居中：宽度不变，左右按比例变化
    if (ch === HoriConsType.Center) {
      style.width = `${width}px`;
      const centerX = (frameWidth - width) / 2;
      style.left = `calc(50% - ${width}px / 2 + ${x - centerX}px)`;
    }
    // 水平跟随缩放：宽度、左右都按比例变化
    if (ch === HoriConsType.Scale) {
      style.left = `${(x / frameWidth) * 100}%`;
      style.right = `${((frameWidth - width - x) / frameWidth) * 100}%`;
    }

    // 垂直靠上固定：高度、上侧距离不变，下侧变化
    if (cv === VertConsType.Top) {
      style.top = `${y}px`;
      style.height = `${height}px`;
    }

    // 垂直靠下固定：高度、下侧距离不变, 上侧变化
    if (cv === VertConsType.Bottom) {
      style.bottom = `${frameHeight - height - y}px`;
      style.height = `${height}px`;
    }
    // 垂直上下固定：上侧、下侧不变的值,高度变化
    if (cv === VertConsType.Stretch) {
      style.top = `${y}px`;
      style.bottom = `${frameHeight - height - y}px`;
      isLayout && (style.height = '100%');
    }
    // 垂直居中，高度不变，上下按比例变化
    if (cv === VertConsType.Center) {
      style.height = `${height}px`;
      style.top = `calc(50% - ${height}px / 2)`;
    }
    // 垂直居中，高度不变，上下按比例变化
    if (cv === VertConsType.Center) {
      style.height = `${height}px`;
      const centerY = (frameHeight - height) / 2;
      style.top = `calc(50% - ${height}px / 2 + ${y - centerY}px)`;
    }
    // 垂直跟随缩放：高度、上下都按比例变化
    if (cv === VertConsType.Scale) {
      style.top = `${(y / frameHeight) * 100}%`;
      style.bottom = `${((frameHeight - height - y) / frameHeight) * 100}%`;
    }

    if (isLayout) {
      if (!style.width) {
        style.width = `calc(100% - ${style.left} - ${style.right}`;
      }
      if (!style.height) {
        style.height = `calc(100% - ${style.top} - ${style.bottom})`;
      }
    }

    return style;
  };

  /**
   * 栅格行、栅格列下的子元素样式
   * @param graph
   * @param parent
   */
  const layoutGuideItemStyle = (graph: Graph, parent: Frame) => {
    const { width, height, gridItem } = graph;

    let itemStyle: Records<string | number> = {};

    if (gridItem) {
      const { rows, columns, x, y, justifySelf, alignSelf } = gridItem;

      itemStyle = {
        position: 'absolute',
        order: graph.order,
        gridRowStart: rows[0],
        gridRowEnd: rows[1],
        gridColumnStart: columns[0],
        gridColumnEnd: columns[1],
        justifySelf: justifySelf,
        alignSelf: alignSelf
      };

      // 设计器中约束在onResize中处理
      if (docStore.isDesignMode.value) {
        Object.assign(itemStyle, {
          width: `${width}px`,
          height: `${height}px`,
          left: `${x}px`,
          top: `${y}px`
        });
      } else {
        Object.assign(itemStyle, constraintsStyle(graph, parent));
      }
    }

    return itemStyle;
  };

  /**
   * 返回栅格宽度和高度
   * @param rowLayoutGuide
   * @param colLayoutGuide
   * @param frameWidth
   * @param frameHeight
   */
  const getLayoutGuideGridSize = (
    rowLayoutGuide: LayoutGuide | undefined,
    colLayoutGuide: LayoutGuide | undefined,
    frameWidth: number,
    frameHeight: number
  ) => {
    let width = 0;
    let height = 0;

    if (colLayoutGuide) {
      const { count, offset, gutter, pattern } = colLayoutGuide;
      if (pattern === PatternMode.Stretch) {
        // 每个栅格列的宽度：（容器的宽度 - 左右边距 - 槽宽）/ 列数
        width = (frameWidth - offset * 2 - gutter * (count - 1)) / count;
      } else {
        width = colLayoutGuide.width as number;
      }
    }

    if (rowLayoutGuide) {
      const { count, offset, gutter, pattern } = rowLayoutGuide;
      if (pattern === PatternMode.Stretch) {
        // 每个栅格行的高度：（容器的高度 - 上下边距 - 槽宽）/ 行数
        height = (frameHeight - offset * 2 - gutter * (count - 1)) / count;
      } else {
        height = rowLayoutGuide.width as number;
      }
    }

    return { width, height };
  };

  return {
    isFreeform,
    isFlex,
    isGrid,
    freeformStyle,
    frameFlexStyle,
    flexItemStyle,
    frameGridStyle,
    frameWrapStyle,
    gridItemStyle,
    constraintsStyle,

    isLayoutRowColumn,
    getLayoutRowColumn,
    layoutGuideItemStyle,
    getLayoutGuideGridSize
  };
};
