import { UUID } from '@hetu/platform-shared';
import {
  useWidgetConfigStore,
  WidgetConfig,
  WidgetBlock,
  WidgetType,
  Color,
  Effects,
  FillPaints,
  FillType,
  Stroke,
  useDocumentStore,
  AxisField
} from '@vis/document-core';
import type { Records } from '@hetu/util';

export const useWidget = () => {

  const { widgetConfigs, optionConfigs, dataConfigs } = useWidgetConfigStore();

  /**
   * 获取组件所属组
   */
  const getWidgetGroup = (widgetType: WidgetType) => {
    return widgetConfigs.getConfig(widgetType)?.group;
  };

  /**
   * 将组件处理成画布需要的格式
   */
  const handleWidget = (widget: WidgetConfig) => {
    const widgetType = widget.type as WidgetType;
    const _widget = new WidgetBlock();
    const dataConfig = dataConfigs.get(widgetType);
    const dataFields = dataConfig.matchs ? transformDataMatchs(dataConfig?.matchs) : {};
    const newWidget = {
      ..._widget,
      id: UUID(),
      type: widgetType,
      name: widget.name,
      datasetType: dataConfig.datasetType,
      datasetId: dataConfig.datasetId,
      ...dataFields,
      options: optionConfigs?.[widgetType]?.options
    } as WidgetBlock;

    const block = optionConfigs[widgetType].block;
    return block
      ? {
          widgetBlock: newWidget,
          block
        }
      : {
          widgetBlock: newWidget
        };
  };

  /**
   * 定义映射类
   */
  const classMap: Records<any> = {
    stroke: Stroke,
    fillPaints: FillPaints,
    effects: Effects
  };

  /**
   * 判断obj上key是否存在或为undefined
   * @param obj
   * @param key
   * @returns
   */
  const isUndefined = (obj: Object, key: string) => {
    return !obj[key as keyof typeof obj];
  };

  /**
   * 为obj添加属性或置为undefined
   * @param obj
   * @param key
   */
  const handleManage = (obj: Object, key: string) => {
    if (isUndefined(obj, key)) {
      // 特殊类型可传入参数
      const args = key === 'fillPaints' ? [FillType.Solid, new Color(0, 0, 0, 0.1)] : [];

      Object.assign(obj, { [key]: new classMap[key](...args) });
    } else {
      Object.assign(obj, { [key]: undefined });
    }
  };

  /**
   * 转换数据格式
   * @param obj 原始对象
   * @returns 转换后的格式
   */
  const transformDataMatchs = (obj: Record<string, any>) => {
    const result: Record<string, AxisField[]> = {};

    Object.keys(obj).forEach((key) => {
      const item = obj[key];
      if (item && typeof item === 'object' && item.name && item.field) {
        result[key] = item.field.map((field: AxisField) => {
          return {
            ...field,
            id: field.fieldName,
            fieldDatatype: field.dataType,
            fieldType: field.fieldType
          };
        });
      }
    });
    return result;
  };

  return {
    handleWidget,
    isUndefined,
    handleManage,
    getWidgetGroup
  };
};
