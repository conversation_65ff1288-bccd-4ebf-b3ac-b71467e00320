<template>
  <div v-if="activeBlock">
    <div class="vis-config-style-data">
      <div class="vis-config-style-data__tree">
        <vis-dataset-tree v-model="activeBlock.datasetId" :name="datasetName" @onSelect="selectDataset" />
      </div>
      <div class="vis-config-style-data__btn">
        <q-btn class="!w-6" @click="openDataSheetDialog">
          <ht-icon name="vis-chart" class="vis-icon"></ht-icon>
        </q-btn>
      </div>
    </div>
    <q-separator></q-separator>
    <div v-for="(match, key) in widgetDataConfig" :key="key">
      <div class="vis-config-style-data__match">
        <div class="vis-config-style-data__match-name">
          <div>{{ match.name }} <span v-if="match.required" class="text-red-500">*</span></div>
        </div>
        <div class="vis-config-style-data__match-field">
          <vis-field-item :mapKey="key" :fieldMapping="fieldMapping" />
        </div>
      </div>
      <q-separator></q-separator>
    </div>
    <div class="vis-config-style-data__match">
      <div class="vis-config-style-data__match-name  !py-2">
        <span>筛选</span>
        <q-btn>
          <ht-icon class="vis-icon min-w-6" name="vis-add" />
          <vis-field-list
            :fieldList="fieldMapping?.datasetField"
            :activeIds="filterActiveIds"
            :offset="[-24, 8]"
            @addField="addFilterField"
          />
        </q-btn>
      </div>
      <div class="vis-config-style-data__match-field" v-if="filters?.length > 0">
        <div v-for="(filter, index) in filters" :key="index" class="container">
          <div class="field-item">
            <q-btn @click="openFilterDialog(Number(index))" class="!w-6">
              <ht-icon :name="`vis-${getFieldIcon(filter)}`" class="vis-icon"></ht-icon>
            </q-btn>
            <q-btn :ripple="false" class="field-btn">
              <span class="w-[calc(100%-12px)] text-left">
                {{ filter?.fieldAlias || filter?.fieldName || '字段' }}
              </span>
            </q-btn>
            <q-btn :ripple="false" class="remove-btn !w-6" @click="removeFilterField(Number(index))">
              <ht-icon name="vis-remove" class="vis-icon"></ht-icon>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
    <q-separator></q-separator>
    <div class="p-3">
      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-select v-model="activeBlock.refreshType" :options="refreshTypeOptions">
                <template #option="{ opt, itemProps }">
                  <q-item class="flex items-center" v-bind="itemProps">
                    <q-item-section>
                      {{ opt.label }}
                    </q-item-section>
                  </q-item>
                </template>
              </vis-select>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-number v-model="activeBlock.refreshSecond" :min="0" :step="1" icon="o_av_timer">
                <template #append>
                  <span>秒/次</span>
                </template>
              </vis-number>
            </div>
          </div>
        </div>
      </div>
      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">
              <span>起始行</span>
            </div>
            <div class="vis-form-field__content">
              <vis-number v-model="activeBlock.startRow" :min="0" :step="1" icon="o_dataset"> </vis-number>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__label">
              <span>数据量</span>
            </div>
            <div class="vis-form-field__content">
              <vis-number v-model="activeBlock.rowsLimit" :min="0" :step="1" icon="o_table_rows">
                <template #append>
                  <span>条</span>
                </template>
              </vis-number>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./data.ts"></script>
<style lang="scss" src="./data.scss"></style>
