import { defineComponent, ref, computed, nextTick, watch } from 'vue';
import type { MenuPosition, ContextMenuPopupOptions } from '../../../../../models';

export default defineComponent({
  name: 'vis-page-popup',
  emits: ['ok', 'cancel'],
  setup(props, { emit, expose }) {
    // #region 二次弹框
    const SAFE_MARGIN = 10; // 安全边距
    const popupRef = ref();
    const popupShow = ref<boolean>(false);
    const position = ref<MenuPosition>({ x: 0, y: 0 });
    const options = ref<any>();

    const popupStyle = computed(() => {
      return {
        position: 'fixed' as const,
        left: `${position.value.x}px`,
        top: `${position.value.y}px`,
        zIndex: 6001
      };
    });

    const adjustPopupPosition = () => {
      if (!popupShow.value || !popupRef.value) return;
      nextTick(() => {
        const contextMenu = document.querySelector('.vis-context-menu');
        const menuRect = contextMenu!.getBoundingClientRect();

        const popup = popupRef.value;
        const popupRect = popup!.getBoundingClientRect();
        const popupWidth = popupRect.width;
        const popupHeight = popupRect.height;
        const vw = window.innerWidth;
        const vh = window.innerHeight;

        let { x, y } = position.value;

        // 鼠标到右侧距离，不足则翻转显示
        const rightSpace = vw - x - SAFE_MARGIN;
        if (rightSpace < popupWidth) {
          x = menuRect.left - menuRect.width;
        } else {
          x = menuRect.left + menuRect.width;
        }

        // 鼠标到底部距离
        const bottomSpace = vh - y - SAFE_MARGIN;
        if (bottomSpace < popupHeight) {
          y = vh - popupHeight - SAFE_MARGIN;
        }

        // 确保不超出左边界
        if (x < SAFE_MARGIN) {
          x = SAFE_MARGIN;
        }

        // 确保不超出上边界
        if (y < SAFE_MARGIN) {
          y = SAFE_MARGIN;
        }

        position.value = { x, y };
      });
    };

    const open = (event: MouseEvent, opt: ContextMenuPopupOptions) => {
      position.value = {
        x: event.clientX,
        y: event.clientY
      };

      options.value = opt;

      popupShow.value = true;
    };

    const close = () => {
      popupShow.value = false;
    };

    const onConfirm = () => {
      options.value.confirm && options.value.confirm();
      emit('ok', options.value);
      popupShow.value = false;
    };

    const onCancel = () => {
      options.value.cancel && options.value.cancel();
      emit('cancel');
      popupShow.value = false;
    };

    // #endregion

    watch(
      () => popupShow.value,
      (val) => {
        val && nextTick(() => adjustPopupPosition());
      }
    );

    expose({
      open,
      close
    });

    return {
      popupRef,
      popupShow,
      onConfirm,
      onCancel,
      popupStyle,
      options
    };
  }
});
