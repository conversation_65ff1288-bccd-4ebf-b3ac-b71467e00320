@import '../../../index.scss';
.#{$vis-design-prefix}-canvas {
  &--moveable {
    z-index: $active-ghost-z-index !important;
    .moveable-control {
      width: 8px !important;
      height: 8px !important;
      margin-top: -4px !important;
      margin-left: -4px !important;
      border: 1px solid $canvas-theme-color !important;
      border-radius: 30% !important;
      background: #fff !important;
    }

    // 旋转时中心原点再显示
    &.moveable-rotatable-dragging {
      .moveable-origin {
        display: block;
      }
    }
    // 旋转中心点
    .moveable-origin {
      border-color: #f56c6c !important;
      display: none;
    }
    // 旋转手柄样式
    .moveable-rotation {
      height: 10px !important;

      &-line {
        display: none !important;
      }

      &-control {
        height: 20px !important;
        width: 20px !important;
        opacity: 0;
      }
      &.tl {
        .moveable-control {
          top: -2px;
          left: -10px;
        }
      }
      &.br {
        .moveable-control {
          left: -12px;
        }
      }
    }

    // 圆角手柄样式及位置
    .moveable-border-radius {
      border-radius: 50% !important;
    }

    div[data-radius-index='0'] {
      left: 15px;
    }

    div[data-radius-index='1'] {
      left: -15px;
    }

    div[data-radius-index='2'] {
      left: -15px;
    }

    div[data-radius-index='3'] {
      left: 15px;
    }
  }
}
