<template>
  <q-dialog
    ref="dialogRef"
    v-model="isVisible"
    :persistent="persistent"
    @show="onShow"
    @hide="onHide"
    class="vis-dialog"
    :class="{ fullscreen: isFullscreen }"
  >
    <q-card
      ref="dialogCard"
      :style="{
        ...dialogStyle,
        ...transformStyle
      }"
    >
      <!-- 拖拽区域 -->
      <div :class="{ dragging: isDragging }" :style="{ cursor: draggable ? 'move' : 'default' }" @mousedown="startDrag">
        <slot name="title"> </slot>
      </div>

      <!-- 内容区域 -->
      <slot></slot>

      <!-- 底部按钮区域 -->
      <slot v-if="$slots.actions" name="actions"></slot>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
