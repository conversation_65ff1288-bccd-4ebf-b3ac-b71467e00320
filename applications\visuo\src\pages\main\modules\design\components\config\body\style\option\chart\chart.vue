<template>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span>图形</span>
    </div>
    <div class="vis-config-card__body">
     <!--  {{ chartOptions.mark }} -->
      <q-separator />
    </div>
  </div>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span>X 轴</span>
      <div class="vis-config-card__header-btn">
        <q-btn flat dense @click="handleAxis('xAxis')">
          <ht-icon class="vis-icon" :name="chartOptions.xAxis.visible ? 'vis-eye-o' : 'vis-eye-c'" />
        </q-btn>
        <q-btn flat dense>
          <ht-icon class="vis-icon" :name="false ? 'vis-subtract' : 'vis-add'" />
        </q-btn>
      </div>
    </div>
    <div class="vis-config-card__body">
      <vis-config-chart-axis :options="chartOptions.xAxis" />
      <q-separator />
    </div>
  </div>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span>Y 轴</span>
      <div class="vis-config-card__header-btn">
        <q-btn flat dense @click="handleAxis('yAxis')">
          <ht-icon class="vis-icon" :name="chartOptions.yAxis.visible ? 'vis-eye-o' : 'vis-eye-c'" />
        </q-btn>
        <q-btn flat dense>
          <ht-icon class="vis-icon" :name="false ? 'vis-subtract' : 'vis-add'" />
        </q-btn>
      </div>
    </div>
    <div class="vis-config-card__body">
      <vis-config-chart-axis :options="chartOptions.yAxis" />
    </div>
  </div>
</template>
<script lang="ts" src="./chart.ts"></script>
