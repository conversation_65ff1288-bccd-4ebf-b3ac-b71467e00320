import {
  CrossAlignment,
  Frame,
  GridItem,
  LayoutGuide,
  LayoutGuideType,
  PatternMode,
  useFill,
  useGraphStyle,
  useLayout
} from '@vis/document-core';
import { useDesignStore } from '../../../stores/design.store';
import { defineComponent, ref, computed, type PropType, watch } from 'vue';
import { useConstraints } from '../../../hooks';
import type { Records } from '@hetu/util';
import { nextTick } from 'vue';

/**
 * 容器
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-design-frame',
  components: {},
  props: {
    graph: {
      type: Object as PropType<Frame>,
      required: true
    },
    parent: {
      type: Object as PropType<Frame>,
      required: false
    }
  },
  setup(props) {
    const designStore = useDesignStore();
    const activeGraphIds = computed(() => designStore.active.value.graphIds);
    const activeGraph = computed(() => designStore.active.value.graph);
    const activeFrame = computed(() => designStore.active.value.frame);
    const canvasState = computed(() => designStore.canvasState.value);

    const frame = computed(() => props.graph);
    const parent = computed(() => props.parent);

    //#region 编辑名称
    const isEdit = ref(false);
    const inputRef = ref<HTMLInputElement>();
    const onClickName = () => {
      isEdit.value = true;
      inputRef.value?.focus();
      inputRef.value?.select();
    };
    //#endregion

    const {
      isFreeform,
      isFlex,
      isGrid,
      freeformStyle,
      frameFlexStyle,
      flexItemStyle,
      frameWrapStyle,
      frameGridStyle,
      gridItemStyle,
      layoutGuideItemStyle,
      isLayoutRowColumn
    } = useLayout();
    const { graphClassName, graphBaseStyle } = useGraphStyle();
    const { setChildrenPosition } = useConstraints();

    const className = computed(() => {
      const name = graphClassName(frame.value);
      name.push(frame.value.autoLayout.direction);
      if (activeGraphIds.value.includes(frame.value.id)) {
        name.push('active');
      }
      if (canvasState.value.frame?.id === frame.value.id) {
        name.push('chosen-frame');
      }
      if (activeFrame.value && activeFrame.value.id === frame.value.id) {
        name.push('active-frame');
      }
      if (canvasState.value.dragging.includes(frame.value.id)) {
        name.push('dragging');
      }

      return name.join(' ');
    });

    const frameStyle = computed(() => {
      const getFrameStyle = (frame: Frame, parent?: Frame) => {
        // 基础样式
        const baseStyle = graphBaseStyle(frame);

        let positionStyle: Records<string | number> = {};

        if (isFlex(frame)) {
          positionStyle = frameFlexStyle(frame);
        } else if (isGrid(frame)) {
          positionStyle = frameGridStyle(frame);
        } else if (isFreeform(frame)) {
          positionStyle = freeformStyle(frame);
        }

        // 说明frame是容器下的子元素
        if (parent) {
          // 父级是flex布局
          if (isFlex(parent)) {
            // 忽略自动布局时，按约束布局
            if (frame.ignoreAutoLayout && frame.constraints) {
              positionStyle = freeformStyle(frame);
            } else {
              positionStyle = flexItemStyle(frame, parent);
            }
          }
          // 父级是grid布局
          else if (isGrid(parent)) {
            if (frame.ignoreAutoLayout && frame.constraints) {
              positionStyle = freeformStyle(frame);
            } else {
              // girdItem是右对齐或者上对齐
              const gridItemAlignRB =
                (frame.gridItem as GridItem).justifySelf === CrossAlignment.End ||
                (frame.gridItem as GridItem).alignSelf === CrossAlignment.End;
              if (
                // girdItem移动过程中用相对位置
                canvasState.value.dragging.includes(frame.id) ||
                // girdItem改变尺寸过程中，并且对齐方式是上或右对齐时用相对位置(因为上或右对齐需要改变transform)
                (canvasState.value.resizeing.includes(frame.id) && gridItemAlignRB)
              ) {
                positionStyle = freeformStyle(frame);
              } else {
                positionStyle = gridItemStyle(frame, parent);
              }
            }
          }
          // 父级是自由布局
          else if (isFreeform(parent)) {
            // 1. 未设置栅格 --> 按自由布局解析(absolute)
            // 2. 设置了栅格
            //    1. 在拖拽或改变尺寸时，按自由布局解析(absolute)
            //    2. 否则
            //       1. 在栅格内，使用relative + grid-area解析
            //       2. 在栅格的边距或槽内，按自由布局解析(absolute)

            // 布局网格内的子元素按grid子元素
            if (isLayoutRowColumn(parent)) {
              // girdItem移动过程中用相对位置
              if (canvasState.value.dragging.includes(frame.id) || canvasState.value.resizeing.includes(frame.id)) {
                positionStyle = freeformStyle(frame);
              } else {
                // 在栅格内
                if (frame.gridItem) {
                  positionStyle = layoutGuideItemStyle(frame, parent);
                }
                // 在栅格的边距或槽内
                else {
                  positionStyle = freeformStyle(frame);
                }
              }
            } else {
              // 处理自由布局的位置
              positionStyle = freeformStyle(frame);
            }
          }
        }

        return { ...positionStyle, ...baseStyle };
      };
      return getFrameStyle(frame.value, parent.value);
    });

    const wrapStyle = computed(() => frameWrapStyle(frame.value));

    // 正常渲染的下级图形,不包含忽略自动布局的图形
    const children = computed(() => {
      return frame.value.children.filter((g) => !g.ignoreAutoLayout);
    });

    // 忽略自动布局的图形不受flex布局影响，放到与wrap平级
    const ignoreAutoGraphs = computed(() => {
      return frame.value.children.filter((g) => g.ignoreAutoLayout);
    });

    // frame为自动布局时，自动布局配置(包括宽高和limitSize)改变时，需要重新计算子元素的位置及大小
    watch(
      () => [frame.value.width, frame.value.height, frame.value.autoLayout, frame.value.limitSize],
      (newValue, oldValue) => {
        nextTick(() => {
          // 在画布中拖拽改变尺寸的图形在onResize事件里处理，这里不调用
          if (!canvasState.value.resizeing.includes(frame.value.id)) {
            const oldFrameWidth = oldValue[0] as number;
            const oldFrameHeight = oldValue[1] as number;
            setChildrenPosition(frame.value, oldFrameWidth, oldFrameHeight);
          }
        });
      },
      {
        deep: true
      }
    );

    //#region 网格布局

    const gridGhostStyle = computed(() => {
      let style = {};
      if (isGrid(frame.value)) {
        const { horizontalGap, verticalGap, padding, gridRowsSizing, gridColumnsSizing } = frame.value.autoLayout;

        const templateRows = gridRowsSizing.map((size) => (isNaN(parseInt(`${size}`)) ? '1fr' : `${size}px`));
        const templateColumns = gridColumnsSizing.map((size) => (isNaN(parseInt(`${size}`)) ? '1fr' : `${size}px`));
        style = {
          display: 'grid',
          gridTemplateRows: templateRows.join(' '),
          gridTemplateColumns: templateColumns.join(' '),
          columnGap: `${horizontalGap}px`,
          rowGap: `${verticalGap}px`,
          padding: padding.join('px ') + 'px',
          borderRadius: 'initial'
        };
      }
      return style;
    });

    /**
     * 计算格子序号
     * @param i
     * @param type
     * @returns
     */
    const getGridItemRowIndex = (i: number, type: 'row' | 'col') => {
      const { gridSize } = frame.value.autoLayout;
      if (type === 'row') {
        return Math.ceil(i / gridSize[1]);
      } else {
        return i % gridSize[1] !== 0 ? i % gridSize[1] : gridSize[1];
      }
    };

    /**
     * 当前可添加的格子
     * @param i
     * @returns
     */
    const isActiveGhostItem = (i: number) => {
      if (canvasState.value.frame?.id === frame.value.id && canvasState.value.gridRowCol) {
        const gridRowCol = canvasState.value.gridRowCol;
        const row = getGridItemRowIndex(i, 'row');
        const col = getGridItemRowIndex(i, 'col');
        if (activeGraph.value && activeGraph.value.gridItem) {
          const { rows, columns } = activeGraph.value.gridItem as GridItem;

          const rowSpan = rows[1] - rows[0];
          const colSpan = columns[1] - columns[0];

          return (
            gridRowCol[0] <= row &&
            row < gridRowCol[0] + rowSpan &&
            gridRowCol[1] <= col &&
            col < gridRowCol[1] + colSpan
          );
        } else {
          return gridRowCol[0] === row && gridRowCol[1] === col;
        }
      }
    };

    //#endregion

    //#region 布局网格

    const layoutGuides = computed(() => frame.value.layoutGuide);

    const { getFillStyle } = useFill();

    /**
     * 布局网格样式
     * @param layoutGuide
     */
    const layoutGuideStyle = (layoutGuide: LayoutGuide) => {
      let style: Records = {};
      const { type, width, offset, gutter, count, pattern, color } = layoutGuide;

      switch (type) {
        case LayoutGuideType.Row:
          {
            style = {
              display: 'grid',
              gridTemplateRows: `repeat(${count}, ${width === 'Auto' ? '1fr' : width + 'px'})`,
              rowGap: `${gutter}px`,
              padding: `${offset}px 0`
            };
            if (pattern !== PatternMode.Stretch) {
              style.alignContent = pattern;
            }
          }
          break;
        case LayoutGuideType.Column:
          style = {
            display: 'grid',
            gridTemplateColumns: `repeat(${count}, ${width === 'Auto' ? '1fr' : width + 'px'})`,
            columnGap: `${gutter}px`,
            padding: `0 ${offset}px`
          };
          if (pattern !== PatternMode.Stretch) {
            style.justifyContent = pattern;
          }
          break;
        case LayoutGuideType.Grid:
          {
            const lineColor = getFillStyle(color).backgroundColor;
            style = {
              background: `linear-gradient(-90deg, ${lineColor} 1px, transparent 1px) 0% 0% / ${count}px ${count}px, linear-gradient(0deg, ${lineColor} 1px, transparent 1px) 0% 0% / ${count}px ${count}px`
            };
          }
          break;
      }

      return style;
    };

    //#endregion

    return {
      frame,

      isEdit,
      inputRef,
      onClickName,

      className,
      frameStyle,
      wrapStyle,

      children,
      ignoreAutoGraphs,

      isGrid,
      gridGhostStyle,
      getGridItemRowIndex,
      isActiveGhostItem,

      getFillStyle,

      layoutGuides,
      LayoutGuideType,
      layoutGuideStyle
    };
  }
});
