<template>
  <div class="vis-form-inline">
    <div :class="`vis-form-inline__content--minus-${minusWidth}`">
      <div class="vis-form-field">
        <div class="vis-fill__content flex flex-nowrap">
          <q-btn flat dense @click="showPopup">
            <vis-svg-icon v-if="fixIconName" :icon="iconOpt.icon" style="width: 12px; height: 12px" />
            <q-img v-else-if="fixPicUrl" :src="imageUrl" style="width: 12px; height: 12px" />
            <ht-icon
              v-else
              :name="iconOpt.type === 'icon' ? 'hticon-vis-icon-style' : 'hticon-vis-navigation-pic'"
              class="vis-icon"
            />
          </q-btn>
          <q-separator vertical class="!m-0" />

          <div v-if="iconOpt.type === 'icon'" class="flex-1">
            <q-input
              :model-value="iconOpt.icon.name || '请选择图标'"
              @click="showIconPicker"
              borderless
              class="rounded-borders flex-1 px-2 cursor-pointer"
              input-class="cursor-pointer"
              placeholder="请输入"
              readonly
            />
            <vis-icon-picker ref="iconPickerRef" v-model="iconOpt.icon" class="flex-1"></vis-icon-picker>
          </div>
          <template v-else>
            <q-input
              v-model="inputUrl"
              @change="onSetUrl"
              borderless
              class="rounded-borders flex-1 px-2"
              placeholder="请输入图片地址"
            />
          </template>
          <template v-if="isStatus">
            <q-separator vertical class="!m-0" />
            <q-btn flat dense>
              <q-icon name="keyboard_arrow_down" class="vis-icon" />
              <q-menu v-model="showMenuType" style="width: 88px" class="vis-menu" dense>
                <q-list dense>
                  <q-item :active="iconOpt.type === 'icon'" @click="handleType('icon')" clickable>
                    <q-item-section>图标</q-item-section>
                  </q-item>
                  <q-item :active="iconOpt.type === 'picture'" @click="handleType('picture')" clickable>
                    <q-item-section>图片</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </div>
      </div>
    </div>
    <!-- 切换类型 -->
    <q-btn v-if="!isStatus" flat dense class="btn-field">
      <ht-icon class="vis-icon" name="vis-control"></ht-icon>
      <q-menu v-model="showMenuType" style="width: 88px" class="vis-menu" dense>
        <q-list dense>
          <q-item :active="iconOpt.type === 'icon'" @click="handleType('icon')" clickable>
            <q-item-section>图标</q-item-section>
          </q-item>
          <q-item :active="iconOpt.type === 'picture'" @click="handleType('picture')" clickable>
            <q-item-section>图片</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-btn>
  </div>
</template>

<script lang="ts" src="./icon.ts"></script>
