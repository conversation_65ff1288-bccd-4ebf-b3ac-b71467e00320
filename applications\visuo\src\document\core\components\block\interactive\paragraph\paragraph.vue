<template>
  <div :id="widget?.id" class="vis-paragraph-content fit position-relative" :style="style">
    <q-scroll-area v-if="isFixed" class="fit" :content-style="{ height: '100%' }">
      <div class="vis-text-content fit" :style="textStyle">
        <span v-html="paragraphData" @click="handleOpen"></span>
      </div>
    </q-scroll-area>
    <div v-else class="vis-text-content fit" :style="textStyle">
      <span v-html="paragraphData" :style="adaptStyle" @click="handleOpen"></span>
    </div>

    <vis-link-dialog v-if="isDialog && visible" :visible="visible" :link="link" @onClose="onClose"></vis-link-dialog>
  </div>
</template>

<script lang="ts" src="./paragraph.ts"></script>
<style lang="scss" src="./paragraph.scss"></style>
