/** 鼠标位置 */
export interface MenuPosition {
  x: number;
  y: number;
}

/** 操作项 */
type ActionKey = string;

/** 操作组 */
type MenuGroupItem = [string, ContextMenuType];

/** 菜单项 = 操作项 | 操作组 */
type MenuItem = ActionKey | MenuGroupItem;

/** 菜单数据 */
export type ContextMenuType = MenuItem[][];

/** 菜单配置项 */
export interface ContextMenuOptions {
  x: number;
  y: number;
  menus: ContextMenuType;
  action: (value: ActionKey) => void;
}

/** 菜单实例 */
export interface ContextMenuInstance {
  /** 鼠标位置 */ 
  position: MenuPosition;
  /**
   * 打开菜单
   * @param options 菜单选项
   * @param options.x 鼠标x坐标
   * @param options.y 鼠标y坐标
   * @param options.menus 菜单数据
   * @param options.action 选中回调
   *  */
  open: (options: ContextMenuOptions) => void;
  /** 关闭菜单 */ 
  close: () => void;
}

/** 二次弹窗配置项 */
export interface ContextMenuPopupOptions {
  title: string;
  message: string;
  confirm: () => void;
  cancel: () => void;
}

/** 菜单二次弹窗实例 */
export interface ContextMenuPopupInstance {
  /**
   * 打开确认弹窗
   * @param options 弹窗选项
   * @param options.title 标题
   * @param options.message 内容
   * @param options.confirm 确认回调
   * @param options.cancel 取消回调
   *  */
  open: (event: MouseEvent, options: any) => void;
  close: () => void;
}
