import { isBoolean, isNumber } from 'lodash-es';

/**
 * 填充
 * 用于文本颜色或背景填充
 * <AUTHOR>
 */
export class FillPaints {
  /** 填充类型 */
  type = FillType.Solid;
  /** 填充颜色 */
  color: Color = new Color();
  /** 是否可见 */
  visible: boolean = true;
  /** 渐变颜色 */
  stops?: ColorStop[];
  /** 旋转角度 */
  rotation?: number;
  /** 图片 */
  image?: Image;
  /** 透明度:颜色为渐变时启用 */
  opacity?: number;

  constructor(
    type?: FillType,
    color?: Color,
    opacity?: number,
    visible?: boolean,
    stops?: ColorStop[],
    rotation?: number
  ) {
    type && (this.type = type);
    color && (this.color = color);
    opacity && (this.opacity = opacity);
    isBoolean(visible) && (this.visible = visible);
    stops && (this.stops = stops);
    rotation && (this.rotation = rotation);
  }
}

export enum FillType {
  /** 纯色 */
  Solid = 'solid',
  /** 线性渐变 */
  Linear = 'gradient_linear',
  /** 径向渐变 */
  Radial = 'gradient_radial',
  /** 旋转渐变 */
  Angular = 'gradient_angular',
  /** 菱形渐变 */
  Diamond = 'gradient_diamond',
  /** 图片 */
  Image = 'image'
}

export class Color {
  /** 红色 */
  r: number = 0;
  /** 绿色 */
  g: number = 0;
  /** 蓝色 */
  b: number = 0;
  /** 透明度 */
  a: number = 1;
  constructor(r?: number, g?: number, b?: number, a?: number) {
    isNumber(r) && (this.r = r);
    isNumber(g) && (this.g = g);
    isNumber(b) && (this.b = b);
    isNumber(a) && (this.a = a);
  }
}

export class ColorStop {
  /** 位置 */
  position: number;
  /** 颜色 */
  color: Color;
  constructor(position: number, color: Color) {
    this.position = position;
    this.color = color;
  }
}

export class Image {
  /** 图片类型 file(资源库文件) | url(图片地址)*/
  type = ImageType.File;
  /** 图片id或地址 */
  url: string = '';
  /** 填充方式 cover(充满) | contain(适应) | repeat(平铺) | strech(拉伸)*/
  objectFit = ImageFit.Cover;
}

export enum ImageType {
  File = 'file',
  Url = 'url'
}

export enum ImageFit {
  Cover = 'cover',
  Contain = 'contain',
  Repeat = 'repeat',
  Strech = 'strech'
}

export const SystemPalette = [
  '#FFFFFF',
  '#E5E5E5',
  '#A6A6A6',
  '#808080',
  '#383838',
  '#000000',
  '#FF5733',
  '#D43030',
  '#E33C64',
  '#FFEB3B',
  '#FFC300',
  '#FF8D1A',
  '#FF8D1A',
  '#43CF7C',
  '#00BAAD',
  '#2A82E4',
  '#7948EA',
  '#AC33C1'
];

export enum ColorValueType {
  HEX = 'HEX',
  RGB = 'RGB',
  CSS = 'CSS',
  HSB = 'HSB',
  HSL = 'HSL'
}
