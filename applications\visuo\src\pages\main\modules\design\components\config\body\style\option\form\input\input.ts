import {
  Font,
  FormFix,
  InputQuery,
  InputQueryTypeOptions,
  InputType,
  Label,
  type InputOptions,
  FormRuleOptions,
  FormRuleType,
  FormRule,
  FillPaints,
  Stroke,
  Color,
  FillType
} from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';
import VisFormLabel from '../label/label.vue';
import VisFormFix from '../fix/fix.vue';
/**
 * 文本输入组件属性面板
 * @authot guohuizheng
 */
export default defineComponent({
  name: 'vis-config-input-option',
  components: {
    VisFormLabel,
    VisFormFix
  },
  props: {
    options: {
      type: Object as PropType<InputOptions>,
      required: true
    }
  },
  setup(props) {
    const computedOptions = computed(() => props.options);
    const typeOptions = [
      { label: '文本', value: InputType.Text },
      { label: '链接', value: InputType.Url },
      { label: '邮箱', value: InputType.Email },
      { label: '电话', value: InputType.Tel }
    ];

    const onTypeChange = (value: InputType) => {
      computedOptions.value.rules = computedOptions.value.rules.filter(
        (item) => ![FormRuleType.Url, FormRuleType.Email, FormRuleType.Tel].includes(item.type)
      );
      showMenuRule.value = false;

      if (value === InputType.Url) {
        computedOptions.value.rules.push(new FormRule(FormRuleType.Url));
      } else if (value === InputType.Email) {
        computedOptions.value.rules.push(new FormRule(FormRuleType.Email));
      } else if (value === InputType.Tel) {
        computedOptions.value.rules.push(new FormRule(FormRuleType.Tel));
      }
    };

    // #region 查询器
    const toggleQuery = () => {
      if (!computedOptions.value.query) {
        computedOptions.value.query = new InputQuery();
      } else {
        delete computedOptions.value.query;
      }
    };

    const handleLock = () => {
      if (!computedOptions.value.query) return;

      computedOptions.value.query.locked = !computedOptions.value.query.locked;
    };

    const handleQueryVisible = () => {
      if (!computedOptions.value.query) return;

      computedOptions.value.query.show = !computedOptions.value.query.show;
    };
    // #endregin

    const toggleFont = () => {
      if (!computedOptions.value.style.font) {
        Object.assign(computedOptions.value.style, {
          font: new Font(),
          background: new FillPaints(undefined, new Color(230, 225, 230, 0.1)),
          border: new Stroke([2, 2, 2, 2], undefined, new FillPaints(FillType.Solid, new Color(230, 225, 230))),
          radius: [4, 4, 4, 4]
        });
      } else {
        delete computedOptions.value.style.font;
        delete computedOptions.value.style.background;
        delete computedOptions.value.style.border;
        delete computedOptions.value.style.radius;
      }
    };

    const radius = ref(Array.isArray(computedOptions.value.style.radius) ? computedOptions.value.style.radius[0] : 4);

    const showRadius = ref(
      Array.isArray(computedOptions.value.style.radius)
        ? !computedOptions.value.style.radius.every((item) => item === computedOptions.value.style.radius![0])
        : false
    );

    const radiusChange = (val: number) => {
      computedOptions.value.style.radius = [val, val, val, val];
    };

    /**
     * 切换标签状态
     */
    const toggleLabel = () => {
      if (!computedOptions.value.label) {
        computedOptions.value.label = new Label(true, '文本输入');
      } else {
        computedOptions.value.label.show = !computedOptions.value.label.show;
      }
    };
    // #region 前后缀
    const showMenuFix = ref(false);
    /**
     * 添加前后缀
     * @param value
     */
    const addFix = (value: string) => {
      computedOptions.value.style[value as 'prefix' | 'suffix'] = new FormFix();
    };
    /**
     * 删除前后缀
     * @param value
     */
    const delFix = (value: string) => {
      delete computedOptions.value.style[value as 'prefix' | 'suffix'];
      showMenuFix.value = false;
    };

    // #endregin

    // #region 校验规则
    const showMenuRule = ref(false);

    const ruleOptions = FormRuleOptions.filter((item) =>
      [
        FormRuleType.Required,
        FormRuleType.Email,
        FormRuleType.Url,
        FormRuleType.Tel,
        FormRuleType.MinLength,
        FormRuleType.MaxLength
      ].includes(item.value)
    );

    const addRule = (value: FormRuleType) => {
      const ruleValue = [FormRuleType.MinLength, FormRuleType.MaxLength].includes(value) ? 0 : undefined;
      computedOptions.value.rules.push(new FormRule(value, ruleValue));
    };

    const delRule = (index: number) => {
      computedOptions.value.rules.splice(index, 1);
      showMenuRule.value = false;
    };

    const hasRules = (value: FormRuleType) => {
      return computedOptions.value.rules.some((item) => item.type === value);
    };

    // #endregion
    return {
      computedOptions,
      typeOptions,
      onTypeChange,

      toggleQuery,
      InputQueryTypeOptions,
      handleLock,
      handleQueryVisible,

      toggleFont,
      radius,
      showRadius,
      radiusChange,
      toggleLabel,

      showMenuFix,
      addFix,
      delFix,

      showMenuRule,
      ruleOptions,
      addRule,
      delRule,
      hasRules
    };
  }
});
