import { Aggregator, AxisField, WidgetType } from '../models';

/**
 * 组件配置数据
 * <AUTHOR>
 */
export class WidgetConfigDataFactory {
  /**
   * 段落
   */
  get [WidgetType.Paragraph]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        columns: {
          name: '内容',
          field: []
        }
      }
    };
  }

  /**
   * 标题
   */
  get [WidgetType.Title]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        columns: {
          name: '标题',
          field: []
        }
      }
    };
  }

  /**
   * 选项卡
   */
  get [WidgetType.Tab]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        columns: {
          name: '标签',
          required: true,
          field: [new AxisField('c9', '类别', undefined, 'dim', 'string')]
        },
        rows: {
          name: '值',
          required: true,
          field: [new AxisField('c13', '数量', Aggregator.SUM, 'measure', 'number')]
        }
      }
    };
  }

  /**
   * 文本输入
   */
  get [WidgetType.Input]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        input: {
          name: '文本输入',
          field: []
        }
      }
    };
  }


  /**
   * 柱状图
   */
  get [WidgetType.Bar]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        xField: {
          name: 'X 轴',
          field: []
        },
        yField: {
          name: 'Y 轴',
          field: []
        }
      }
    };
  }

  /**
   * 获取组件配置
   * @param widgetType 组件类型
   * @returns 组件配置
   */
  get(widgetType: WidgetType) {
    return this[widgetType];
  }
}
