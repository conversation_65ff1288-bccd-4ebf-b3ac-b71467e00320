/**
 * 组件一级分组
 */
export enum WidgetGroup {
  /** 文本 */
  Words = 'words',
  /** 容器 */
  // Container = 'container',
  /** 图表 */
  Chart = 'chart'
}

/**
 * 组件二级分组
 */
export enum WidgetSubGroup {
  /** 文本 */
  Text = 'text',
  /** 列表 */
  List = 'list',
  /** 交互 */
  Interactive = 'interactive',
  /** 柱状图 */
  Bar = 'bar',
  /** 饼图 */
  Pie = 'pie',
  /** 折线图 */
  Line = 'line',
  /** 散点图 */
  Scatter = 'scatter',
  /** 表单 */
  Form = 'form'
}

/**
 * 组件类型
 */
export enum WidgetType {
  //#region 文本类
  /** 段落 */
  Paragraph = 'paragraph',
  /** 标题 */
  Title = 'title',
  /** 选项卡 */
  Tab = 'tab',
  //#endregion

  // #region 表单类
  Input = 'input',
  // #endregion

  // #region 图表类
  Bar = 'bar'
  // #endregion
}

/**
 * 组件名称
 */
export enum WidgetName {
  Paragraph = '段落',
  Title = '标题',
  Tab = '选项卡',
  Input = '文本输入',
  Bar = '柱状图'
}
