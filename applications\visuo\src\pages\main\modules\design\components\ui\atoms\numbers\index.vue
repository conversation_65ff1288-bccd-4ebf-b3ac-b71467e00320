<template>
  <div class="vis-numbers rounded-borders flex-1">
    <div class="vis-numbers-inline flex gap-2 mb-2">
      <!-- 俩值 -->
      <vis-number
        class="flex-1"
        v-model="values[0]"
        @update:model-value="radiusChange(0)"
        :min="0"
        :icon="!isMulti ? iconArr[0] : iconArr[3]"
      ></vis-number>
      <vis-number
        class="flex-1"
        v-model="values[1]"
        @update:model-value="radiusChange(1)"
        :min="0"
        :icon="!isMulti ? iconArr[1] : iconArr[4]"
      ></vis-number>
      <q-btn :class="{ active: isMulti }" @click="toggleMulti">
        <ht-icon class="vis-icon" :name="iconArr[2] || 'hticon-vis-padding-round'" />
        <q-tooltip> 单独设置 </q-tooltip>
      </q-btn>
    </div>

    <!-- 四值 -->
    <div v-if="isMulti" class="vis-numbers-inline flex gap-2 mb-2 w-[calc(100%-32px)]">
      <vis-number class="flex-1" v-model="values[3]" :min="0" :icon="iconArr[6]"></vis-number>
      <vis-number class="flex-1" v-model="values[2]" :min="0" :icon="iconArr[5]"></vis-number>
    </div>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
