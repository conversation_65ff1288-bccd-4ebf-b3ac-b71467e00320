import { isNumber } from 'lodash-es';
/**
 * 聚合函数
 */
export class AggregatorFunction {
  /** 标准语法函数名 */
  function = '';
  /** 函数中文名称 */
  name = '';
  /** 函数简写 */
  shortFunction = '';
  /** 排序值 */
  order = 0;
  /** 是否支持该函数 1-true 0-false */
  support = 1;

  constructor(
    func?: string,
    name?: string,
    shortFunction?: string,
    order?: number,
    support?: number
  ) {
    func && (this.function = func);
    name && (this.name = name);
    shortFunction && (this.shortFunction = shortFunction);
    isNumber(order) && (this.order = order);
    isNumber(support) && (this.support = support);
  }
}
