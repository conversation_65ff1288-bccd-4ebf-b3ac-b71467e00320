import { computed, defineComponent, ref, type PropType } from 'vue';
import { SeniorFont, TextEffects, useFill } from '@vis/document-core';

/**
 * 文本弹出框
 */
export default defineComponent({
  name: 'vis-text-more',
  props: {
    /**
     * 文本配置
     */
    text: {
      type: Object as PropType<SeniorFont>,
      required: true
    },
    textEffects: {
      type: Object as PropType<TextEffects>
    }
  },

  setup(props) {
    const { getFillStyle } = useFill();

    const tab = ref('text');

    const textPopupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      textPopupRef.value?.handleShow(e);
    };

    const textColor = computed(() => {
      const fill = getFillStyle(props.text.color, true, true);

      return fill.color
        ? fill
        : {
            ...fill,
            color: fill.backgroundImage === 'url()' ? 'black' : 'transparent',
            backgroundClip: 'text',
            webkitBackgroundClip: 'text'
          };
    });

    return {
      tab,
      textColor,
      textPopupRef,
      popupShow,
      showPopup
    };
  }
});
