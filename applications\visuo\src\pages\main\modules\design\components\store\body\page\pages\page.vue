<template>
  <div class="vis-store-page-container h-80" :style="{ height: `${pagesHeight}px` }">
    <!-- 头部 -->
    <div class="vis-store-page-container-header" v-show="!isSearchHide">
      <span class="vis-store-page-container-title">页面</span>
      <div class="vis-store-page-container-btn-group">
        <q-btn class="vis-store-page-container-btn" flat icon="o_search" size="8px" @click="onUpdateSearchHide">
          <q-tooltip> 搜索 </q-tooltip>
        </q-btn>
        <q-btn flat class="vis-store-page-container-btn" @click="onAdd('page')">
          <ht-icon class="vis-icon" name="hticon-vis-add"></ht-icon>
          <q-tooltip> 添加页面 </q-tooltip>
        </q-btn>
        <q-btn flat class="vis-store-page-container-btn" @click="onAdd('group')" :disabled="isDisabledGroup">
          <ht-icon class="vis-icon" name="hticon-vis-group-a"></ht-icon>
          <q-tooltip> 添加页面分类 </q-tooltip>
        </q-btn>
        <q-btn class="vis-store-page-container-btn" flat size="8px" @click="onUpdateExpandAllHide">
          <ht-icon
            class="vis-icon"
            :name="isExpandHide ? 'hticon-vis-resize-fill-y' : 'hticon-vis-resize-adapt-y'"
          ></ht-icon>
          <q-tooltip> {{ isExpandHide ? '折叠' : '展开' }} </q-tooltip>
        </q-btn>
      </div>
    </div>
    <!-- 筛选框 -->
    <div class="vis-store-page-container-header" v-show="isSearchHide">
      <div class="vis-store-page-container-header-search">
        <q-input
          class="vis-store-page-container-header-input"
          ref="filterRef"
          clearable
          rounded
          dense
          borderless
          placeholder="搜索页面..."
          v-model="keyWord"
          @update:model-value="onKeyWordChange"
          @blur="onUpdateSearchHide"
          @clear="filterRef?.focus()"
        >
          <template #prepend>
            <q-icon name="search" flat size="16px" />
          </template>
        </q-input>
      </div>
    </div>
    <!-- 拖拽树 -->
    <q-scroll-area ref="scrollAreaRef" class="h-[calc(100%-28px)] pl-1 pr-1">
      <div
        :class="['vis-store-page-container-tree_layout', { 'is-dragging': isDragging }]"
        v-click-outside="onClickOutside"
      >
        <div v-if="!pages.length && isSearchHide && keyWord" class="empty-content">没有找到匹配的结果</div>
        <vis-page-item
          v-else
          :nodes="pages"
          @drag-end="onDragEnd"
          @drag-add="onDragAdd"
          @drag-start="onDragStart"
          @drag-move="onCheckMove"
          :drag-target-parent-id="dragTargetParentId"
          :selected-ids="selectedIds"
          @update:selected-ids="onUpdateSelected"
          :expanded-map="expandedMap"
          @update:expanded-map="onUpdateExpandHide"
          :is-editing-status="isEditingStatus"
          :is-search-hide="isSearchHide"
          @context-menu="onOpenContextMenu"
        />
      </div>
    </q-scroll-area>
  </div>
</template>

<script lang="ts" src="./page.ts"></script>
<style lang="scss" scoped src="./page.scss"></style>
