import { Page, Group, GraphType, useDocumentStore } from '@vis/document-core';
import { defineComponent, ref, nextTick, watchEffect, computed } from 'vue';
import type { PropType } from 'vue';
import { VIS_DESIGN_INFINITE_CANVAS } from '../../../../../../models';
import draggable from 'vuedraggable';
import { useDesignStore } from '../../../../../../stores';

/**
 * 页面树
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-page-item',
  components: { draggable },
  props: {
    nodes: {
      type: Array as PropType<(Group | Page)[]>,
      required: true
    },
    selectedIds: {
      type: Array as PropType<string[]>,
      required: true
    },
    expandedMap: {
      type: Map,
      required: true,
      default: () => new Map<string, boolean>()
    },
    itemKey: {
      type: String,
      default: VIS_DESIGN_INFINITE_CANVAS
    },
    dragTargetParentId: {
      type: String,
      default: null
    },
    isSearchHide: {
      type: Boolean,
      default: false
    },
    isEditingStatus: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'update:selected-ids',
    'update:expanded-map',
    'drag-start',
    'drag-end',
    'drag-add',
    'drag-move',
    'context-menu'
  ],
  setup(props, { emit }) {
    const docStore = useDocumentStore();
    const pagesComp = computed(() => docStore.document.value.children);

    const designStore = useDesignStore();
    const activePage = computed(() => designStore.active.value.page);
    const editingGraph = computed(() => designStore.editingGraph.value);

    // #region 双击编辑
    const editingName = ref<string>('');
    const editInputRef = ref<any>(null);

    const isEditing = (node: Page | Group) => {
      return editingGraph.value && editingGraph.value.id === node.id;
    };

    const handleEdit = (node: Page | Group) => {
      editingName.value = node.name;
      nextTick(() => {
        editInputRef.value?.focus();
        editInputRef.value?.select();
      });
    };

    const onDoubleClick = (node: Page | Group, e: Event) => {
      e.stopPropagation();
      designStore.setEditingGraph(node);
      handleEdit(node);
    };

    const onSaveName = (node: Page | Group) => {
      const name = editingName.value.trim();
      if (name && editingName.value !== node.name) {
        const nodeIds = node.type === GraphType.Group ? [...(node as Group).children.map((p) => p.id)] : [node.id];
        // 重命名
        pagesComp.value.forEach((page) => {
          if (nodeIds.includes(page.id)) {
            if (node.type === GraphType.Page) {
              page.name = name;
            } else if (node.type === GraphType.Group) {
              page.group = name;
            }
          }
        });
      }
      onCancelEdit();
    };

    const onCancelEdit = () => {
      designStore.setEditingGraph(null);
      editingName.value = '';
    };

    watchEffect(() => {
      editingGraph.value && setTimeout(() => handleEdit(<Page | Group>editingGraph.value), 10);
    });
    // #endregion

    // #region 展开收起
    const isExpanded = (node: Group) => {
      return props.expandedMap.get(node.id) || false;
    };
    const toggleExpand = (node: Group, e: Event) => {
      if (node.children && node.children.length > 0) {
        emit('update:expanded-map', node);
      }
    };
    const onEmitExpanded = (node: Group) => {
      emit('update:expanded-map', node);
    };
    // #endregion

    // #region 拖拽
    const onDragStart = (evt: CustomEvent) => {
      emit('drag-start', evt);
    };

    const onDragEnd = (evt: CustomEvent) => {
      emit('drag-end', evt);
    };

    const onDragAdd = (evt: CustomEvent) => {
      emit('drag-add', evt);
    };

    const onCheckMove = (evt: any) => {
      emit('drag-move', evt);
      // 阻止编组嵌套拖拽
      if (evt.from && evt.to && evt.from !== evt.to && evt.draggedContext.element.type === GraphType.Group) {
        return false;
      }
    };

    const isDragTargetParent = (node: Group) => {
      return props.dragTargetParentId === node.id;
    };
    // #endregion

    // #region 选中高亮 - 支持多选
    const isActivated = (node: Page | Group) => activePage.value.id === node.id;
    const isSelected = (node: Page | Group) => props.selectedIds.includes(node.id);
    const onSelect = (node: Page | Group, $event: Event) => {
      emit('update:selected-ids', node, $event);
    };
    // 转发id
    const onEmitSelected = (node: Page | Group, $event: Event) => {
      emit('update:selected-ids', node, $event);
    };
    // #endregion

    // #region 右键菜单
    const onOpenContextMenu = (node: Page | Group, $event: Event) => {
      emit('context-menu', node, $event);
    };

    // #endregion

    // #region 主页面
    const isHomePage = (node: Page | Group) => {
      return docStore.document.value.home === node.id;
    };
    // #endregion

    return {
      onDragStart,
      onDragEnd,
      onDragAdd,
      onCheckMove,
      isExpanded,
      toggleExpand,
      onEmitExpanded,
      isActivated,
      isSelected,
      onSelect,
      onEmitSelected,
      isEditing,
      editingName,
      editInputRef,
      onDoubleClick,
      onSaveName,
      onCancelEdit,
      isDragTargetParent,

      onOpenContextMenu,

      isHomePage
    };
  }
});
