import {
  DirectionType,
  TabResizeType,
  SelectMode,
  IconOption,
  TabItemStyle,
  StrokeType,
  TabOptions,
  iconPositionOptions,
  Carousel,
  FontConfig
} from '@vis/document-core';
import { computed, defineComponent, onMounted, ref, watch, type PropType, type Ref } from 'vue';
import VisAspect from '../../property/aspect/index.vue';
import VisConfigTabStatus from './status/status.vue';
import VisTabIconSelector from './icon/icon.vue';

/**
 * <AUTHOR>
 * 选项卡组件属性面板
 */
export default defineComponent({
  name: 'vis-config-tab-option',
  components: {
    VisAspect,
    VisConfigTabStatus,
    VisTabIconSelector
  },
  props: {
    options: {
      type: Object as PropType<TabOptions>,
      required: true
    }
  },
  setup(props) {
    const selectMode = ref(SelectMode.Single);

    const hoverItem = ref('text');

    const activeItem = ref('text');
    const tabOptions = computed(() => {
      return props.options;
    });
    const tabStyle = computed(() => props.options.style);

    const popupShow = ref(false);

    const { fontSizes } = new FontConfig();

    const selectOptions = [
      {
        value: SelectMode.Single,
        label: '单选'
      },
      {
        value: SelectMode.Multiple,
        label: '多选'
      }
    ];

    const overflowOptions = [
      {
        value: 0,
        label: '截断'
      },
      {
        value: 1,
        label: '省略号'
      },
      {
        value: 2,
        label: '换行'
      },
      {
        value: 3,
        label: '跑马灯'
      }
    ];

    const directionOptions = [
      {
        value: DirectionType.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Horizontal}`,
        tip: '水平'
      },
      { value: DirectionType.Vertical, label: '', icon: `hticon-vis-layout-${DirectionType.Vertical}`, tip: '垂直' },
      { value: DirectionType.Grid, label: '', icon: `hticon-vis-layout-${DirectionType.Grid}`, tip: '网格' }
    ];

    /**
     * 修改选择模式
     * @param val
     */
    const onChangeSelectMode = (val: SelectMode) => {
      tabOptions.value.multiple = val === SelectMode.Multiple ? true : false;
    };

    watch(
      () => props.options.multiple,
      (val) => {
        selectMode.value = val ? SelectMode.Multiple : SelectMode.Single;
      }
    );

    //#region  布局
    const popupRef = ref();
    const showMenuWidth = ref(false);
    const showMenuHeight = ref(false);

    const onShowPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    const onChangeResize = (type: 'resizeX' | 'resizeY', resize: TabResizeType) => {
      tabOptions.value[type] = resize;
    };

    const hoverGrid = ref([0, 0]);
    const onMouseMovePicker = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.className.indexOf('hand') !== -1) {
        const row = target.getAttribute('data-row');
        const col = target.getAttribute('data-col');
        hoverGrid.value = [Number(row), Number(col)];
      }
    };

    const onMouseLeavePicker = () => {
      hoverGrid.value = [0, 0];
    };

    const onClickGrid = (row: number, col: number) => {
      tabOptions.value.layout.row = row;
      tabOptions.value.layout.column = col;
    };

    /**
     * 行距显示条件
     */
    const getShowLineGutter = computed(
      () =>
        tabOptions.value.layout.direction === DirectionType.Vertical ||
        (tabOptions.value.layout.flowWrap &&
          tabOptions.value.layout.direction === DirectionType.Horizontal &&
          tabOptions.value.resizeX === TabResizeType.Fixed)
    );
    //#endregion 布局

    //#region --------- 圆角 ---------------

    const isMixed = computed(
      () => !tabOptions.value.style.radius.every((item) => item === tabOptions.value.style.radius[0])
    );

    const isLeftMixed = computed(() => tabOptions.value.style.radius[0] !== tabOptions.value.style.radius[3]);

    const isRightMixed = computed(() => tabOptions.value.style.radius[1] !== tabOptions.value.style.radius[2]);

    const allRadius: Ref<number | string> = ref(isMixed.value ? 'Mixed' : props.options.style.radius[0]);
    const leftRadius: Ref<number | string> = ref(isLeftMixed.value ? 'Mixed' : props.options.style.radius[0]);
    const rightRadius: Ref<number | string> = ref(isRightMixed.value ? 'Mixed' : props.options.style.radius[1]);

    const showRadius = ref(false);

    const isAllRadius = computed(() => {
      // 垂直布局或 水平布局时只有宽度为自适应 或者 宽度为固定宽度换行未开启的状态下才显示
      return (
        tabOptions.value.layout.direction === DirectionType.Vertical ||
        (tabOptions.value.layout.direction === DirectionType.Horizontal &&
          (tabOptions.value.resizeX === TabResizeType.Adapt ||
            (!tabOptions.value.layout.flowWrap && tabOptions.value.resizeX === TabResizeType.Fixed)))
      );
    });

    const radiusChange = (val: number, position: string) => {
      if (position === 'all') {
        if (typeof allRadius.value === 'number') {
          tabOptions.value.style.radius = [val, val, val, val];
        }
      } else {
        if (position === 'left') {
          if (typeof leftRadius.value === 'number') {
            tabOptions.value.style.radius[0] = val;
            tabOptions.value.style.radius[3] = val;
          }
        } else {
          if (typeof rightRadius.value === 'number') {
            tabOptions.value.style.radius[1] = val;
            tabOptions.value.style.radius[2] = val;
          }
        }
      }
    };

    watch(
      () => tabOptions.value.style.radius,
      (val) => {
        allRadius.value = isMixed.value ? 'Mixed' : val[0];
        // leftRadius.value = isLeftMixed.value ? Math.max(val[0], val[3]) : val[0];
        // rightRadius.value = isRightMixed.value ? Math.max(val[1], val[2]) : val[1];
      },
      {
        deep: true
      }
    );

    watch(
      () => isMixed.value,
      (val) => {
        allRadius.value = val ? 'Mixed' : tabOptions.value.style.radius[0];
      }
    );

    watch(
      () => showRadius.value,
      (val) => {
        if (!val) {
          if (!isAllRadius.value) {
            leftRadius.value = isLeftMixed.value ? 'Mixed' : tabOptions.value.style.radius[0];
            rightRadius.value = isRightMixed.value ? 'Mixed' : tabOptions.value.style.radius[1];
          }
        }
      }
    );

    watch(
      () => tabOptions.value.layout.direction,
      () => {
        if (isAllRadius.value) {
          showRadius.value = allRadius.value === 'Mixed';
        }
      }
    );

    watch(
      () => isAllRadius.value,
      (val) => {
        if (val) {
          // 单个圆角设置
          showRadius.value = isMixed.value;
        } else {
          showRadius.value = isLeftMixed.value || isRightMixed.value;
        }
      },
      {
        immediate: true
      }
    );

    //#endregion --------- 圆角 ---------------

    const toggle = (val: 'icon' | 'carousel') => {
      if (tabOptions.value[val]) {
        tabOptions.value[val] = undefined;
      } else {
        val === 'icon' && (tabOptions.value[val] = new IconOption());
        val === 'carousel' && (tabOptions.value[val] = new Carousel());
      }
    };

    //#region 图标
    const onAspectRatio = () => {
      tabOptions.value.icon!.aspectRatio = !tabOptions.value.icon!.aspectRatio;
    };

    //#endregion 图标

    //#region ---------------- 状态 ----------------------

    const addStatus = (status: 'hover' | 'active') => {
      tabStyle.value[status] = new TabItemStyle();
    };

    const deleteStatus = (status: 'hover' | 'active') => {
      if (tabStyle.value[status]) {
        tabStyle.value[status] = undefined;
      }
    };

    //#endregion ---------------- 状态 ----------------------

    onMounted(() => {
      selectMode.value = tabOptions.value.multiple ? SelectMode.Multiple : SelectMode.Single;
    });

    return {
      selectMode,
      tabOptions,
      tabStyle,
      selectOptions,
      overflowOptions,
      hoverItem,
      activeItem,
      TabResizeType,
      showMenuWidth,
      showMenuHeight,
      allRadius,
      fontSizes,
      leftRadius,
      rightRadius,
      isAllRadius,
      showRadius,
      directionOptions,
      DirectionType,
      hoverGrid,
      getShowLineGutter,
      iconPositionOptions,
      popupRef,
      popupShow,
      onChangeSelectMode,
      onChangeResize,
      radiusChange,
      toggle,
      addStatus,
      deleteStatus,
      onMouseMovePicker,
      onMouseLeavePicker,
      onClickGrid,
      onShowPopup,
      onAspectRatio
    };
  }
});
