import { defineComponent, ref, watch } from 'vue';
/**
 * 多值输入框
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-numbers',
  props: {
    modelValue: {
      type: Array<number>,
      required: true
    },
    /**
     * 多值类型，根据此类型设置默认图标
     * margin:边距 padding:内边距 border:边框 radius:圆角
     */
    type: {
      type: String
    },
    /**
     * 自定义图标数组，其中全部图标用于切换双值和四值设置
     * 数组顺序：[上下, 左右, 全部, 上, 右, 下, 左]
     */
    icons: {
      type: Array<string>
    }
  },
  setup(props, { emit }) {
    const iconArr = ref<string[]>([]);
    if (props.icons) {
      iconArr.value = props.icons;
    } else if (props.type) {
      switch (props.type) {
        // 出了新图标可以在此替换
        case 'margin':
        case 'padding':
          iconArr.value = [
            'hticon-vis-padding-y',
            'hticon-vis-padding-x',
            'hticon-vis-padding-round',
            'hticon-vis-padding-top',
            'hticon-vis-padding-right',
            'hticon-vis-padding-bottom',
            'hticon-vis-padding-left'
          ];
          break;
        case 'border':
          iconArr.value = [];
          break;
        case 'radius':
          iconArr.value = [
            'hticon-vis-radius-tb',
            'hticon-vis-radius-lr',
            'hticon-vis-radius-s',
            'hticon-vis-radius-lt',
            'hticon-vis-radius-rt',
            'hticon-vis-radius-rb',
            'hticon-vis-radius-lb'
          ];
          break;
        default:
          break;
      }
    }
    const isMulti = ref(false);
    const toggleMulti = () => {
      isMulti.value = !isMulti.value;
      if (!isMulti.value) {
        values.value[2] = values.value[0];
        values.value[3] = values.value[1];
      }
    };
    const values = ref<number[]>(new Array(4).fill(undefined));

    const initData = () => {
      if (props.modelValue[2] === props.modelValue[0] && props.modelValue[3] === props.modelValue[1]) {
        isMulti.value = false;
      } else {
        isMulti.value = true;
      }
      values.value = props.modelValue;
    };
    initData();

    watch(
      () => values.value,
      () => {
        emit('update:modelValue', values.value);
        emit('change', values.value);
      },
      { deep: true }
    );

    const radiusChange = (index: number) => {
      if (!isMulti.value) {
        if (index === 0) {
          values.value[2] = values.value[0];
        }
        if (index === 1) {
          values.value[3] = values.value[1];
        }
      }
    };

    return {
      iconArr,
      isMulti,
      toggleMulti,
      values,

      radiusChange
    };
  }
});
