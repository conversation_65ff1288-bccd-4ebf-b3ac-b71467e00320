import { defineStore } from '@hetu/core';
import { computed, ref } from 'vue';
import { Document, Frame, IconConfig, Page } from '../models';
import type { Records } from '@hetu/util';

/**
 * 文档存储store
 * <AUTHOR>
 */
export const useDocumentStore = defineStore(() => {
  const document = ref<Document>(new Document());

  const staticDatas = computed({
    get() {
      return document.value.staticData;
    },
    set(value) {
      document.value.staticData = value;
    }
  });

  const staticDataSheets = computed(() => Object.keys(staticDatas.value));

  // 主容器
  const mainFrame = computed(() => {
    const homePage = document.value.children.find((p) => p.id === document.value.home) as Page;

    return homePage.children.find((f) => f.id === homePage.main) as Frame;
  });

  /**
   * 组件相关状态
   * key: widget.id
   */
  const widgetStore = ref<
    Records<{
      /** 组件加载的动态数据 */
      dynamicData: any;
      /**  组件渲染完成状态 */
      ready: boolean | undefined;
      /** 存储组件配置中的变量，用于监听变量变化时做对比  */
      vars: string[];
    }>
  >({});

  const fontFamilys = ref<string[] | { name: string }[]>([]);

  const mode = ref<'design' | 'preview' | 'share'>('design');

  const isDesignMode = computed(() => mode.value === 'design');
  const isPreviewMode = computed(() => mode.value === 'preview');
  const isShareMode = computed(() => mode.value === 'share');

  // 图标
  const iconConfigs = ref<IconConfig[]>([]);

  const reset = () => {
    document.value = new Document();
    mode.value = 'design';
  };

  return {
    document,
    staticDatas,
    staticDataSheets,
    mainFrame,
    widgetStore,

    /** 字体配置 */
    fontFamilys,

    mode,
    isDesignMode,
    isPreviewMode,
    isShareMode,

    iconConfigs,

    reset
  };
});
