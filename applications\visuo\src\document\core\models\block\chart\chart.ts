import { WidgetBlock } from '../widget-block';
import { Label } from './label';
import { Title } from './title';

/**
 * 图表
 * <AUTHOR>
 */
export class Chart extends WidgetBlock {
  /**
   * 图表配置
   */
  options: ChartOptions = new ChartOptions();
}

/**
 * 图表配置
 * <AUTHOR>
 */
export class ChartOptions {
  /** 图表图例 */

  /** 图表提示 */

  /** 图表工具 */

  /** 图表标签 */
  label?: Label;

  /** 图表标题 */
  title?: Title;

  /** 图表主区域 */

  /** 图表滚动条 */

  /** 图表缩略轴 */
}
