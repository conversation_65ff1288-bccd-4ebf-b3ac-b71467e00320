import type { Block } from '../block';
import { Graph, GraphType } from '../graph-basic';
import { Color, Effects, FillPaints, FillType, Stroke } from '../ui';
import { AutoLayout, Constraints, LayoutGuide } from './layout';

/**
 * 容器
 * <AUTHOR>
 */
export class Frame extends Graph {
  type = GraphType.Frame;

  name = '容器';

  /** 填充 */
  fillPaints = [new FillPaints(FillType.Solid, new Color(255, 255, 255, 1))];

  /** 自动布局 */
  autoLayout = new AutoLayout();

  /** 布局网格 */
  layoutGuide: LayoutGuide[] = [];

  /** 剪裁超出内容 */
  clip = true;

  children: Array<Frame | Block> = [];
}
