import { isBoolean } from 'lodash-es';
import { DirectionType } from '../../frame';
import {
  Color,
  ColorStop,
  Effects,
  FillPaints,
  FillType,
  Font,
  Image,
  Stroke,
  Text,
  TextAlign,
  SeniorFont,
  TextEffects,
  VerticalAlign
} from '../../ui';
import { WidgetName, WidgetType } from '../config/widget-enum';
import { Icon } from '../form';
import { WidgetBlock } from '../widget-block';

/**
 * 选项卡
 * <AUTHOR>
 */
export class Tab extends WidgetBlock {
  type = WidgetType.Tab;

  name = WidgetName.Tab;

  options: TabOptions = new TabOptions();
}

/**
 * 选项卡配置项
 */
export class TabOptions {
  /**
   * 是否多选
   */
  multiple: boolean = false;

  /**
   * 选中项自动定位
   */
  autoPosition = false;

  /**
   * 列宽模式
   */
  resizeX = TabResizeType.Adapt;

  /**
   * 行高模式
   */
  resizeY = TabResizeType.Adapt;
  /**
   * 选项卡列宽
   */
  width: number = 200;

  /**
   * 选项卡行高
   */
  height: number = 50;

  /**
   * 默认选中项
   */
  defaultIndex: string = '1';

  /**
   * 选项卡排版布局
   */
  layout: Layout = new Layout();

  /**
   * 图标
   */
  icon?: IconOption;

  style: TabStyle = new TabStyle();

  /**
   * 选项卡轮播配置
   */
  carousel?: Carousel;

  constructor(
    multiple?: boolean,
    defaultIndex?: string,
    autoPosition?: boolean,
    resizeX?: TabResizeType,
    resizeY?: TabResizeType,
    carousel?: Carousel
  ) {
    isBoolean(multiple) && (this.multiple = multiple);
    defaultIndex && (this.defaultIndex = defaultIndex);
    isBoolean(autoPosition) && (this.autoPosition = autoPosition);
    resizeX && (this.resizeX = resizeX);
    resizeY && (this.resizeY = resizeY);
    carousel && (this.carousel = carousel);
  }
}

/**
 * 选项卡数据
 */
export type TabData = {
  columns: string;
  rows: string | number;
};

/**
 * 布局
 */
export class Layout {
  /**
   * 布局方向
   */
  direction: DirectionType = DirectionType.Horizontal;

  /**
   * 是否自动换行
   */
  flowWrap = false;

  /**
   * 列数
   */
  column: number = 4;
  /**
   * 行数
   */
  row: number = 2;
  /**
   * 选项卡间距
   * 行距  列距
   */
  gutter: [number, number] = [0, 0];

  /**
   * 滚动条
   */
  scrollbar = false;
}

export enum IconPosition {
  Top = 'top',
  Left = 'left',
  Bottom = 'bottom',
  Right = 'right'
}

/**
 * 图标配置
 */
export class IconOption {
  visible = false;

  width = 20;

  height = 20;

  position = IconPosition.Left;

  type: string = 'picture';
  /**
   * 图片
   */
  image?: Image;

  icon: Icon = new Icon();

  /**
   * 是否锁定纵横比
   */
  aspectRatio = true;
  /**
   * 图标与文字间距离
   */
  gutter: number = 0;
}

/**
 * 轮播
 */
export class Carousel {
  /**
   * 间隔时长
   */
  interval: number = 5;
  /**
   * 用户点击停留时长
   */
  clickTime: number = 20;
}

/**
 * 选项卡样式配置
 */
export class TabStyle {
  /** 文字方向 */
  // textDirection: string = 'horizontal';

  /**
   * 文本溢出效果
   *  0溢出  1省略号  2换行 3跑马灯
   */
  overflow: number = 0;

  /**
   * 滚动速度
   * 只有跑马灯效果的时候可配置
   */
  scrollSpeed = 1;

  /**
   * 字体样式
   */
  font: SeniorFont = new SeniorFont(
    14,
    700,
    undefined,
    new FillPaints(FillType.Solid, new Color(95, 95, 110)),
    TextAlign.Center,
    VerticalAlign.Center
  );

  /**
   * 文本阴影
   */
  textEffects: TextEffects = new TextEffects(false, undefined, new Color(0, 0, 0, 1));

  /**
   * 背景样式
   */
  background?: FillPaints = new FillPaints(FillType.Solid, new Color(228, 233, 239));

  /**
   * 描边
   */
  border?: Stroke = new Stroke([2, 2, 2, 2], undefined, new FillPaints(FillType.Solid, new Color(217, 225, 235)));

  /**
   * 圆角
   */
  radius = [0, 0, 0, 0];

  /**
   * 透明度
   */
  opacity = 100;

  /**
   * 阴影
   */
  shadow: Effects = new Effects(false);

  /**
   * 悬浮样式
   */
  hover?: TabItemStyle;

  /**
   * 激活样式
   */
  active?: TabItemStyle = new TabItemStyle(
    new SeniorFont(
      14,
      700,
      undefined,
      new FillPaints(FillType.Solid, new Color(33, 141, 244)),
      TextAlign.Center,
      VerticalAlign.Center
    ),
    new FillPaints(
      FillType.Linear,
      undefined,
      undefined,
      undefined,
      [
        new ColorStop(0, new Color(136, 186, 234, 1)),
        new ColorStop(1, new Color(255, 255, 255, 0)),
        new ColorStop(0.5982, new Color(170, 201, 230, 1)),
        new ColorStop(0.99, new Color(174, 203, 231, 1))
      ],
      180
    ),
    new Stroke([2, 2, 2, 2], undefined, new FillPaints(FillType.Solid, new Color(66, 154, 238)))
  );
}

/**
 * 每个选项样式
 */
export class TabItemStyle {
  /**
   * 样式显隐
   */
  visible = true;
  /**
   * 字体样式
   */
  font: SeniorFont = new SeniorFont(undefined, undefined, undefined, undefined, TextAlign.Center, VerticalAlign.Center);

  /**
   * 文本特效
   */
  textEffects: TextEffects = new TextEffects();

  /**
   * 背景样式
   */
  background: FillPaints = new FillPaints();

  /**
   * 描边
   */
  border: Stroke = new Stroke();

  /**
   * 图标
   */
  icon: IconOption = new IconOption();

  /**
   * 阴影
   */
  shadow: Effects = new Effects();

  constructor(font?: SeniorFont, background?: FillPaints, border?: Stroke, shadow?: Effects) {
    font && (this.font = font);
    background && (this.background = background);
    border && (this.border = border);
    shadow && (this.shadow = shadow);
  }
}

/**
 * 选择模式
 */
export enum SelectMode {
  /** 单选 */
  Single = 'single',

  /** 多选 */
  Multiple = 'multiple'
}

export enum TabResizeType {
  /** 固定尺寸 */
  Fixed = 'fixed',

  /** 适应容器 */
  Adapt = 'adapt'
}

export const iconPositionOptions = [
  {
    value: IconPosition.Left,
    label: '',
    icon: `hticon-vis-padding-${IconPosition.Right}`,
    tip: '左侧'
  },
  {
    value: IconPosition.Right,
    label: '',
    icon: `hticon-vis-padding-${IconPosition.Left}`,
    tip: '右侧'
  },
  {
    value: IconPosition.Top,
    label: '',
    icon: `hticon-vis-padding-${IconPosition.Bottom}`,
    tip: '顶部'
  },
  {
    value: IconPosition.Bottom,
    label: '',
    icon: `hticon-vis-padding-${IconPosition.Top}`,
    tip: '底部'
  }
];
