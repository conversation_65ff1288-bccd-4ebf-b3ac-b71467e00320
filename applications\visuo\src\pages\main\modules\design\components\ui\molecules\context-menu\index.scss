@import '../../../index';

.#{$vis-prefix}-context-menu {
  @apply fixed visible z-6000 top-0 left-0;

  background-color: $input-active-bg;
  box-shadow: 0 1px 5px rgb(0 0 0 / 20%), 0 2px 2px rgb(0 0 0 / 14%), 0 3px 1px -2px rgb(0 0 0 / 12%);

  &-content {
    &-list {
      // @apply pl-0! pr-0! pt-1! pb-1!;

      &-item {
        // @apply rounded-0! pl-1.5! pr-4!;

        &-icon {
          // @apply p-1!;
        }
      }

      .group-active::after {
        @apply hidden;
      }

      .side-icon-12 {
        font-size: $primary-font-size !important;
      }
    }

    &-scroll {
      @apply overflow-y-auto relative w-fit;

      &::-webkit-scrollbar {
        @apply absolute w-1.5 h-full;
      }

      &::-webkit-scrollbar-thumb,
      ::-webkit-scrollbar-track {
        @apply absolute rounded-2.5;

        box-shadow: none;
      }

      &::-webkit-scrollbar-thumb {
        @apply bg-[#cbcbcb] opacity-20 cursor-grab;
      }
    }
  }
}
