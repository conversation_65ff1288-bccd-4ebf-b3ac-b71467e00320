import { defineComponent, computed, ref, watch, nextTick } from 'vue';
import { useDesignStore } from '../../../../stores';
import { Graph, GraphType, QuickStyle, useDocumentStore, useFrame, useGraphConfig } from '@vis/document-core';
import { Block, WidgetBlock } from '@vis/document-core';
import VisConfigProperty from './property/property.vue';
import VisConfigOption from './option/option.vue';
import VisConfigData from './data/data.vue';
import VisConfigQuickStyle from './quick-style/quick-style.vue';

/**
 * 组件属性面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-style',
  components: {
    VisConfigProperty,
    VisConfigOption,
    VisConfigData,
    VisConfigQuickStyle
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();

    const activeGraph = computed(() => designStore.active.value.graph as Graph);

    const docStore = useDocumentStore();

    const { prepareSize } = useFrame();

    // 获取当前选中的组件
    const activeBlock = computed(
      () => docStore.document.value.blocks.find((b) => b.id === (activeGraph.value as Block)?.decoration) as WidgetBlock
    );

    const tab = ref('property');

    const { graphConfig } = useGraphConfig();

    /**
     * 获取当前选中的组件标题
     */
    const activeTitle = computed(() => {
      return activeBlock.value?.name || graphName.value || activeGraph.value?.type;
    });

    const graphName = computed(() => {
      return graphConfig[activeGraph.value?.type].name;
    });

    /**
     * 设置容器尺寸
     * @param size
     */
    const onFrameSize = (size: { width: number; height: number }) => {
      activeGraph.value.width = size.width;
      activeGraph.value.height = size.height;
    };

    const scrollAreaRef = ref();

    const isBottom = ref(false);
    const verticalSize = ref(0);
    const verticalContainerSize = ref(0);

    // 判断是否到底部的通用函数
    const checkIsBottom = () => {
      if (verticalContainerSize.value > 0 && verticalSize.value > 0) {
        if (verticalSize.value > verticalContainerSize.value) {
          isBottom.value = true;
        } else {
          isBottom.value = false;
        }
      } else {
        isBottom.value = false;
      }
    };

    // 更新滚动信息的通用函数
    const updateScrollInfo = (info?: { verticalSize: number; verticalContainerSize: number }) => {
      if (info) {
        verticalSize.value = info.verticalSize;
        verticalContainerSize.value = info.verticalContainerSize;
      } else if (scrollAreaRef.value) {
        // 主动获取滚动信息
        const scrollInfo = scrollAreaRef.value.getScroll();

        if (scrollInfo) {
          verticalSize.value = scrollInfo.verticalSize;
          verticalContainerSize.value = scrollInfo.verticalContainerSize;
        }
      }

      checkIsBottom();
    };

    const handleScroll = (info: { verticalSize: number; verticalContainerSize: number }) => {
      updateScrollInfo(info);
    };

    const refreshScrollState = () => {
      isBottom.value = false;
      verticalSize.value = 0;

      // 等待 DOM 更新完成后再重新计算
      nextTick(() => {
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            updateScrollInfo();
          });
        });
      });
    };

    // 监听 tab 变化，刷新滚动状态
    watch(
      () => [tab.value],
      () => {
        refreshScrollState();
      },
      {
        deep: true
      }
    );

    // 监听 activeGraph 的 id 变化
    watch(
      () => activeGraph.value?.id,
      (newId, oldId) => {
        if (newId !== oldId) {
          if (scrollAreaRef.value) {
            scrollAreaRef.value.setScrollPosition('vertical', 0, 0);
            // 滚动后刷新状态
            refreshScrollState();
          }

          if (activeGraph.value?.type === GraphType.Block) {
            tab.value = 'option';
          } else {
            tab.value = 'property';
          }
        }
      }
    );

    //#region 快速切换
    const quickStyleRef = ref();

    const openPopup = (e: Event) => {
      e.stopPropagation();
      quickStyleRef.value?.showPopup(e);
    };

    //#endregion 快速切换

    return {
      activeGraph,
      activeBlock,
      activeTitle,
      tab,

      prepareSize,
      GraphType,
      onFrameSize,

      scrollAreaRef,
      handleScroll,
      refreshScrollState,
      isBottom,
      quickStyleRef,
      openPopup
    };
  }
});
