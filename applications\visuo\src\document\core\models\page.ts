import type { Block } from './block';
import type { Frame } from './frame';
import { GraphBasic, GraphType } from './graph-basic';
import type { TextBox } from './text-box';
import { FillPaints } from './ui';

/**
 * 页面
 * <AUTHOR>
 */
export class Page extends GraphBasic {
  type = GraphType.Page;

  /** 页面名称 */
  name = '页面 1';

  /** 页面组名称 */
  group = '';

  /** 画布背景颜色 */
  backgroundColor = new FillPaints();

  /** 主容器id */
  main = '';

  /** 辅助线 */
  guides = new Guides();

  children: Array<Frame | Block | TextBox> = [];
}

/**
 * 辅助线
 */
export class Guides {
  visible = true;
  horizontal: number[] = [];
  vertical: number[] = [];
}

export interface GuidesInterface {
  getGuides(): number[];
  scroll(pos: number): void;
  scrollGuides(pos: number): void;
  loadGuides(guides: number[]): void;
  resize(): void;
}

export interface ClickOutsideElement extends HTMLElement {
  _clickOutsideHandler?: (e: MouseEvent) => void;
}

export class Group extends GraphBasic {
  /** 类型 */
  type = GraphType.Group;

  /** 子节点 */
  children: Array<Group | Page> = [];
}
