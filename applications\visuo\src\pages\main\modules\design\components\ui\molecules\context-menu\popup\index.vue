<template>
  <q-menu v-model="popupShow" :target="false">
    <div ref="popupRef" :style="popupStyle" class="vis-store-page-popup vis-menu">
      <q-card>
        <q-card-section v-if="options.title" class="vis-store-page-popup-title">
          <span>{{ options.title }}</span>
          <q-btn icon="close" @click="onCancel" dense flat />
        </q-card-section>
        <q-card-section v-if="options.message" class="vis-store-page-popup-message">
          <q-icon name="warning" color="warning" class="vis-icon" />
          <span>{{ options.message }}</span>
        </q-card-section>
        <q-card-actions class="vis-store-page-popup-actions" align="right">
          <q-btn flat color="grey" label="取消" @click="onCancel" />
          <q-btn color="primary" label="确定" @click="onConfirm" />
        </q-card-actions>
      </q-card>
    </div>
  </q-menu>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
