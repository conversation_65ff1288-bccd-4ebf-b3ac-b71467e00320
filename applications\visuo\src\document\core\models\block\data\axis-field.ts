/**
 * 数据资源字段
 * <AUTHOR>
 */
export class AxisField {
  /**
   * 字段id
   */
  id?: string;

  /**
   * 字段名
   */
  fieldName?: string;

  /**
   * 字段别名
   */
  fieldAlias?: string;

  /**
   * 源字段名
   */
  sourceFieldName?: string;

  /**
   * 函数
   */
  aggregator?: Aggregator;

  /**
   * 字段类型 dim | measure
   */
  fieldType?: string;

  /**
   * 字段的实际类型
   */
  dataType?: string;

  /**
   * 排序方式 desc | asc | ''
   */
  sortDir?: string;

  /**
   * 映射字段类型 color ｜size ｜ tooltip ｜ shape
   */
  mapperType?: string;

  /**
   * 数据转换
   */
  dataTransformation?: DateTransFormation | NumberTransFormation;

  [key: string]: any;

  /**
   *
   * @param fieldName 字段名
   * @param fieldAlias 字段别名
   * @param aggregator 函数
   * @param fieldType 字段类型 dim | measure
   * @param dataType 字段的实际类型
   */
  constructor(
    fieldName: string | undefined,
    fieldAlias: string | undefined,
    aggregator?: Aggregator,
    fieldType?: string,
    dataType?: string
  ) {
    fieldName && (this.fieldName = fieldName);
    fieldAlias && (this.fieldAlias = fieldAlias);
    aggregator && (this.aggregator = aggregator);
    fieldType && (this.fieldType = fieldType);
    dataType && (this.dataType = dataType);
  }
}

export type AxisFields = Array<AxisField>;

/**
 * 过滤筛选字段的过滤数据
 */
export interface FilterAxisField extends AxisField {
  /** 过滤值: `DataFilter` */
  filter?: any;

  /** 过滤类型: `DataFilterType` */
  filterType?: any;
}

/**
 * 日期格式化
 */
export interface DateTransFormation {
  /** 单位值 */
  unit: string;

  /**
   * 日期类型
   * yearQuarter-年季度
   * yearMonth-年月
   * yearWeek-年周
   * yearMonthDay-年月日
   * yearMonthDayHourMinute-年月日时分
   * monthDay-月日
   * hourMinute-时分
   * year-年
   * quarter-季度
   * month-月
   * week-周
   * day-日
   * hour-时
   * minute-分
   */
  dateType:
    | 'yearQuarter'
    | 'yearMonth'
    | 'yearWeek'
    | 'yearMonthDay'
    | 'yearMonthDayHourMinute'
    | 'monthDay'
    | 'hourMinute'
    | 'year'
    | 'quarter'
    | 'month'
    | 'week'
    | 'day'
    | 'hour'
    | 'minute';

  /** 日期分隔符
   * 1-默认(-)
   * 2-中文
   * 3-斜杠(/)
   */
  dateSeparator: 1 | 2 | 3;

  /** 时间分隔符
   * 1-默认(:)
   * 2-中文
   */
  timeSeparator: 1 | 2;

  /** 小时格式
   * 24-24小时制
   * 12-12小时制
   */
  timeType: 12 | 24;

  /** 是否自动补0
   * 0-否，1-是
   */
  paddedByZero: 0 | 1;

  /** 禁用分隔符
   * 0-否，1-是
   */
  separator: 0 | 1;

  /** 格式化表达式 */
  expression: string;
}

/**
 * 数值格式化
 */
export interface NumberTransFormation {
  /**
   * 格式化类型
   * normal 默认不做格式化
   * number
   */
  type: 'normal' | 'number';

  /** 小数点 */
  decimal: number;

  /** 前缀 */
  prefix: string;

  /** 后缀 */
  suffix: string;

  /** 千分位分隔符 */
  thousand: boolean;

  /** 四舍五入 */
  rounding: boolean;

  /** 单位：无、千、万、百万、 亿、K、M */
  unit: 'none' | 'thousand' | 'million' | 'billion' | 'K' | 'M';
}

export enum Aggregator {
  SUM = 'sum',
  AVG = 'avg',
  MAX = 'max',
  MIN = 'min',
  COUNT = 'count'
}
