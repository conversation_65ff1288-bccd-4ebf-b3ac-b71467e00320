<template>
  <div :className="className" :id="frame.id" :parent="frame.parent" :style="frameStyle">
    <!-- 无滚动条: wrap处理裁剪超出内容 -->
    <div v-if="!isScroll" class="wrap" :style="wrapStyle" @scroll="onScroll">
      <vis-entrance v-for="ele in children" :key="ele.id" :graph="ele" :parent="frame" />
    </div>
    <!-- 有滚动条 -->
    <q-scroll-area v-else :style="scrollStyle" ref="scrollRef" @scroll="onScroll">
      <div v-if="isFlex(frame) || isGrid(frame)" class="wrap" :style="wrapStyle">
        <vis-entrance v-for="ele in children" :key="ele.id" :graph="ele" :parent="frame" />
      </div>
      <vis-entrance v-else v-for="ele in children" :key="ele.id" :graph="ele" :parent="frame" />
      <!-- 由于图形是绝对定位的，脱离了文档流，使得无法撑开容器，所以需要一个空元素来撑开容器 -->
      <div v-if="!isMainFrame" :style="contentStyle"></div>
    </q-scroll-area>
    <!-- 自由布局，约束内配置了固定的图形 -->
    <vis-entrance v-for="ele in fixChildren" :key="ele.id" :graph="ele" :parent="frame" />
  </div>
</template>

<script lang="ts" src="./frame.ts"></script>
<style lang="scss" src="./frame.scss"></style>
