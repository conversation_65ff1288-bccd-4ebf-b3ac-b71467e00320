// export function UUID() {
//   const generate = () => {
//     if (typeof crypto === 'object') {
//       if (typeof (crypto as any).randomUUID === 'function') {
//         return (crypto as any).randomUUID();
//       }
//       if (typeof crypto.getRandomValues === 'function' && typeof Uint8Array === 'function') {
//         const callback = (c: any) => {
//           const num = Number(c);
//           return (num ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16);
//         };
//         return ([1e7].toString() + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, callback);
//       }
//     }
//     let timestamp = new Date().getTime();
//     let perforNow = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0;
//     return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
//       let random = Math.random() * 16;
//       if (timestamp > 0) {
//         random = (timestamp + random) % 16 | 0;
//         timestamp = Math.floor(timestamp / 16);
//       } else {
//         random = (perforNow + random) % 16 | 0;
//         perforNow = Math.floor(perforNow / 16);
//       }
//       return (c === 'x' ? random : (random & 0x3) | 0x8).toString(16);
//     });
//   };
//   return generate().replaceAll('-', '_');
// }
export function UUID(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz';
  let timestamp = new Date().getTime();
  let perforNow = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0;
  const uuid = 'xxxxxxxxxxxxxxx4xxxyxxxxxxxxx'.replace(/[xy]/g, (c) => {
    let random = Math.random() * 16;
    if (timestamp > 0) {
      random = (timestamp + random) % 16 | 0;
      timestamp = Math.floor(timestamp / 16);
    } else {
      random = (perforNow + random) % 16 | 0;
      perforNow = Math.floor(perforNow / 16);
    }
    const charIndex = Math.floor(Math.random() * chars.length);
    if (c === 'x') {
      return random.toString(16);
    } else {
      return chars[charIndex] + ((random & 0x3) | 0x8).toString(16);
    }
  });

  if (!isNaN(parseInt(uuid.charAt(0)))) {
    return UUID();
  }

  return uuid;
}

/** 确定性 hash */
function deterministicHash(str: string) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // 转换为 32 位整数
  }
  return Math.abs(hash).toString(36); // 转换为 36 进制字符串
}

/**
 * 根据内容生成确定性的 UUID
 * @param content
 * @param length
 * @returns
 * <AUTHOR>
 * */
export function contentBaseHash(content: string, length = 8) {
  const str = typeof content === 'string' ? content : JSON.stringify(content);
  const hash = deterministicHash(str);

  if (hash.length >= length) {
    return hash.substring(0, length);
  } else {
    let result = hash;
    while (result.length < length) {
      result += hash;
    }
    return result.substring(0, length);
  }
}
