import type { <PERSON>F<PERSON>, Block, Paragraph, WidgetBlock } from '../models';

/**
 * 组件公共方法
 * <AUTHOR>
 */
export const useWidget = () => {
  /**
   * 获取组件配置
   * @param widget 组件
   * @returns 组件配置
   */
  const getWidgetOptions = (widget: WidgetBlock) => {
    const option = widget?.options;
    return option;
  };

  /**
   * 获取组件样式
   * @param widget 组件
   * @returns 组件样式
   */
  const getWidgetStyle = (widget: WidgetBlock) => {
    const style = widget?.options?.style;

    return style;
  };

  return {
    getWidgetStyle,
    getWidgetOptions
  };
};
