<template>
  <div
    class="vis-input flex h-full items-center"
    :class="[{ disabled: computedOptions.disabled }, { column: topPosition }]"
  >
    <!-- 标签 -->
    <div
      v-if="computedOptions.label?.show"
      class="vis-input__label"
      :class="{ 'w-full': topPosition }"
      :style="labelStyle"
    >
      {{ computedOptions.label.text }}
    </div>
    <!-- 输入框 -->
    <div
      class="vis-input__content flex-1"
      :class="[{ 'vis-input__content--readonly': isDesign }, topPosition ? 'w-full' : 'h-full']"
    >
      <q-input
        ref="inputRef"
        v-model="inputValue"
        @update:modelValue="onValueChange"
        :placeholder="computedOptions.placeholder"
        flat
        borderless
        class="h-full px-2 relative items-center"
        :style="style"
        input-class="!h-full"
        :input-style="inputStyle"
        :clearable="computedOptions.clearable"
        @clear="handleClear"
        :rules="lazyRules"
        lazy-rules
      >
        <!-- 查询器和前缀 -->
        <template #prepend v-if="computedOptions.query?.show || prefixType === 'icon' || prefixType === 'text'">
          <q-select
            v-if="computedOptions.query?.show"
            v-model="queryValue"
            @update:modelValue="onValueChange"
            :options="InputQueryTypeOptions"
            borderless
            dense
            options-dense
            emit-value
            map-options
            :disable="computedOptions.query.locked"
          />
          <vis-svg-icon
            v-if="prefixType === 'icon' && computedOptions.style.prefix?.icon"
            :icon="computedOptions.style.prefix.icon"
            :style="prefixIconStyle"
          />
          <span v-if="prefixType === 'text'" :style="prefixTextStyle">{{ computedOptions.style.prefix?.text }}</span>
        </template>
        <!-- 后缀 -->
        <template #append v-if="suffixType === 'icon' || suffixType === 'text'">
          <vis-svg-icon
            v-if="suffixType === 'icon' && computedOptions.style.suffix?.icon"
            :icon="computedOptions.style.suffix.icon"
            :style="suffixIconStyle"
          />
          <span v-if="suffixType === 'text'" :style="suffixTextStyle">{{ computedOptions.style.suffix?.text }}</span>
        </template>
      </q-input>
    </div>
  </div>
</template>
<script lang="ts" src="./input.ts"></script>
<style lang="scss" src="./input.scss"></style>
