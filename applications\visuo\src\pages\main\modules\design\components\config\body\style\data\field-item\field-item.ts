import type { DatasetField } from '@hetu/metadata-shared';
import { defineComponent, ref, type PropType, computed, nextTick } from 'vue';
import VisFieldList from '../field-list/field-list.vue';
import VisFieldConfig from '../field-config/field-config.vue';
import { AxisField, WidgetBlock, Block, Graph, useDocumentStore, Aggregator } from '@vis/document-core';
import draggable from 'vuedraggable';
import { useDesignStore } from '../../../../../../stores';
import { useDataset } from '../../../../../../hooks';

/**
 * 字段项组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-field-item',
  components: {
    draggable,
    VisFieldList,
    VisFieldConfig
  },
  props: {
    fieldMapping: {
      type: Object as PropType<{ datasetName: string; datasetField: DatasetField[] }>,
      default: () => ({})
    },
    mapKey: {
      type: String as PropType<'xField' | 'yField' | 'dataMapping' | 'columns' | 'rows' | 'levels'>,
      default: ''
    }
  },
  setup(props) {
    const { active } = useDesignStore();

    const activeGraph = computed(() => active.value.graph as Graph);

    const docStore = useDocumentStore();

    // 获取当前选中的组件
    const activeBlock = computed(() => {
      const blockId = (activeGraph.value as Block)?.decoration;
      if (!blockId) return null;
      return (docStore.document.value.blocks.find((b) => b.id === blockId) as WidgetBlock) || null;
    });

    // const dataMapping = computed(() => activeBlock.value?.dataMapping || {});

    //当前选中的字段id
    const activeIds = computed(() => {
      return activeBlock.value?.[props.mapKey]?.map((field: AxisField) => field.id) || [];
    });

    // 控制字段列表展开状态
    const openStates = ref<Record<string, boolean>>({});

    // 控制字段配置菜单显示状态
    const configStates = ref<Record<string, boolean>>({});

    // 获取或设置指定的展开状态
    const getOpenState = (index: number) => openStates.value[`${props.mapKey}-${index}`] || false;
    const toggleOpenState = (index: number, type: 'open' | 'close') => {
      if (index !== -1) {
        openStates.value[`${props.mapKey}-${index}`] = type === 'open' ? true : false;
      }
    };

    // 获取或设置字段配置菜单的显示状态
    const getConfigState = (index: number) => configStates.value[`${props.mapKey}-${index}`] || false;
    const toggleConfigState = (index: number, isVisible: boolean) => {
      configStates.value[`${props.mapKey}-${index}`] = isVisible;
    };

    const getItemKey = (item: AxisField) => {
      return item.fieldName || Math.random().toString();
    };

    // 删除字段
    const removeField = (index: number) => {
      const key = props.mapKey;
      const fields = activeBlock.value?.[key];
      if (fields && fields.length > index) {
        fields.splice(index, 1);
        if (activeBlock.value) {
          activeBlock.value[key] = fields;
        }
      }
    };

    // 添加字段
    const addField = (fields: DatasetField[]) => {
      if (activeBlock.value) {
        activeBlock.value[props.mapKey] = fields;
      }
    };

    // 单选逻辑
    const selectField = (field: DatasetField[], index: string) => {
      if (!activeBlock.value) return;
      const oldIndex = activeBlock.value[props.mapKey]?.findIndex((item: AxisField) => item.id === field[0].id);
      if (activeBlock.value[props.mapKey]) {
        (activeBlock.value[props.mapKey] as AxisField[])[Number(index)] = field[0] as AxisField;
        if (oldIndex !== -1) {
          (activeBlock.value[props.mapKey] as AxisField[]).splice(oldIndex, 1);
        }
      }
    };

    const { getDataTypeIcon } = useDataset();

    // 处理字段前的icon
    const getFieldIcon = (field: AxisField) => {
      return field.sortDir ? field.sortDir : 'field-' + getDataTypeIcon(field.fieldDatatype);
    };

    // 处理过滤字段
    const handleFilter = (index: number) => {
      // 检查原始字段是否存在
      if (!activeBlock.value) return;
      const sourceField = activeBlock.value[props.mapKey]?.[index];
      if (!sourceField) {
        return;
      }
      if (activeBlock.value.filters && activeBlock.value.filters.length > 0) {
        const filterIndex = activeBlock.value.filters.findIndex((filter: AxisField) => filter.id === sourceField.id);
        if (filterIndex !== -1) {
          // 更新已存在的筛选字段，保持筛选相关属性
          const existingFilter = activeBlock.value.filters[filterIndex];
          activeBlock.value.filters[filterIndex] = {
            ...sourceField,
            // 保持原有的筛选属性
            filterExpression: existingFilter.filterExpression,
            filterRule: existingFilter.filterRule,
            includeNull: existingFilter.includeNull
          };
        } else {
          activeBlock.value.filters.push({
            ...sourceField
          });
        }
      } else {
        activeBlock.value.filters = [
          {
            ...sourceField
          }
        ];
      }
    };

    // 处理排序字段
    const handleSort = (index: number, order: 'asc' | 'desc') => {
      if (!activeBlock.value) return;
      const keys = ['xField', 'yField', 'dataMapping', 'columns', 'rows', 'levels', 'filters'] as (keyof WidgetBlock)[];
      keys.map((key) => {
        activeBlock.value?.[key]?.map((field: AxisField) => {
          if (field.id === activeBlock.value?.[props.mapKey]?.[index].id) {
            field.sortDir = order;
          }
        });
      });
    };

    // 处理度量计算字段
    const handleAggregator = (index: number, aggregator: Aggregator) => {
      if (!activeBlock.value) return;
      const keys = ['xField', 'yField', 'dataMapping', 'columns', 'rows', 'levels'] as (keyof WidgetBlock)[];
      keys.map((key) => {
        activeBlock.value?.[key]?.map((field: AxisField) => {
          if (field.id === activeBlock.value?.[props.mapKey]?.[index].id) {
            field.aggregator = aggregator;
          }
        });
      });
    };

    return {
      activeBlock,
      activeIds,
      // dataMapping,
      getOpenState,
      toggleOpenState,
      getConfigState,
      toggleConfigState,
      getItemKey,
      removeField,
      addField,
      selectField,
      getFieldIcon,
      handleFilter,
      handleSort,
      handleAggregator
    };
  }
});
