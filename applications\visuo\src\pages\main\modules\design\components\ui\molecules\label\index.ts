import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { Text, FontConfig, Font, TextAlign, VerticalAlign, TextAdapt, useDocumentStore } from '@vis/document-core';

/**
 * 标签设置
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-label',
  props: {
    modelValue: {
      type: Object as PropType<Font>,
      required: true
    }
  },
  setup(props, { emit }) {
    const { fontSizes, fontWeights } = new FontConfig();

    const docStore = useDocumentStore();

    const fontFamilys = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });

    const computedModel = computed({
      get() {
        if (!props.modelValue) {
          return new Font();
        }
        return props.modelValue;
      },

      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    const visible = ref(computedModel.value.color.visible);
    const handleVisible = () => {
      visible.value = !visible.value;

      emit(
        'update:modelValue',
        Object.assign({}, computedModel.value, { color: { ...computedModel.value.color, visible: visible.value } })
      );
    };

    return {
      fontFamilys,
      fontSizes,
      fontWeights,
      computedModel,

      visible,
      handleVisible
    };
  }
});
