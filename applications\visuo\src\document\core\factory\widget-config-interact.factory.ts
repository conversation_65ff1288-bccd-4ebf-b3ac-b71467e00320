import type { Records } from '@hetu/util';
import {
  WidgetConfigEvent,
  type IWidgetConfigInteract,
  WidgetType,
  WidgetConfigAction,
  WidgetConfigDataDefine
} from '../models';
/**
 * 组件配置交互工厂类
 * 用于管理不同组件的交互事件配置
 * <AUTHOR>
 */
export class WidgetConfigInteractFactory {
  private static readonly MOUSE_EVENTS = {
    click: new WidgetConfigEvent('点击'),
    mouseover: new WidgetConfigEvent('悬停'),
    mouseout: new WidgetConfigEvent('移出')
  };

  private static readonly DRILLDOWN_EVENTS = {
    drillown: new WidgetConfigEvent(
      '当下钻时',
      '当执行下钻事件时',
      [
        new WidgetConfigDataDefine('currentField', '当前维度字段', 'object'),
        new WidgetConfigDataDefine('nextField', '下一级维度字段', 'object'),
        new WidgetConfigDataDefine('data', '数据', 'object'),
        new WidgetConfigDataDefine('eventParams', '图表事件参数', 'object')
      ],
      {
        currentField: {
          fieldAlias: '',
          fieldName: '',
          fieldType: ''
        },
        nextField: {
          fieldAlias: '',
          fieldName: '',
          fieldType: ''
        },
        data: {
          name: '',
          value: ''
        },
        eventParams: {}
      }
    ),
    scrollup: new WidgetConfigEvent(
      '当上卷时',
      '当点击上卷图形标签时',
      [
        new WidgetConfigDataDefine('currentField', '当前维度字段', 'object'),
        new WidgetConfigDataDefine('triggerField', '点击维度字段', 'object'),
        new WidgetConfigDataDefine('eventParams', '图表事件参数', 'object')
      ],
      {
        currentField: {
          fieldAlias: '',
          fieldName: '',
          fieldType: ''
        },
        triggerField: {
          fieldAlias: '',
          fieldName: '',
          fieldType: ''
        },
        eventParams: {}
      }
    )
  };

  private static readonly REQUEST_EVENTS = {
    beforeRequest: new WidgetConfigEvent(
      '当数据请求发送前',
      '当发送数据接口请求前触发事件',
      [
        new WidgetConfigDataDefine('filter', '过滤参数', 'object'),
        new WidgetConfigDataDefine('query', '查询参数', 'object'),
        new WidgetConfigDataDefine('sort', '排序参数', 'object')
      ],
      {
        filter: {},
        query: {},
        sort: {}
      }
    ),
    afterRequest: new WidgetConfigEvent('当数据请求完成后', '当数据接口请求完成后触发事件')
  };

  private static readonly ACTIONS = {
    updateWidget: new WidgetConfigAction('更新组件配置', '更新组件配置', {}, {}, {}),
    show: new WidgetConfigAction(
      '显示',
      '显示组件',
      {},
      {},
      {
        type: 'none',
        delay: 0,
        duration: 1000
      }
    ),
    hide: new WidgetConfigAction(
      '隐藏',
      '隐藏组件',
      {},
      {},
      {
        type: 'none',
        delay: 0,
        duration: 1000
      }
    )
  };

  private static readonly DRILLDOWN_ACTIONS = {
    drillown: new WidgetConfigAction(
      '下钻',
      '当前组件执行下钻动作。注: 下钻时请按照下钻顺序下钻',
      [
        new WidgetConfigDataDefine('name', '下钻维度字段值', 'string'),
        new WidgetConfigDataDefine('seriesIndex', '系列下标', 'number', '', false)
      ],
      {},
      {
        name: '',
        seriesIndex: 0
      }
    ),
    scrollup: new WidgetConfigAction(
      '上卷',
      '当前组件执行上卷动作',
      [
        new WidgetConfigDataDefine('fieldName', '字段名', 'string'),
        new WidgetConfigDataDefine('fieldAlias', '中文名', 'string')
      ],
      {},
      {
        fieldName: '',
        fieldAlias: ''
      }
    )
  };

  private static readonly REQUEST_ACTIONS = {
    requestDataApi: new WidgetConfigAction(
      '请求数据接口',
      '数据接口所需参数',
      [
        new WidgetConfigDataDefine('filter', '过滤参数', 'object'),
        new WidgetConfigDataDefine('query', '查询参数', 'object'),
        new WidgetConfigDataDefine('sort', '排序参数', 'object')
      ],
      {},
      {
        filter: {},
        query: {},
        sort: {
          fieldName: 'ASC'
        }
      }
    ),
    importDataApi: new WidgetConfigAction('导入数据', '导入数据', {}, {})
  };

  /**
   * 容器相关配置
   */
  readonly FRAME: IWidgetConfigInteract = {
    events: {
      ...WidgetConfigInteractFactory.MOUSE_EVENTS
    },
    actions: {
      ...WidgetConfigInteractFactory.ACTIONS
    }
  };

  /**
   * 段落组件配置
   */
  readonly [WidgetType.Paragraph]: IWidgetConfigInteract = {
    events: {
      ...WidgetConfigInteractFactory.MOUSE_EVENTS,
      ...WidgetConfigInteractFactory.REQUEST_EVENTS,
      ...WidgetConfigInteractFactory.DRILLDOWN_EVENTS
    },
    actions: {
      ...WidgetConfigInteractFactory.ACTIONS,
      ...WidgetConfigInteractFactory.REQUEST_ACTIONS,
      ...WidgetConfigInteractFactory.DRILLDOWN_ACTIONS
    }
  };

  /**
   * 标题组件配置
   */
  readonly [WidgetType.Title]: IWidgetConfigInteract = {
    events: {
      click: WidgetConfigInteractFactory.MOUSE_EVENTS.click
    },
    actions: {
      ...WidgetConfigInteractFactory.ACTIONS
    }
  };

  /**
   * 标题组件配置
   */
  readonly [WidgetType.Tab]: IWidgetConfigInteract = {
    events: {
      click: WidgetConfigInteractFactory.MOUSE_EVENTS.click
    },
    actions: {
      ...WidgetConfigInteractFactory.ACTIONS
    }
  };

  /**
   * 文本输入组件配置
   */
  readonly [WidgetType.Input]: IWidgetConfigInteract = {
    events: {
      click: WidgetConfigInteractFactory.MOUSE_EVENTS.click
    },
    actions: {
      ...WidgetConfigInteractFactory.ACTIONS
    }
  };

  /**
   * 获取组件配置
   * @param widgetName 组件名称
   * @returns 组件交互配置
   */
  getWidgetConfig(widgetType: WidgetType): IWidgetConfigInteract | undefined {
    return this[widgetType];
  }
  /**
   * 获取组件事件
   */
  getWidgetEvent(widgetType: WidgetType): Records<WidgetConfigEvent> | boolean | undefined {
    return this[widgetType].events;
  }

  /**
   * 获取组件动作
   */
  getWidgetAction(widgetType: WidgetType): Records<WidgetConfigAction> | boolean | undefined {
    return this[widgetType].actions;
  }
}
