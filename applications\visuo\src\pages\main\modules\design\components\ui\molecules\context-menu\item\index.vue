<template>
  <div
    class="vis-context-menu-content-scroll"
    :style="{ maxHeight: `${scrollMaxHeightComp}px`, height: `${getScrollHeight(menus)}px` }"
  >
    <q-list class="vis-context-menu-content-list" dense>
      <template v-for="(group, groupIndex) in menus" :key="groupIndex">
        <template v-for="(action, index) in group" :key="index">
          <!-- 菜单项 -->
          <template v-if="typeof action === 'string'">
            <q-item
              class="vis-context-menu-content-list-item"
              clickable
              :disable="getAction(action).disable"
              @click.prevent="onHandler(action, $event)"
            >
              <!-- <q-item-section class="vis-context-menu-content-list-item-icon" avatar>
                <ht-icon
                  v-if="getAction(action).icon.startsWith('hticon')"
                  class="vis-icon"
                  :name="getAction(action).icon"
                />
                <q-icon v-else :name="getAction(action).icon" />
              </q-item-section> -->
              <q-item-section>
                {{ getAction(action).title }}
              </q-item-section>
              <q-item-section side>
                <label>
                  <span v-for="(keyboard, index) in getAction(action).shortcuts" :key="index">
                    <span v-if="index > 0">&nbsp;+</span>
                    {{ getKeyCode(keyboard) }}
                  </span>
                </label>
              </q-item-section>
            </q-item>
          </template>

          <!-- 菜单组 -->
          <template v-else-if="Array.isArray(action)">
            <q-item
              class="vis-context-menu-content-list-item"
              clickable
              :disable="getGroup(action[0]).disable"
              :active="isActive(action[0])"
              @click.prevent="onHandler(action[0], $event)"
              active-class="group-active"
            >
              <!-- <q-item-section class="vis-context-menu-content-list-item-icon" avatar>
                <ht-icon
                  v-if="getGroup(action[0]).icon.startsWith('hticon')"
                  class="vis-icon"
                  :name="getGroup(action[0]).icon"
                />
                <q-icon v-else :name="getGroup(action[0]).icon" />
              </q-item-section> -->
              <q-item-section>{{ getGroup(action[0]).title }}</q-item-section>
              <q-item-section side>
                <q-icon name="keyboard_arrow_right" class="side-icon-12" />
              </q-item-section>

              <!-- 嵌套菜单 -->
              <q-menu anchor="top right" self="top left" :offset="[12, 0]" class="vis-menu overflow-hidden">
                <div class="vis-context-menu-content">
                  <vis-context-menu-item :menus="action[1]" @action="onActionEmit" />
                </div>
              </q-menu>
            </q-item>
          </template>
        </template>

        <!-- 分组分隔线 -->
        <q-separator v-if="groupIndex + 1 < menus.length" :key="`sep-${groupIndex}`" />
      </template>
    </q-list>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
