<template>
  <q-menu
    class="vis-menu py-1 w-[120px]"
    :offset="[136, -24]"
    @before-show="handleBeforeShow"
    @before-hide="handleBeforeHide"
  >
    <q-scroll-area :style="{ height: '220px', maxHeight: '300px' }">
      <q-list class="!py-0">
        <q-item clickable v-close-popup @click="handleFilter">
          <q-item-section>筛选</q-item-section>
        </q-item>
        <q-separator class="-!mx-3"></q-separator>
        <!-- 选择聚合函数 -->
        <q-item clickable :disable="field?.fieldType === 'dim'">
          <q-item-section>度量计算</q-item-section>
          <q-item-section side>
            <q-icon side name="keyboard_arrow_right" />
          </q-item-section>
        </q-item>
        <q-menu v-if="field?.fieldType !== 'dim'" :offset="[-124, -210]" class="vis-menu">
          <q-list :transition-duration="0">
            <q-item
              clickable
              v-close-popup="!!fun.support"
              class="!pt-0"
              v-for="fun in aggregatorFunctions"
              :key="fun.function"
              :disable="!fun.support"
              :active="field?.aggregator === fun.function"
              @click="handleAggregator(fun.function)"
            >
              <q-item-section>{{ fun.name }}</q-item-section>
            </q-item>
          </q-list>
        </q-menu>

        <q-separator class="-!mx-3"></q-separator>
        <q-item clickable v-close-popup>
          <q-item-section>数据格式</q-item-section>
        </q-item>
        <q-item clickable v-close-popup>
          <q-item-section>日期格式</q-item-section>
        </q-item>
        <q-separator class="-!mx-3"></q-separator>
        <q-item :active="field?.sortDir === 'asc'" clickable v-close-popup @click="handleSort('asc')">
          <q-item-section>升序</q-item-section>
        </q-item>
        <q-item :active="field?.sortDir === 'desc'" clickable v-close-popup @click="handleSort('desc')">
          <q-item-section>降序</q-item-section>
        </q-item>
        <q-separator class="-!mx-3"></q-separator>
       <!--  <q-item clickable v-close-popup>
          <q-item-section>重命名</q-item-section>
        </q-item> -->
        <q-item clickable v-close-popup @click="handleRemove">
          <q-item-section>删除</q-item-section>
        </q-item>
      </q-list>
    </q-scroll-area>
  </q-menu>
</template>

<script lang="ts" src="./field-config.ts"></script>
