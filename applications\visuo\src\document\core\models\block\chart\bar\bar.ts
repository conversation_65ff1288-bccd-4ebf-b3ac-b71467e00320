import { FillPaints } from '../../..';
import { Axis } from '../axis';
import { Chart, ChartOptions } from '../chart';
import { Mark } from '../mark';

/**
 * 柱状图标记
 * <AUTHOR>
 */
export class BarMark extends Mark {
  /**
   * 类型
   */
  type = 'rect';

  /**
   * 柱子宽度
   */
  width = 0;

  /**
   * 柱子最小高度
   */
  height = 0;

  /**
   * 类间距
   */
  spacing = 0;

  /**
   * 组间距
   */
  groupSpacing = 0;

  /**
   * 柱子圆角
   */
  radius = [0, 0, 0, 0];

  /**
   * 分组/堆叠
   */
  showType = 'group';

  /**
   * 柱子背景
   */
  background: FillPaints = new FillPaints();
}

/**
 * 柱状图配置
 * <AUTHOR>
 */
export class BarOptions extends ChartOptions {
  mark: BarMark = new BarMark();

  /** x轴 */
  xAxis: Axis = new Axis();

  /** y轴 */
  yAxis: Axis = new Axis();
}
