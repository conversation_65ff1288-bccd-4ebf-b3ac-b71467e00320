<template>
  <div class="vis-tab overflow-hidden" :style="allStyle">
    <q-scroll-area
      ref="scrollRef"
      v-if="
        layout.scrollbar && (tabOption.resizeX === TabResizeType.Fixed || tabOption.resizeY === TabResizeType.Fixed)
      "
      class="fit"
    >
      <div
        :style="tabStyle"
        :class="
          block.stroke && (block.stroke.align === StrokeAlign.Inside || block.stroke.align === StrokeAlign.Center)
            ? 'vis-tab-content'
            : ''
        "
      >
        <div
          v-for="(item, idx) in data"
          :key="idx"
          :style="tabItemStyle(idx)"
          class="vis-tab__item position-relative overflow-hidden cursor-pointer"
          ref="tabRef"
          @mouseover="hoverIndex = idx"
          @mouseleave="hoverIndex = -1"
          @click="handleActive(idx)"
        >
          <template v-if="shouldShowIcon(idx, 'before')">
            <!-- 悬浮状态下的图标 -->
            <div
              v-if="statusIconUrl(idx, 'hover')"
              class="overflow-hidden !rounded-none"
              :class="{ 'display-flex': tabOption.style.hover?.icon?.type === 'icon' }"
            >
              <q-img
                v-if="tabOption.style.hover?.icon?.type === 'picture'"
                :style="getIconStyle('picture', 'hover')"
                :src="statusIconUrl(idx, 'hover')"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle('icon', 'hover')" :icon="tabOption.style.hover?.icon?.icon" />
            </div>
            <!-- 选中状态下的图标 -->
            <div
              v-else-if="statusIconUrl(idx, 'active')"
              class="overflow-hidden !rounded-none"
              :class="{ 'display-flex': tabOption.style.active?.icon?.type === 'icon' }"
            >
              <q-img
                v-if="tabOption.style.active?.icon?.type === 'picture'"
                :style="getIconStyle('picture', 'active')"
                :src="statusIconUrl(idx, 'active')"
              ></q-img>
              <vis-svg-icon v-else :style="getIconStyle('icon', 'active')" :icon="tabOption.style.active?.icon?.icon" />
            </div>
            <!-- 普通状态下的图标 -->
            <template v-else-if="tabOption.icon">
              <div
                v-if="tabOption.icon.image && tabOption.icon.image.url && tabOption.icon.type === 'picture'"
                class="overflow-hidden !rounded-none"
              >
                <q-img :style="getIconStyle('picture')" :src="iconUrl"></q-img>
              </div>

              <div
                v-else-if="tabOption.icon.icon.name && tabOption.icon.type === 'icon'"
                class="overflow-hidden !rounded-none display-flex"
              >
                <vis-svg-icon :style="getIconStyle('icon')" :icon="tabOption.icon.icon" />
              </div>

              <div v-else class="overflow-hidden !rounded-none">
                <q-icon
                  name="image"
                  :style="getIconStyle('picture')"
                  :size="tabOption.icon.width + 'px'"
                  class="text-grey-5 text-shadow-none inline-block"
                ></q-icon>
              </div>
            </template>
          </template>
          <div
            class="overflow-hidden vis-tab__text position-relative"
            :style="tabContentStyle(idx)"
            ref="tabContentRef"
          >
            <span :style="getItemOverflowStyle(idx)" ref="marqueeRefs">{{ item.columns }}</span>
            <!-- 用于检测换行时，文本是否处于溢出状态 -->
            <span
              v-if="tabOption.style.overflow === 2"
              class="position-absolute top-0 left-0 opacity-0 whitespace-nowrap"
              ref="changLineRefs"
              >{{ item.columns }}</span
            >
            <template v-if="tabOption.style.overflow === 3 && textOverflowFlags[idx]">
              <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
              <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
            </template>
          </div>
          <template v-if="shouldShowIcon(idx, 'after')">
            <!-- 悬浮状态下的图标 -->
            <div
              v-if="statusIconUrl(idx, 'hover')"
              class="overflow-hidden !rounded-none"
              :class="{ 'display-flex': tabOption.style.hover?.icon?.type === 'icon' }"
            >
              <q-img
                v-if="tabOption.style.hover?.icon?.type === 'picture'"
                :style="getIconStyle('picture', 'hover')"
                :src="statusIconUrl(idx, 'hover')"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle('icon', 'hover')" :icon="tabOption.style.hover?.icon?.icon" />
            </div>
            <!-- 选中状态下的图标 -->
            <div
              v-else-if="statusIconUrl(idx, 'active')"
              class="overflow-hidden !rounded-none"
              :class="{ 'display-flex': tabOption.style.active?.icon?.type === 'icon' }"
            >
              <q-img
                v-if="tabOption.style.active?.icon?.type === 'picture'"
                :style="getIconStyle('picture', 'active')"
                :src="statusIconUrl(idx, 'active')"
              ></q-img>
              <vis-svg-icon v-else :style="getIconStyle('icon', 'active')" :icon="tabOption.style.active?.icon?.icon" />
            </div>
            <!-- 普通状态下的图标 -->
            <template v-else-if="tabOption.icon">
              <div
                v-if="tabOption.icon.image && tabOption.icon.image.url && tabOption.icon.type === 'picture'"
                class="overflow-hidden !rounded-none"
              >
                <q-img :style="getIconStyle('picture')" :src="iconUrl"></q-img>
              </div>

              <div
                v-else-if="tabOption.icon.icon.name && tabOption.icon.type === 'icon'"
                class="overflow-hidden !rounded-none display-flex"
              >
                <vis-svg-icon :style="getIconStyle('icon')" :icon="tabOption.icon.icon" />
              </div>

              <div v-else class="overflow-hidden !rounded-none">
                <q-icon
                  name="image"
                  :style="getIconStyle('picture')"
                  :size="tabOption.icon.width + 'px'"
                  class="text-grey-5 text-shadow-none inline-block"
                ></q-icon>
              </div>
            </template>
          </template>
        </div>
      </div>
    </q-scroll-area>
    <div
      class="h-full"
      :class="
        block.stroke && (block.stroke.align === StrokeAlign.Inside || block.stroke.align === StrokeAlign.Center)
          ? 'vis-tab-content'
          : ''
      "
      v-else
      :style="tabStyle"
    >
      <div
        v-for="(item, idx) in data"
        :key="idx"
        :style="tabItemStyle(idx)"
        class="vis-tab__item position-relative overflow-hidden cursor-pointer"
        ref="tabRef"
        @mouseover="hoverIndex = idx"
        @mouseleave="hoverIndex = -1"
        @click="handleActive(idx)"
      >
        <template v-if="shouldShowIcon(idx, 'before')">
          <!-- 悬浮状态下的图标 -->
          <div
            v-if="statusIconUrl(idx, 'hover')"
            class="overflow-hidden !rounded-none"
            :class="{ 'display-flex': tabOption.style.hover?.icon?.type === 'icon' }"
          >
            <q-img
              v-if="tabOption.style.hover?.icon?.type === 'picture'"
              :style="getIconStyle('picture', 'hover')"
              :src="statusIconUrl(idx, 'hover')"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle('icon', 'hover')" :icon="tabOption.style.hover?.icon?.icon" />
          </div>
          <!-- 选中状态下的图标 -->
          <div
            v-else-if="statusIconUrl(idx, 'active')"
            class="overflow-hidden !rounded-none"
            :class="{ 'display-flex': tabOption.style.active?.icon?.type === 'icon' }"
          >
            <q-img
              v-if="tabOption.style.active?.icon?.type === 'picture'"
              :style="getIconStyle('picture', 'active')"
              :src="statusIconUrl(idx, 'active')"
            ></q-img>
            <vis-svg-icon v-else :style="getIconStyle('icon', 'active')" :icon="tabOption.style.active?.icon?.icon" />
          </div>
          <!-- 普通状态下的图标 -->
          <template v-else-if="tabOption.icon">
            <div
              v-if="tabOption.icon.image && tabOption.icon.image.url && tabOption.icon.type === 'picture'"
              class="overflow-hidden !rounded-none"
            >
              <q-img :style="getIconStyle('picture')" :src="iconUrl"></q-img>
            </div>

            <div
              v-else-if="tabOption.icon.icon.name && tabOption.icon.type === 'icon'"
              class="overflow-hidden !rounded-none display-flex"
            >
              <vis-svg-icon :style="getIconStyle('icon')" :icon="tabOption.icon.icon" />
            </div>

            <div v-else class="overflow-hidden !rounded-none">
              <q-icon
                name="image"
                :style="getIconStyle('picture')"
                :size="tabOption.icon.width + 'px'"
                class="text-grey-5 text-shadow-none inline-block"
              ></q-icon>
            </div>
          </template>
        </template>
        <div class="overflow-hidden vis-tab__text position-relative" :style="tabContentStyle(idx)" ref="tabContentRef">
          <span :style="getItemOverflowStyle(idx)" ref="marqueeRefs">{{ item.columns }}</span>
          <!-- 用于检测换行时，文本是否处于溢出状态 -->
          <span
            v-if="tabOption.style.overflow === 2"
            class="position-absolute top-0 left-0 opacity-0 whitespace-nowrap"
            ref="changLineRefs"
            >{{ item.columns }}</span
          >
          <template v-if="tabOption.style.overflow === 3 && textOverflowFlags[idx]">
            <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
            <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
          </template>
        </div>
        <template v-if="shouldShowIcon(idx, 'after')">
          <!-- 悬浮状态下的图标 -->
          <div
            v-if="statusIconUrl(idx, 'hover')"
            class="overflow-hidden !rounded-none"
            :class="{ 'display-flex': tabOption.style.hover?.icon?.type === 'icon' }"
          >
            <q-img
              v-if="tabOption.style.hover?.icon?.type === 'picture'"
              :style="getIconStyle('picture', 'hover')"
              :src="statusIconUrl(idx, 'hover')"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle('icon', 'hover')" :icon="tabOption.style.hover?.icon?.icon" />
          </div>
          <!-- 选中状态下的图标 -->
          <div
            v-else-if="statusIconUrl(idx, 'active')"
            class="overflow-hidden !rounded-none"
            :class="{ 'display-flex': tabOption.style.active?.icon?.type === 'icon' }"
          >
            <q-img
              v-if="tabOption.style.active?.icon?.type === 'picture'"
              :style="getIconStyle('picture', 'active')"
              :src="statusIconUrl(idx, 'active')"
            ></q-img>
            <vis-svg-icon v-else :style="getIconStyle('icon', 'active')" :icon="tabOption.style.active?.icon?.icon" />
          </div>
          <!-- 普通状态下的图标 -->
          <template v-else-if="tabOption.icon">
            <div
              v-if="tabOption.icon.image && tabOption.icon.image.url && tabOption.icon.type === 'picture'"
              class="overflow-hidden !rounded-none"
            >
              <q-img :style="getIconStyle('picture')" :src="iconUrl"></q-img>
            </div>

            <div
              v-else-if="tabOption.icon.icon.name && tabOption.icon.type === 'icon'"
              class="overflow-hidden !rounded-none display-flex"
            >
              <vis-svg-icon :style="getIconStyle('icon')" :icon="tabOption.icon.icon" />
            </div>

            <div v-else class="overflow-hidden !rounded-none">
              <q-icon
                name="image"
                :style="getIconStyle('picture')"
                :size="tabOption.icon.width + 'px'"
                class="text-grey-5 text-shadow-none inline-block"
              ></q-icon>
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./tab.ts"></script>
<style lang="scss" src="./tab.scss"></style>
