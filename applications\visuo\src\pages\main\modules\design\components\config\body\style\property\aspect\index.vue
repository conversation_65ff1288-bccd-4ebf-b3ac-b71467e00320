<template>
  <div class="vis-aspect">
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label">透明度</div>
          <div class="vis-form-field__content">
            <vis-number v-model="graph.opacity" icon="hticon-vis-opacity" :min="0" :max="100" suffix="%" />
          </div>
        </div>

        <div class="vis-form-field">
          <div class="vis-form-field__label">圆角</div>
          <div class="vis-form-field__content">
            <vis-mix-input v-model="radius" icon="hticon-vis-radius" @update:model-value="radiusChange" :min="0" />
          </div>
        </div>
      </div>
      <q-btn flat class="btn-field" :class="{ active: showRadius }" @click="showRadius = !showRadius">
        <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
        <q-tooltip> 单独设置 </q-tooltip>
      </q-btn>
    </div>
  </div>

  <div class="vis-form-inline" v-if="showRadius">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-number v-model="graph.radius[0]" icon="hticon-vis-radius-lt" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-number v-model="graph.radius[1]" icon="hticon-vis-radius-rt" :min="0" />
        </div>
      </div>
    </div>
  </div>
  <div class="vis-form-inline" v-if="showRadius">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-number v-model="graph.radius[3]" icon="hticon-vis-radius-lb" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-number v-model="graph.radius[2]" icon="hticon-vis-radius-rb" :min="0" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
