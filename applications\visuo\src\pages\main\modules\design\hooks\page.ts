import { computed, nextTick, ref } from 'vue';
import { useDesignStore } from '../stores';
import { Graph, Group, GraphType, Page, Block, Frame, useDocumentStore } from '@vis/document-core';
import { UUID } from '@hetu/platform-shared';
import { isString, cloneDeepWith, cloneDeep } from 'lodash-es';
import { Notify } from 'quasar';
import { VIS_DESIGN_INFINITE_CANVAS } from '../models';

/**
 * 页面操作
 * <AUTHOR>
 */
export const usePage = () => {
  const designStore = useDesignStore();
  const documentStore = useDocumentStore();

  const documentComp = computed(() => documentStore.document.value);

  const pagesComp = computed(() => documentComp.value.children);
  const blocksComp = computed(() => documentComp.value.blocks);

  const groupCounter = ref<number>(0);
  const groupCounterComp = computed(() => ++groupCounter.value);

  /**
   * 更新order
   * */
  const updatePageOrder = () => {
    pagesComp.value.forEach((page, index, _) => (page.order = index));
  };

  /**
   * 根据id查找页面
   * @param id
   * @returns
   */
  const findPage = (id: string): Page | undefined => {
    return pagesComp.value.find((page) => page.id === id) as Page;
  };

  /**
   * 切换页面
   * @param page
   * @returns
   * */
  const switchPage = (page: Page | string) => {
    // 重置状态
    designStore.reset();
    const currentPage = findPage(!isString(page) ? page.id : page) as Page;
    calcPageZoom(currentPage);
    designStore.active.value.page = currentPage;
  };

  /**
   * 缩放内容，使其缩放显示全部内容，并且居中
   * @param page
   */
  const calcPageZoom = (page: Page) => {
    if (!page.children.length) {
      designStore.rulerState.value.zoom = 1;
      designStore.infiniteCanvasRef.value?.setZoom(1);
      return;
    }
    const minX = Math.min(...page.children.map((g) => g.transform.translate[0]));
    const minY = Math.min(...page.children.map((g) => g.transform.translate[1]));
    const maxX = Math.max(...page.children.map((g) => g.transform.translate[0] + g.width));
    const maxY = Math.max(...page.children.map((g) => g.transform.translate[1] + g.height));

    // 获取内容宽度和高度
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    // 获取dock高度
    const dockDom = document.querySelector('.vis-design-dock') as HTMLDivElement;
    const dockHeight = dockDom ? dockDom.offsetHeight + 10 : 0;

    // 设计器中间窗口的宽高
    const canvasDom = document.querySelector(`#${VIS_DESIGN_INFINITE_CANVAS}`) as HTMLDivElement;
    const canvasWidth = canvasDom.offsetWidth;
    const canvasHeight = canvasDom.offsetHeight - dockHeight;

    const sx = canvasWidth / contentWidth;
    const sy = canvasHeight / contentHeight;
    // 缩放值
    let scale = Math.min(sx, sy);
    scale = Math.min(1, scale);

    nextTick(() => {
      // 标尺缩放
      designStore.rulerState.value.zoom = scale;
      // 无限画布缩放
      designStore.infiniteCanvasRef.value?.setZoom(scale);
      // 内容的偏移值，水平垂直居中
      const offsetX = (canvasWidth - contentWidth * scale) / 2 / scale;
      const offsetY = (canvasHeight - contentHeight * scale) / 2 / scale;
      designStore.infiniteCanvasRef.value?.scrollTo(minX - offsetX, minY - offsetY);
    });
  };

  /**
   * 新增空白页面
   * @param page
   * @param group
   * @returns pageInstance
   * */
  const addPage = (group?: Group) => {
    // 新增实例
    const pageInstance = new Page();
    // 设置属性
    pageInstance.name = `页面 ${pagesComp.value.length + 1}`;
    // 加入列表
    if (group) {
      // 添加分类名称
      pageInstance.group = group.name;
      // 加到组内尾部
      const lastPage = group.children[group.children.length - 1] as Page;
      const lastIndex = pagesComp.value.findIndex((page) => page.id === lastPage.id);
      pagesComp.value.splice(lastIndex + 1, 0, pageInstance);
      // 更新order
      updatePageOrder();
    } else {
      pageInstance.order = pagesComp.value.length;
      pagesComp.value.push(pageInstance);
    }
    // 切换页面
    designStore.active.value.page = pageInstance;

    // 返回实例
    return pageInstance;
  };

  /**
   * 添加到页面组
   * @param page
   * @returns
   * */
  const addPageToGroup = (page: Page | string) => {
    const targetPage = findPage(!isString(page) ? page.id : page);
    if (targetPage) {
      const groupName = `分类 ${groupCounterComp.value}`;
      // 查看是否已有分类
      const hasGroup = pagesComp.value.find((page) => page.group === groupName);
      if (hasGroup) {
        // 删除原位置
        const targetIndex = pagesComp.value.findIndex((page) => page.id === targetPage.id);
        pagesComp.value.splice(targetIndex, 1);
        // 添加分类名称
        targetPage.group = groupName;
        // 加到新位置
        const hasGroupIndex = pagesComp.value.findIndex((page) => page.group === groupName);
        pagesComp.value.splice(hasGroupIndex, 0, targetPage);
        // 更新order
        updatePageOrder();
      } else {
        // 直接添加分类名称
        targetPage.group = groupName;
      }
    }
  };

  /**
   * 移动到页面组
   * @param page
   * @returns
   * */
  const movePageToGroup = (page: Page | string, groupId: string, groups: (Group | Page)[]) => {
    const targetPage = findPage(!isString(page) ? page.id : page);
    if (targetPage) {
      const group = groups.find((group) => group.id === groupId) as Group;
      targetPage.group = group ? group.name : '';
    }
  };

  /**
   * 获取页面的ids数组
   * @param groups
   * @returns
   * */
  const getPageIds = (groups: (Group | Page)[]) => {
    const ids: string[] = [];
    groups.forEach((group) => {
      if (group.children && group.children.length > 0) {
        ids.push(...getPageIds(group.children as Page[]));
      } else {
        ids.push(group.id);
      }
    });
    return ids;
  };

  /**
   * 根据ids排序页面
   * @param pageIds
   * @returns
   * */
  const sortPages = (pageIds: string[]) => {
    // 创建id-index映射
    const idIndexMap = new Map();
    pageIds.forEach((pageId, index) => idIndexMap.set(pageId, index));
    // 创建原数组副本排序
    const results = [...pagesComp.value].sort((a, b) => {
      const indexA = idIndexMap.has(a.id) ? idIndexMap.get(a.id) : Infinity;
      const indexB = idIndexMap.has(b.id) ? idIndexMap.get(b.id) : Infinity;

      // 如果某个ID不在指定的IDs数组中，将其排在最后
      return indexA - indexB;
    });
    // 更新order
    results.forEach((page, index, _) => (page.order = index)); // 更新order

    // 更新原数组
    documentComp.value.children = results;
  };

  /**
   * 修改分组/页面名称
   * @param graph
   * */
  const renamePage = designStore.setEditingGraph;
  const renamePageGroup = designStore.setEditingGraph;

  /**
   * 删除页面
   * @param page
   * @returns
   * */
  const deletePage = (page: Page | string) => {
    if (pagesComp.value.length <= 1) {
      return Notify.create({
        classes: 'vis-design-notify',
        position: 'top',
        type: 'negative',
        message: '无法删除所有页面，需至少保留一个页面'
      });
    }

    // 页面Id
    const pageId = !isString(page) ? page.id : page;

    const targetPage = findPage(pageId);
    if (targetPage) {
      // 删除页面为主页（home），将home值置空
      if (documentComp.value.home === pageId) {
        documentComp.value.home = '';
      }
      // 删除页面为当前选中页，选中相邻页面
      let nextPage = null;
      if (designStore.active.value.page.id === pageId) {
        const index = pagesComp.value.findIndex((page) => page.id === pageId);
        const nextIndex = index === pagesComp.value.length - 1 ? index - 1 : index + 1;
        nextPage = pagesComp.value[nextIndex];
      }
      // 删除页面
      const index = pagesComp.value.findIndex((page) => page.id === targetPage.id);
      pagesComp.value.splice(index, 1);
      // 更新order
      updatePageOrder();
      // 相邻页存在, 则选中
      nextPage && switchPage(nextPage);
    }
  };

  /**
   * 克隆节点，维护id和parent引用关系，复制block元素节点
   * @param value
   * @returns
   * */
  const customClone = (value: Page | Graph) => {
    // 创建节点ID映射表
    const idMap = new Map();
    // 创建BLOCK映射表
    const blockMap = new Map();

    // 收集所有 ID 并生成新 ID
    function collectIds(node: any) {
      if (node && typeof node === 'object' && node.id) {
        // 生成新的节点ID
        idMap.set(node.id, UUID());
        // 生成新的BLOCK ID
        if (node.type === GraphType.Block) {
          blockMap.set(node.decoration, UUID());
        }
        // 递归处理
        if (node.children) {
          node.children.forEach(collectIds);
        }
      }
    }

    collectIds(value);

    function customizer(value: any) {
      if (value && typeof value === 'object' && 'id' in value) {
        // 克隆节点并更新 ID

        // 这种方式会导致克隆对象和原对象共享内存，导致修改克隆对象时，原对象也会被修改
        // const cloned = { ...value };
        const cloned = cloneDeep(value);

        cloned.id = idMap.get(value.id) || value.id;

        // 更新 parent 引用
        if (value.parent) {
          cloned.parent = idMap.get(value.parent) || value.parent;
        }

        // 处理BLOCK类型数据
        if (cloned.type === GraphType.Block) {
          // 更新 BLOCK ID
          cloned.decoration = blockMap.get(value.decoration) || value.decoration;
          // 克隆 BLOCK 数据
          const targetBlock = blocksComp.value.find((b) => b.id === (value as Block).decoration);
          const clonedBlock: any = cloneDeep(targetBlock);
          clonedBlock.id = cloned.decoration;
          blocksComp.value.push(clonedBlock);
        }

        // 递归处理
        if (cloned.children) {
          cloned.children = cloned.children.map((child: any) => cloneDeepWith(child, customizer));
        }

        return cloned;
      }

      return undefined;
    }

    return cloneDeepWith(value, customizer);
  };

  /**
   * 复制页面
   * @param page
   * @returns
   * */
  const clonePage = (page: Page | string) => {
    const targetPage = findPage(!isString(page) ? page.id : page);
    if (targetPage) {
      // 克隆
      const clonedPage = customClone(targetPage);
      clonedPage.name = `${clonedPage.name} 副本`;
      // 更新主容器
      clonedPage.main = clonedPage.children[0] ? clonedPage.children[0].id : '';
      // 当前位置相邻处
      const index = pagesComp.value.findIndex((page) => page.id === targetPage.id);
      pagesComp.value.splice(index + 1, 0, clonedPage);
      // 更新order
      updatePageOrder();
      // 切换页面
      switchPage(clonedPage);
    }
  };

  /**
   * 设置为主页
   * @param page
   * @returns
   * */
  const setHomePage = (page: Page | string) => {
    const targetPage = findPage(!isString(page) ? page.id : page);
    if (targetPage) {
      documentComp.value.home = targetPage.id;
    }
    switchPage((<Page>page).id);
  };

  /**
   * 取消页面组
   * @param group
   * @returns
   * */
  const cancelPageGroup = (group: Group) => {
    // 收集组内页面的id
    const pageIds = group.children.map((page) => page.id);
    // 取消分组
    pagesComp.value.forEach((page) => {
      if (pageIds.includes(page.id)) {
        page.group = '';
      }
    });
  };

  /**
   * 删除页面组
   * @param group
   * @returns
   * */
  const deletePageGroup = (group: Group) => {
    // 收集组内页面的id
    const pageIds = group.children.map((page) => page.id);
    // 组内页面的起始和结束index
    const firstPageIndex = pagesComp.value.findIndex((page) => page.id === pageIds[0]);
    const lastPageIndex = pagesComp.value.findIndex((page) => page.id === pageIds[pageIds.length - 1]);

    // 组内页面的起始和结束index的前后是否存在页面，如果都不存在，则无法删除
    if (!(pagesComp.value[firstPageIndex - 1] || pagesComp.value[lastPageIndex + 1])) {
      return Notify.create({
        classes: 'vis-design-notify',
        position: 'top',
        type: 'negative',
        message: '无法删除所有页面，需至少保留一个页面'
      });
    }

    // 删除页面组内含有主页（home），将home值置空
    if (pageIds.includes(documentComp.value.home)) {
      documentComp.value.home = '';
    }
    // 删除页面组内含有选中页，选中相邻页面
    let nextPage = null;
    if (pageIds.includes(designStore.active.value.page.id)) {
      const nextIndex = lastPageIndex === pagesComp.value.length - 1 ? firstPageIndex - 1 : lastPageIndex + 1;
      nextPage = pagesComp.value[nextIndex];
    }
    // 删除组内页面
    pagesComp.value.splice(firstPageIndex, pageIds.length);
    // 更新order
    updatePageOrder();
    // 相邻页存在, 则选中
    nextPage && switchPage(nextPage);
  };

  return {
    findPage,
    switchPage,
    addPage,
    addPageToGroup,
    movePageToGroup,
    getPageIds,
    sortPages,
    renamePage,
    renamePageGroup,
    deletePage,
    clonePage,
    customClone,
    setHomePage,
    cancelPageGroup,
    deletePageGroup
  };
};
