export * from './models';
export * from './factory';
export * from './stores';
export * from './hooks';
export * from './services';

import type { App } from 'vue';
import { components } from './components';
import { useApp } from '@hetu/core';

let isRegister = false;

function install(app: App) {
  // 全局注册组件
  components.forEach((component) => app.component(component.name as string, component));
  isRegister = true;
}

export function setupGraph() {
  const app = useApp();
  !isRegister && install(app);
}
