@font-face {
  font-family: "hticon-vis"; /* Project id 4936170 */
  src: url('iconfont.woff2?t=1753429486571') format('woff2'),
       url('iconfont.woff?t=1753429486571') format('woff'),
       url('iconfont.ttf?t=1753429486571') format('truetype');
}

[class*='hticon-vis'] {
  font-family: "hticon-vis" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hticon-vis-border-no:before {
  content: "\e680";
}

.hticon-vis-radius-tb:before {
  content: "\e681";
}

.hticon-vis-radius-lr:before {
  content: "\e682";
}

.hticon-vis-prefix:before {
  content: "\e683";
}

.hticon-vis-suffix:before {
  content: "\e67f";
}

.hticon-vis-field-integer:before {
  content: "\e61d";
}

.hticon-vis-asc:before {
  content: "\e637";
}

.hticon-vis-desc:before {
  content: "\e674";
}

.hticon-vis-field-date:before {
  content: "\e67b";
}

.hticon-vis-field-string:before {
  content: "\e67c";
}

.hticon-vis-text-left:before {
  content: "\e677";
}

.hticon-vis-text-justify:before {
  content: "\e678";
}

.hticon-vis-text-right:before {
  content: "\e679";
}

.hticon-vis-text-center:before {
  content: "\e67a";
}

.hticon-vis-format_underline:before {
  content: "\e66f";
}

.hticon-vis-auto:before {
  content: "\e670";
}

.hticon-vis-vertical_b:before {
  content: "\e671";
}

.hticon-vis-ellipsis:before {
  content: "\e672";
}

.hticon-vis-line-width:before {
  content: "\e673";
}

.hticon-vis-format_through:before {
  content: "\e675";
}

.hticon-vis-fixed:before {
  content: "\e676";
}

.hticon-vis-vertical_t:before {
  content: "\e606";
}

.hticon-vis-format_italic:before {
  content: "\e66a";
}

.hticon-vis-single:before {
  content: "\e66b";
}

.hticon-vis-data:before {
  content: "\e66e";
}

.hticon-vis-horizontal-center:before {
  content: "\e666";
}

.hticon-vis-align-left:before {
  content: "\e667";
}

.hticon-vis-align-top:before {
  content: "\e668";
}

.hticon-vis-align-right:before {
  content: "\e669";
}

.hticon-vis-align-bottom:before {
  content: "\e66c";
}

.hticon-vis-delete-height:before {
  content: "\e66d";
}

.hticon-vis-vertical-center:before {
  content: "\e65e";
}

.hticon-vis-delete-width:before {
  content: "\e65f";
}

.hticon-vis-position:before {
  content: "\e660";
}

.hticon-vis-padding-round:before {
  content: "\e661";
}

.hticon-vis-delete:before {
  content: "\e662";
}

.hticon-vis-clip:before {
  content: "\e663";
}

.hticon-vis-resize-fill-x:before {
  content: "\e664";
}

.hticon-vis-resize-fill-y:before {
  content: "\e665";
}

.hticon-vis-grid-col:before {
  content: "\e65d";
}

.hticon-vis-vertical_c:before {
  content: "\e6da";
}

.hticon-vis-min-height:before {
  content: "\e644";
}

.hticon-vis-resize-fixed-y:before {
  content: "\e645";
}

.hticon-vis-padding-y:before {
  content: "\e650";
}

.hticon-vis-resize-adapt-y:before {
  content: "\e651";
}

.hticon-vis-padding-x:before {
  content: "\e652";
}

.hticon-vis-max-width:before {
  content: "\e653";
}

.hticon-vis-resizing-w:before {
  content: "\e654";
}

.hticon-vis-auto-layout-add:before {
  content: "\e655";
}

.hticon-vis-resizing-h:before {
  content: "\e656";
}

.hticon-vis-resize-adapt-x:before {
  content: "\e657";
}

.hticon-vis-layout-warp:before {
  content: "\e658";
}

.hticon-vis-ziju:before {
  content: "\e659";
}

.hticon-vis-grid-row:before {
  content: "\e65a";
}

.hticon-vis-resize-fixed-x:before {
  content: "\e65b";
}

.hticon-vis-min-width:before {
  content: "\e65c";
}

.hticon-vis-max-height:before {
  content: "\e641";
}

.hticon-vis-letter-y:before {
  content: "\e601";
}

.hticon-vis-letter-x:before {
  content: "\e64f";
}

.hticon-vis-gap-horizontal:before {
  content: "\e602";
}

.hticon-vis-layout-horizontal:before {
  content: "\e646";
}

.hticon-vis-padding-bottom:before {
  content: "\e647";
}

.hticon-vis-layout-freeform:before {
  content: "\e648";
}

.hticon-vis-gap-vertical:before {
  content: "\e649";
}

.hticon-vis-padding-right:before {
  content: "\e64a";
}

.hticon-vis-padding-left:before {
  content: "\e64b";
}

.hticon-vis-layout-vertical:before {
  content: "\e64c";
}

.hticon-vis-layout-grid:before {
  content: "\e64d";
}

.hticon-vis-padding-top:before {
  content: "\e64e";
}

.hticon-vis-auto-layout:before {
  content: "\e604";
}

.hticon-vis-letter-h:before {
  content: "\e642";
}

.hticon-vis-rotate:before {
  content: "\e643";
}

.hticon-vis-control:before {
  content: "\e62d";
}

.hticon-vis-xuxian:before {
  content: "\e62e";
}

.hticon-vis-flip-v:before {
  content: "\e630";
}

.hticon-vis-switch-chart:before {
  content: "\e631";
}

.hticon-vis-letter-w:before {
  content: "\e632";
}

.hticon-vis-remove:before {
  content: "\e633";
}

.hticon-vis-play:before {
  content: "\e634";
}

.hticon-vis-radius:before {
  content: "\e635";
}

.hticon-vis-radius-rb:before {
  content: "\e636";
}

.hticon-vis-flip-h:before {
  content: "\e638";
}

.hticon-vis-radius-s:before {
  content: "\e639";
}

.hticon-vis-radius-rt:before {
  content: "\e63a";
}

.hticon-vis-radius-lb:before {
  content: "\e63b";
}

.hticon-vis-opacity:before {
  content: "\e63c";
}

.hticon-vis-radius-lt:before {
  content: "\e63d";
}

.hticon-vis-theme:before {
  content: "\e63e";
}

.hticon-vis-icon-style:before {
  content: "\e63f";
}

.hticon-vis-associate:before {
  content: "\e640";
}

.hticon-vis-property:before {
  content: "\e608";
}

.hticon-vis-blur:before {
  content: "\e60c";
}

.hticon-vis-rotate-90:before {
  content: "\e617";
}

.hticon-vis-shixian:before {
  content: "\e623";
}

.hticon-vis-spread:before {
  content: "\e62c";
}

.hticon-vis-eyedropper:before {
  content: "\e618";
}

.hticon-vis-solid:before {
  content: "\e62f";
}

.hticon-vis-navigation:before {
  content: "\e600";
}

.hticon-vis-chat-l:before {
  content: "\e603";
}

.hticon-vis-chat-r:before {
  content: "\e605";
}

.hticon-vis-chart:before {
  content: "\e607";
}

.hticon-vis-eye-o:before {
  content: "\e609";
}

.hticon-vis-eye-c:before {
  content: "\e60a";
}

.hticon-vis-switch:before {
  content: "\e60b";
}

.hticon-vis-capsule-d:before {
  content: "\e60d";
}

.hticon-vis-arrow-d:before {
  content: "\e60e";
}

.hticon-vis-frame:before {
  content: "\e60f";
}

.hticon-vis-h-arrow-l:before {
  content: "\e610";
}

.hticon-vis-mode-s:before {
  content: "\e611";
}

.hticon-vis-group-a:before {
  content: "\e612";
}

.hticon-vis-add:before {
  content: "\e613";
}

.hticon-vis-navigation-t:before {
  content: "\e614";
}

.hticon-vis-group:before {
  content: "\e615";
}

.hticon-vis-navigation-pic:before {
  content: "\e616";
}

.hticon-vis-canvas-f:before {
  content: "\e619";
}

.hticon-vis-arrow-r:before {
  content: "\e61a";
}

.hticon-vis-subtract:before {
  content: "\e61b";
}

.hticon-vis-h-arrow-r:before {
  content: "\e61c";
}

.hticon-vis-mode-d:before {
  content: "\e61e";
}

.hticon-vis-c-chat-l:before {
  content: "\e61f";
}

.hticon-vis-navigation-c:before {
  content: "\e620";
}

.hticon-vis-textbox:before {
  content: "\e621";
}

.hticon-vis-h-arrow-d:before {
  content: "\e622";
}

.hticon-vis-group-o:before {
  content: "\e624";
}

.hticon-vis-canvas-s:before {
  content: "\e625";
}

.hticon-vis-capsule-t:before {
  content: "\e626";
}

.hticon-vis-canvas-c:before {
  content: "\e627";
}

.hticon-vis-c-chat-r:before {
  content: "\e628";
}

.hticon-vis-mode-d-a:before {
  content: "\e629";
}

.hticon-vis-h-arrow-t:before {
  content: "\e62a";
}

.hticon-vis-navigation-d:before {
  content: "\e62b";
}

